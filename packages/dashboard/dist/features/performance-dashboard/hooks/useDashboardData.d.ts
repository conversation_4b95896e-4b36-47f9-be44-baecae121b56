/**
 * Dashboard Data Hook
 *
 * Custom hook for fetching and managing dashboard data
 */
interface Metric {
    title: string;
    value: string;
}
interface ChartDataPoint {
    date: string;
    value: number;
}
export declare const useDashboardData: () => {
    metrics: Metric[];
    chartData: ChartDataPoint[];
    recentTrades: CompleteTradeData[];
    isLoading: boolean;
    error: string;
    fetchDashboardData: () => Promise<void>;
};
export {};
//# sourceMappingURL=useDashboardData.d.ts.map