/**
 * ICT Action Plan Component
 *
 * Replaces generic Trading Plan with sophisticated ICT-specific action items
 * based on real trading performance data and model analysis.
 */
import React from 'react';
export interface ICTActionPlanProps {
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * ICT Action Plan Component
 */
export declare const ICTActionPlan: React.FC<ICTActionPlanProps>;
//# sourceMappingURL=ICTActionPlan.d.ts.map