/**
 * Elite ICT Intelligence Component
 *
 * Comprehensive trading intelligence system integrating:
 * - Intelligent Model Selection Engine
 * - Advanced Pattern Quality Scoring
 * - Granular Session Intelligence
 * - Predictive Intelligence Layer
 */
import React from 'react';
export interface EliteICTIntelligenceProps {
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * Elite ICT Intelligence Component
 */
export declare const EliteICTIntelligence: React.FC<EliteICTIntelligenceProps>;
//# sourceMappingURL=EliteICTIntelligence.d.ts.map