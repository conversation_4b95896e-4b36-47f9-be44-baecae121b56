import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayAnalytics } from '../hooks/usePDArrayAnalytics';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const Section = styled.div `
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
`;
const SectionTitle = styled.h3 `
  color: #dc2626;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const WeeklyComparisonHeader = styled.div `
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
`;
const WeeklyStats = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 12px;
`;
const StatCard = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;
const StatValue = styled.div `
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
`;
const StatLabel = styled.div `
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ActionItemsList = styled.div `
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
const ActionItemCard = styled.div `
  background: ${({ priority }) => {
    switch (priority) {
        case 'high': return 'linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.05))';
        case 'medium': return 'linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.05))';
        case 'low': return 'linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.05))';
    }
}};
  border: 1px solid ${({ priority }) => {
    switch (priority) {
        case 'high': return 'rgba(220, 38, 38, 0.3)';
        case 'medium': return 'rgba(245, 158, 11, 0.3)';
        case 'low': return 'rgba(107, 114, 128, 0.3)';
    }
}};
  border-radius: 8px;
  padding: 16px;
  position: relative;
`;
const ActionItemHeader = styled.div `
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 8px;
`;
const ActionItemDescription = styled.div `
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  flex: 1;
`;
const PriorityBadge = styled.div `
  background: ${({ priority }) => {
    switch (priority) {
        case 'high': return 'linear-gradient(135deg, #dc2626, #ef4444)';
        case 'medium': return 'linear-gradient(135deg, #f59e0b, #fbbf24)';
        case 'low': return 'linear-gradient(135deg, #6b7280, #9ca3af)';
    }
}};
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 12px;
`;
const ConfidenceMeter = styled.div `
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: ${({ confidence }) => confidence}%;
  background: linear-gradient(90deg, #dc2626, #ef4444);
  border-radius: 0 0 8px 8px;
`;
const ElementGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;
const ElementCard = styled.div `
  background: ${({ performance }) => {
    switch (performance) {
        case 'excellent': return 'linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05))';
        case 'good': return 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05))';
        case 'average': return 'linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(75, 85, 99, 0.05))';
        case 'poor': return 'linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05))';
    }
}};
  border: 1px solid ${({ performance }) => {
    switch (performance) {
        case 'excellent': return 'rgba(16, 185, 129, 0.3)';
        case 'good': return 'rgba(59, 130, 246, 0.3)';
        case 'average': return 'rgba(107, 114, 128, 0.3)';
        case 'poor': return 'rgba(239, 68, 68, 0.3)';
    }
}};
  border-radius: 8px;
  padding: 16px;
`;
const ElementName = styled.div `
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
`;
const ElementStats = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
const ElementWinRate = styled.div `
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
`;
const ElementTrades = styled.div `
  color: #9ca3af;
  font-size: 12px;
`;
const getElementPerformanceLevel = (winRate, totalTrades) => {
    if (totalTrades < 3)
        return 'average';
    if (winRate >= 70)
        return 'excellent';
    if (winRate >= 60)
        return 'good';
    if (winRate >= 45)
        return 'average';
    return 'poor';
};
/**
 * Dynamic Trading Plan Component
 */
export const DynamicTradingPlan = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { analytics, isLoading: analyticsLoading, error: analyticsError } = usePDArrayAnalytics();
    const loading = isLoading || analyticsLoading;
    const displayError = error || analyticsError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Dynamic Trading Plan", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Analyzing your PD Array performance..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Dynamic Trading Plan", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    const { weeklyComparison, dynamicActionItems, elementPerformance, combinationPerformance, totalAnalyzedTrades } = analytics;
    return (_jsx(Card, { title: "\uD83C\uDFAF Dynamic Trading Plan", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsxs(WeeklyComparisonHeader, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA This Week's Performance" }), _jsxs(WeeklyStats, { children: [_jsxs(StatCard, { children: [_jsx(StatValue, { children: weeklyComparison.currentWeek.totalTrades }), _jsx(StatLabel, { children: "PD Array Trades" })] }), _jsxs(StatCard, { children: [_jsxs(StatValue, { children: [weeklyComparison.currentWeek.winRate.toFixed(1), "%"] }), _jsx(StatLabel, { children: "Win Rate" })] }), _jsxs(StatCard, { children: [_jsx(StatValue, { children: totalAnalyzedTrades }), _jsx(StatLabel, { children: "Total Analyzed" })] })] })] }), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83C\uDFAF Performance-Driven Action Items" }), _jsx(ActionItemsList, { children: dynamicActionItems.map((item) => (_jsxs(ActionItemCard, { priority: item.priority, confidence: item.confidence, children: [_jsxs(ActionItemHeader, { children: [_jsx(ActionItemDescription, { children: item.description }), _jsx(PriorityBadge, { priority: item.priority, children: item.priority })] }), _jsx(ConfidenceMeter, { confidence: item.confidence })] }, item.id))) })] }), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCC8 PD Array Element Performance" }), _jsx(ElementGrid, { children: elementPerformance.filter(e => e.totalTrades > 0).map((element) => (_jsxs(ElementCard, { performance: getElementPerformanceLevel(element.winRate, element.totalTrades), children: [_jsx(ElementName, { children: element.element }), _jsxs(ElementStats, { children: [_jsxs(ElementWinRate, { children: [element.winRate.toFixed(1), "% Win Rate"] }), _jsxs(ElementTrades, { children: [element.totalTrades, " trades"] })] })] }, element.element))) }), elementPerformance.filter(e => e.totalTrades > 0).length === 0 && (_jsx("div", { style: { textAlign: 'center', color: '#9ca3af', padding: '20px' }, children: "Start logging PD Array elements in your trade notes to see performance analysis" }))] }), combinationPerformance.filter(c => c.totalTrades > 0).length > 0 && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83D\uDD25 Best Element Combinations" }), _jsx(ElementGrid, { children: combinationPerformance
                                .filter(c => c.totalTrades > 0)
                                .sort((a, b) => b.winRate - a.winRate)
                                .slice(0, 4)
                                .map((combo) => (_jsxs(ElementCard, { performance: combo.effectiveness === 'excellent' ? 'excellent' :
                                    combo.effectiveness === 'good' ? 'good' :
                                        combo.effectiveness === 'average' ? 'average' : 'poor', children: [_jsx(ElementName, { children: combo.combination }), _jsxs(ElementStats, { children: [_jsxs(ElementWinRate, { children: [combo.winRate.toFixed(1), "% Win Rate"] }), _jsxs(ElementTrades, { children: [combo.totalTrades, " trades"] })] })] }, combo.combination))) })] }))] }) }));
};
//# sourceMappingURL=DynamicTradingPlan.js.map