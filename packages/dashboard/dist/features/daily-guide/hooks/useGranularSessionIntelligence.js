/**
 * Granular Session Intelligence Hook
 *
 * Detailed session breakdown with optimal entry timing:
 * - 15-30 minute performance windows
 * - Real-time session alerts and guidance
 * - Session transition intelligence
 * - Optimal window recommendations
 */
import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
/**
 * Session window definitions
 */
const SESSION_WINDOWS = {
    NY_OPEN: [
        { start: '09:30', end: '09:45', label: 'Market Open', description: 'Initial volatility assessment' },
        { start: '09:45', end: '10:15', label: 'OPTIMAL WINDOW', description: 'Primary opportunity window' },
        { start: '10:15', end: '10:45', label: 'Secondary Window', description: 'Continuation opportunities' },
        { start: '10:45', end: '11:00', label: 'Session Wind-down', description: 'Reduced activity period' }
    ],
    LUNCH_MACRO: [
        { start: '11:50', end: '12:10', label: 'PRIMARY WINDOW', description: 'Highest win rate period' },
        { start: '12:10', end: '12:30', label: 'Continuation Window', description: 'Follow-through opportunities' },
        { start: '12:30', end: '13:00', label: 'Midday Consolidation', description: 'Range-bound analysis' },
        { start: '13:00', end: '13:30', label: 'Afternoon Transition', description: 'Preparation for next session' }
    ],
    MOC: [
        { start: '15:30', end: '15:45', label: 'Pre-Close Setup', description: 'End-of-day positioning' },
        { start: '15:45', end: '16:00', label: 'CLOSE WINDOW', description: 'Final momentum plays' }
    ],
    PRE_MARKET: [
        { start: '08:00', end: '08:30', label: 'Early Pre-Market', description: 'News reaction analysis' },
        { start: '08:30', end: '09:00', label: 'Pre-Market Prime', description: 'Setup development' },
        { start: '09:00', end: '09:30', label: 'Market Prep', description: 'Final positioning' }
    ]
};
/**
 * Parse time string to minutes since midnight
 */
const timeToMinutes = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
};
/**
 * Get current time in HH:MM format
 */
const getCurrentTime = () => {
    const now = new Date();
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
};
/**
 * Check if current time is within a window
 */
const isTimeInWindow = (currentTime, window) => {
    const current = timeToMinutes(currentTime);
    const start = timeToMinutes(window.start);
    const end = timeToMinutes(window.end);
    return current >= start && current <= end;
};
/**
 * Calculate time remaining in window
 */
const getTimeRemaining = (currentTime, window) => {
    const current = timeToMinutes(currentTime);
    const end = timeToMinutes(window.end);
    return Math.max(0, end - current);
};
/**
 * Extract session from trade data
 */
const extractSession = (trade) => {
    const session = trade.trade.session;
    if (session)
        return session;
    // Try to extract from entry time
    const entryTime = trade.trade.entry_time;
    if (!entryTime)
        return null;
    const timeMinutes = timeToMinutes(entryTime);
    // Map time to session
    if (timeMinutes >= timeToMinutes('09:30') && timeMinutes <= timeToMinutes('11:00')) {
        return 'NY Open';
    }
    else if (timeMinutes >= timeToMinutes('11:50') && timeMinutes <= timeToMinutes('13:30')) {
        return 'Lunch Macro';
    }
    else if (timeMinutes >= timeToMinutes('15:30') && timeMinutes <= timeToMinutes('16:00')) {
        return 'MOC';
    }
    else if (timeMinutes >= timeToMinutes('08:00') && timeMinutes <= timeToMinutes('09:30')) {
        return 'Pre-Market';
    }
    return null;
};
/**
 * Analyze session performance
 */
const analyzeSession = (sessionType, sessionName, trades) => {
    const windows = SESSION_WINDOWS[sessionType];
    const sessionTrades = trades.filter(trade => {
        const session = extractSession(trade);
        return session === sessionName;
    });
    // Analyze each window
    const windowAnalysis = windows.map(window => {
        const windowTrades = sessionTrades.filter(trade => {
            const entryTime = trade.trade.entry_time;
            if (!entryTime)
                return false;
            return isTimeInWindow(entryTime, window);
        });
        const totalTrades = windowTrades.length;
        const winningTrades = windowTrades.filter(t => t.trade.win_loss === 'Win').length;
        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
        const rMultiples = windowTrades
            .map(t => t.trade.r_multiple)
            .filter((r) => r !== undefined && r !== null);
        const avgRMultiple = rMultiples.length > 0 ?
            rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
        const totalPnL = windowTrades
            .map(t => t.trade.achieved_pl || 0)
            .reduce((sum, pl) => sum + pl, 0);
        // Determine model preference
        const rdContTrades = windowTrades.filter(t => t.trade.model_type === 'RD-Cont');
        const fvgRdTrades = windowTrades.filter(t => t.trade.model_type === 'FVG-RD');
        const rdContWinRate = rdContTrades.length > 0 ?
            (rdContTrades.filter(t => t.trade.win_loss === 'Win').length / rdContTrades.length) * 100 : 0;
        const fvgRdWinRate = fvgRdTrades.length > 0 ?
            (fvgRdTrades.filter(t => t.trade.win_loss === 'Win').length / fvgRdTrades.length) * 100 : 0;
        let modelPreference = null;
        if (rdContTrades.length >= 2 && fvgRdTrades.length >= 2) {
            modelPreference = rdContWinRate > fvgRdWinRate ? 'RD-Cont' : 'FVG-RD';
        }
        else if (rdContTrades.length >= 3) {
            modelPreference = 'RD-Cont';
        }
        else if (fvgRdTrades.length >= 3) {
            modelPreference = 'FVG-RD';
        }
        // Determine if this is an optimal window
        const isOptimal = winRate >= 70 && totalTrades >= 3;
        const isPrimary = window.label.includes('OPTIMAL') || window.label.includes('PRIMARY');
        // Generate recommendation
        let recommendation = '';
        if (isOptimal) {
            recommendation = `PRIORITIZE - ${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)} avg R`;
        }
        else if (winRate >= 60 && totalTrades >= 2) {
            recommendation = `GOOD OPPORTUNITY - ${winRate.toFixed(0)}% win rate`;
        }
        else if (totalTrades < 2) {
            recommendation = 'INSUFFICIENT DATA - Monitor for opportunities';
        }
        else {
            recommendation = `CAUTION - ${winRate.toFixed(0)}% win rate, consider reduced size`;
        }
        return {
            window,
            performance: {
                totalTrades,
                winningTrades,
                winRate,
                avgRMultiple,
                totalPnL
            },
            modelPreference,
            isOptimal,
            isPrimary,
            recommendation
        };
    });
    // Calculate overall session performance
    const totalTrades = sessionTrades.length;
    const winningTrades = sessionTrades.filter(t => t.trade.win_loss === 'Win').length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const rMultiples = sessionTrades
        .map(t => t.trade.r_multiple)
        .filter((r) => r !== undefined && r !== null);
    const avgRMultiple = rMultiples.length > 0 ?
        rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
    // Find best and worst windows
    const windowsWithData = windowAnalysis.filter(w => w.performance.totalTrades > 0);
    const bestWindow = windowsWithData.length > 0 ?
        windowsWithData.reduce((best, current) => current.performance.winRate > best.performance.winRate ? current : best).window : null;
    const worstWindow = windowsWithData.length > 0 ?
        windowsWithData.reduce((worst, current) => current.performance.winRate < worst.performance.winRate ? current : worst).window : null;
    // Current status
    const currentTime = getCurrentTime();
    const currentWindow = windows.find(w => isTimeInWindow(currentTime, w)) || null;
    const timeRemaining = currentWindow ? getTimeRemaining(currentTime, currentWindow) : 0;
    // Find next window
    const currentMinutes = timeToMinutes(currentTime);
    const nextWindow = windows.find(w => timeToMinutes(w.start) > currentMinutes) || null;
    const isActive = currentWindow !== null;
    let recommendation = '';
    if (isActive && currentWindow) {
        const currentWindowAnalysis = windowAnalysis.find(w => w.window === currentWindow);
        recommendation = currentWindowAnalysis?.recommendation || 'Monitor for opportunities';
    }
    else if (nextWindow) {
        const timeToNext = timeToMinutes(nextWindow.start) - currentMinutes;
        recommendation = `Next window: ${nextWindow.label} in ${timeToNext} minutes`;
    }
    else {
        recommendation = 'Session not active';
    }
    return {
        sessionName,
        sessionType,
        windows: windowAnalysis,
        overallPerformance: {
            totalTrades,
            winRate,
            avgRMultiple,
            bestWindow,
            worstWindow
        },
        currentStatus: {
            isActive,
            currentWindow,
            timeRemaining,
            nextWindow,
            recommendation
        }
    };
};
/**
 * Granular Session Intelligence Hook
 */
export const useGranularSessionIntelligence = () => {
    const [trades, setTrades] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch trade data
    useEffect(() => {
        const fetchTrades = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const tradeData = await tradeStorageService.getAllTrades();
                setTrades(tradeData);
            }
            catch (err) {
                console.error('Error fetching trades for session intelligence:', err);
                setError('Failed to load trade data');
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTrades();
    }, []);
    // Analyze all sessions
    const sessionAnalyses = useMemo(() => {
        return [
            analyzeSession('NY_OPEN', 'NY Open', trades),
            analyzeSession('LUNCH_MACRO', 'Lunch Macro', trades),
            analyzeSession('MOC', 'MOC', trades),
            analyzeSession('PRE_MARKET', 'Pre-Market', trades)
        ];
    }, [trades]);
    // Generate live session guidance
    const liveGuidance = useMemo(() => {
        const currentTime = getCurrentTime();
        const activeSession = sessionAnalyses.find(s => s.currentStatus.isActive) || null;
        // Find next session
        const currentMinutes = timeToMinutes(currentTime);
        const nextSession = sessionAnalyses
            .filter(s => !s.currentStatus.isActive)
            .find(s => {
            const firstWindow = s.windows[0];
            return timeToMinutes(firstWindow.window.start) > currentMinutes;
        }) || null;
        // Find time to next optimal window
        let timeToNextOptimal = 0;
        let urgencyLevel = 'LOW';
        if (activeSession) {
            const optimalWindows = activeSession.windows.filter(w => w.isOptimal || w.isPrimary);
            const currentWindow = activeSession.currentStatus.currentWindow;
            if (currentWindow && optimalWindows.some(w => w.window === currentWindow)) {
                timeToNextOptimal = activeSession.currentStatus.timeRemaining;
                urgencyLevel = timeToNextOptimal <= 15 ? 'HIGH' : 'MEDIUM';
            }
        }
        else if (nextSession) {
            const nextOptimal = nextSession.windows.find(w => w.isOptimal || w.isPrimary);
            if (nextOptimal) {
                timeToNextOptimal = timeToMinutes(nextOptimal.window.start) - currentMinutes;
                urgencyLevel = timeToNextOptimal <= 30 ? 'MEDIUM' : 'LOW';
            }
        }
        // Generate current recommendation
        let currentRecommendation = '';
        if (activeSession) {
            currentRecommendation = activeSession.currentStatus.recommendation;
        }
        else if (nextSession && timeToNextOptimal <= 60) {
            currentRecommendation = `Prepare for ${nextSession.sessionName} in ${timeToNextOptimal} minutes`;
        }
        else {
            currentRecommendation = 'No active trading sessions - Monitor for setups';
        }
        return {
            currentTime,
            activeSession,
            nextSession,
            timeToNextOptimal,
            currentRecommendation,
            urgencyLevel
        };
    }, [sessionAnalyses]);
    return {
        sessionAnalyses,
        liveGuidance,
        isLoading,
        error,
        refresh: () => {
            setTrades([]);
        }
    };
};
//# sourceMappingURL=useGranularSessionIntelligence.js.map