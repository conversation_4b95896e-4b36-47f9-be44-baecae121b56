"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const b=require("react"),s=require("styled-components"),vo=require("react-dom");var hr=(e=>(e.LONG="LONG",e.SHORT="SHORT",e))(hr||{}),xr=(e=>(e.OPEN="OPEN",e.CLOSED="CLOSED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e.PENDING="PENDING",e))(xr||{}),br=(e=>(e.MARKET="MARKET",e.LIMIT="LIMIT",e.STOP="STOP",e.STOP_LIMIT="STOP_LIMIT",e))(br||{}),yr=(e=>(e.BUY="BUY",e.SELL="SELL",e))(yr||{}),vr=(e=>(e.PENDING="PENDING",e.FILLED="FILLED",e.PARTIALLY_FILLED="PARTIALLY_FILLED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e))(vr||{}),Sr=(e=>(e.GTC="GTC",e.IOC="IOC",e.FOK="FOK",e.DAY="DAY",e))(Sr||{}),G=(e=>(e.LONDON="london",e.NEW_YORK_AM="new-york-am",e.NEW_YORK_PM="new-york-pm",e.ASIA="asia",e.PRE_MARKET="pre-market",e.AFTER_HOURS="after-hours",e.OVERNIGHT="overnight",e))(G||{}),M=(e=>(e.MORNING_BREAKOUT="morning-breakout",e.MID_MORNING_REVERSION="mid-morning-reversion",e.PRE_LUNCH="pre-lunch",e.LUNCH_MACRO_EXTENDED="lunch-macro-extended",e.LUNCH_MACRO="lunch-macro",e.POST_LUNCH="post-lunch",e.PRE_CLOSE="pre-close",e.POWER_HOUR="power-hour",e.MOC="moc",e.LONDON_OPEN="london-open",e.LONDON_NY_OVERLAP="london-ny-overlap",e.CUSTOM="custom",e))(M||{});const Ce={constant:{parentArrays:["NWOG","Old-NWOG","NDOG","Old-NDOG","Monthly-FVG","Weekly-FVG","Daily-FVG","15min-Top/Bottom-FVG","1h-Top/Bottom-FVG"],fvgTypes:["Strong-FVG","AM-FPFVG","PM-FPFVG","Asia-FPFVG","Premarket-FPFVG","MNOR-FVG","Macro-FVG","News-FVG","Top/Bottom-FVG"]},action:{liquidityEvents:["None","London-H/L","Premarket-H/L","09:30-Opening-Range-H/L","Lunch-H/L","Prev-Day-H/L","Prev-Week-H/L","Monthly-H/L","Macro-H/L"]},variable:{rdTypes:["None","True-RD","IMM-RD","Dispersed-RD","Wide-Gap-RD"]},entry:{methods:["Simple-Entry","Complex-Entry","Complex-Entry/Mini"]}},So=["RD-Cont","FVG-RD","Combined"];var o={},wo={get exports(){return o},set exports(e){o=e}},Re={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ht;function Co(){if(Ht)return Re;Ht=1;var e=b,t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(p,f,l){var d,m={},h=null,y=null;l!==void 0&&(h=""+l),f.key!==void 0&&(h=""+f.key),f.ref!==void 0&&(y=f.ref);for(d in f)n.call(f,d)&&!c.hasOwnProperty(d)&&(m[d]=f[d]);if(p&&p.defaultProps)for(d in f=p.defaultProps,f)m[d]===void 0&&(m[d]=f[d]);return{$$typeof:t,type:p,key:h,ref:y,props:m,_owner:i.current}}return Re.Fragment=r,Re.jsx=a,Re.jsxs=a,Re}var _e={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ut;function To(){return Ut||(Ut=1,process.env.NODE_ENV!=="production"&&function(){var e=b,t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),p=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),y=Symbol.for("react.offscreen"),g=Symbol.iterator,x="@@iterator";function C(u){if(u===null||typeof u!="object")return null;var v=g&&u[g]||u[x];return typeof v=="function"?v:null}var S=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function w(u){{for(var v=arguments.length,T=new Array(v>1?v-1:0),P=1;P<v;P++)T[P-1]=arguments[P];_("error",u,T)}}function _(u,v,T){{var P=S.ReactDebugCurrentFrame,q=P.getStackAddendum();q!==""&&(v+="%s",T=T.concat([q]));var V=T.map(function(A){return String(A)});V.unshift("Warning: "+v),Function.prototype.apply.call(console[u],console,V)}}var z=!1,$=!1,L=!1,j=!1,R=!1,N;N=Symbol.for("react.module.reference");function U(u){return!!(typeof u=="string"||typeof u=="function"||u===n||u===c||R||u===i||u===l||u===d||j||u===y||z||$||L||typeof u=="object"&&u!==null&&(u.$$typeof===h||u.$$typeof===m||u.$$typeof===a||u.$$typeof===p||u.$$typeof===f||u.$$typeof===N||u.getModuleId!==void 0))}function Y(u,v,T){var P=u.displayName;if(P)return P;var q=v.displayName||v.name||"";return q!==""?T+"("+q+")":T}function k(u){return u.displayName||"Context"}function H(u){if(u==null)return null;if(typeof u.tag=="number"&&w("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof u=="function")return u.displayName||u.name||null;if(typeof u=="string")return u;switch(u){case n:return"Fragment";case r:return"Portal";case c:return"Profiler";case i:return"StrictMode";case l:return"Suspense";case d:return"SuspenseList"}if(typeof u=="object")switch(u.$$typeof){case p:var v=u;return k(v)+".Consumer";case a:var T=u;return k(T._context)+".Provider";case f:return Y(u,u.render,"ForwardRef");case m:var P=u.displayName||null;return P!==null?P:H(u.type)||"Memo";case h:{var q=u,V=q._payload,A=q._init;try{return H(A(V))}catch{return null}}}return null}var ee=Object.assign,re=0,de,oe,D,X,xe,B,pe;function Nt(){}Nt.__reactDisabledLog=!0;function Gr(){{if(re===0){de=console.log,oe=console.info,D=console.warn,X=console.error,xe=console.group,B=console.groupCollapsed,pe=console.groupEnd;var u={configurable:!0,enumerable:!0,value:Nt,writable:!0};Object.defineProperties(console,{info:u,log:u,warn:u,error:u,group:u,groupCollapsed:u,groupEnd:u})}re++}}function Kr(){{if(re--,re===0){var u={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:ee({},u,{value:de}),info:ee({},u,{value:oe}),warn:ee({},u,{value:D}),error:ee({},u,{value:X}),group:ee({},u,{value:xe}),groupCollapsed:ee({},u,{value:B}),groupEnd:ee({},u,{value:pe})})}re<0&&w("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var nt=S.ReactCurrentDispatcher,st;function Fe(u,v,T){{if(st===void 0)try{throw Error()}catch(q){var P=q.stack.trim().match(/\n( *(at )?)/);st=P&&P[1]||""}return`
`+st+u}}var it=!1,qe;{var Qr=typeof WeakMap=="function"?WeakMap:Map;qe=new Qr}function kt(u,v){if(!u||it)return"";{var T=qe.get(u);if(T!==void 0)return T}var P;it=!0;var q=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var V;V=nt.current,nt.current=null,Gr();try{if(v){var A=function(){throw Error()};if(Object.defineProperty(A.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(A,[])}catch(fe){P=fe}Reflect.construct(u,[],A)}else{try{A.call()}catch(fe){P=fe}u.call(A.prototype)}}else{try{throw Error()}catch(fe){P=fe}u()}}catch(fe){if(fe&&P&&typeof fe.stack=="string"){for(var O=fe.stack.split(`
`),ne=P.stack.split(`
`),J=O.length-1,Z=ne.length-1;J>=1&&Z>=0&&O[J]!==ne[Z];)Z--;for(;J>=1&&Z>=0;J--,Z--)if(O[J]!==ne[Z]){if(J!==1||Z!==1)do if(J--,Z--,Z<0||O[J]!==ne[Z]){var ae=`
`+O[J].replace(" at new "," at ");return u.displayName&&ae.includes("<anonymous>")&&(ae=ae.replace("<anonymous>",u.displayName)),typeof u=="function"&&qe.set(u,ae),ae}while(J>=1&&Z>=0);break}}}finally{it=!1,nt.current=V,Kr(),Error.prepareStackTrace=q}var we=u?u.displayName||u.name:"",Bt=we?Fe(we):"";return typeof u=="function"&&qe.set(u,Bt),Bt}function Xr(u,v,T){return kt(u,!1)}function Jr(u){var v=u.prototype;return!!(v&&v.isReactComponent)}function Be(u,v,T){if(u==null)return"";if(typeof u=="function")return kt(u,Jr(u));if(typeof u=="string")return Fe(u);switch(u){case l:return Fe("Suspense");case d:return Fe("SuspenseList")}if(typeof u=="object")switch(u.$$typeof){case f:return Xr(u.render);case m:return Be(u.type,v,T);case h:{var P=u,q=P._payload,V=P._init;try{return Be(V(q),v,T)}catch{}}}return""}var He=Object.prototype.hasOwnProperty,Lt={},Rt=S.ReactDebugCurrentFrame;function Ue(u){if(u){var v=u._owner,T=Be(u.type,u._source,v?v.type:null);Rt.setExtraStackFrame(T)}else Rt.setExtraStackFrame(null)}function Zr(u,v,T,P,q){{var V=Function.call.bind(He);for(var A in u)if(V(u,A)){var O=void 0;try{if(typeof u[A]!="function"){var ne=Error((P||"React class")+": "+T+" type `"+A+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof u[A]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw ne.name="Invariant Violation",ne}O=u[A](v,A,P,T,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(J){O=J}O&&!(O instanceof Error)&&(Ue(q),w("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",P||"React class",T,A,typeof O),Ue(null)),O instanceof Error&&!(O.message in Lt)&&(Lt[O.message]=!0,Ue(q),w("Failed %s type: %s",T,O.message),Ue(null))}}}var eo=Array.isArray;function at(u){return eo(u)}function to(u){{var v=typeof Symbol=="function"&&Symbol.toStringTag,T=v&&u[Symbol.toStringTag]||u.constructor.name||"Object";return T}}function ro(u){try{return _t(u),!1}catch{return!0}}function _t(u){return""+u}function Mt(u){if(ro(u))return w("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",to(u)),_t(u)}var Le=S.ReactCurrentOwner,oo={key:!0,ref:!0,__self:!0,__source:!0},Pt,Dt,ct;ct={};function no(u){if(He.call(u,"ref")){var v=Object.getOwnPropertyDescriptor(u,"ref").get;if(v&&v.isReactWarning)return!1}return u.ref!==void 0}function so(u){if(He.call(u,"key")){var v=Object.getOwnPropertyDescriptor(u,"key").get;if(v&&v.isReactWarning)return!1}return u.key!==void 0}function io(u,v){if(typeof u.ref=="string"&&Le.current&&v&&Le.current.stateNode!==v){var T=H(Le.current.type);ct[T]||(w('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',H(Le.current.type),u.ref),ct[T]=!0)}}function ao(u,v){{var T=function(){Pt||(Pt=!0,w("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};T.isReactWarning=!0,Object.defineProperty(u,"key",{get:T,configurable:!0})}}function co(u,v){{var T=function(){Dt||(Dt=!0,w("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};T.isReactWarning=!0,Object.defineProperty(u,"ref",{get:T,configurable:!0})}}var lo=function(u,v,T,P,q,V,A){var O={$$typeof:t,type:u,key:v,ref:T,props:A,_owner:V};return O._store={},Object.defineProperty(O._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(O,"_self",{configurable:!1,enumerable:!1,writable:!1,value:P}),Object.defineProperty(O,"_source",{configurable:!1,enumerable:!1,writable:!1,value:q}),Object.freeze&&(Object.freeze(O.props),Object.freeze(O)),O};function uo(u,v,T,P,q){{var V,A={},O=null,ne=null;T!==void 0&&(Mt(T),O=""+T),so(v)&&(Mt(v.key),O=""+v.key),no(v)&&(ne=v.ref,io(v,q));for(V in v)He.call(v,V)&&!oo.hasOwnProperty(V)&&(A[V]=v[V]);if(u&&u.defaultProps){var J=u.defaultProps;for(V in J)A[V]===void 0&&(A[V]=J[V])}if(O||ne){var Z=typeof u=="function"?u.displayName||u.name||"Unknown":u;O&&ao(A,Z),ne&&co(A,Z)}return lo(u,O,ne,q,P,Le.current,A)}}var lt=S.ReactCurrentOwner,$t=S.ReactDebugCurrentFrame;function Se(u){if(u){var v=u._owner,T=Be(u.type,u._source,v?v.type:null);$t.setExtraStackFrame(T)}else $t.setExtraStackFrame(null)}var dt;dt=!1;function ut(u){return typeof u=="object"&&u!==null&&u.$$typeof===t}function Ot(){{if(lt.current){var u=H(lt.current.type);if(u)return`

Check the render method of \``+u+"`."}return""}}function po(u){{if(u!==void 0){var v=u.fileName.replace(/^.*[\\\/]/,""),T=u.lineNumber;return`

Check your code at `+v+":"+T+"."}return""}}var zt={};function fo(u){{var v=Ot();if(!v){var T=typeof u=="string"?u:u.displayName||u.name;T&&(v=`

Check the top-level render call using <`+T+">.")}return v}}function At(u,v){{if(!u._store||u._store.validated||u.key!=null)return;u._store.validated=!0;var T=fo(v);if(zt[T])return;zt[T]=!0;var P="";u&&u._owner&&u._owner!==lt.current&&(P=" It was passed a child from "+H(u._owner.type)+"."),Se(u),w('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',T,P),Se(null)}}function Ft(u,v){{if(typeof u!="object")return;if(at(u))for(var T=0;T<u.length;T++){var P=u[T];ut(P)&&At(P,v)}else if(ut(u))u._store&&(u._store.validated=!0);else if(u){var q=C(u);if(typeof q=="function"&&q!==u.entries)for(var V=q.call(u),A;!(A=V.next()).done;)ut(A.value)&&At(A.value,v)}}}function go(u){{var v=u.type;if(v==null||typeof v=="string")return;var T;if(typeof v=="function")T=v.propTypes;else if(typeof v=="object"&&(v.$$typeof===f||v.$$typeof===m))T=v.propTypes;else return;if(T){var P=H(v);Zr(T,u.props,"prop",P,u)}else if(v.PropTypes!==void 0&&!dt){dt=!0;var q=H(v);w("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",q||"Unknown")}typeof v.getDefaultProps=="function"&&!v.getDefaultProps.isReactClassApproved&&w("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function mo(u){{for(var v=Object.keys(u.props),T=0;T<v.length;T++){var P=v[T];if(P!=="children"&&P!=="key"){Se(u),w("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",P),Se(null);break}}u.ref!==null&&(Se(u),w("Invalid attribute `ref` supplied to `React.Fragment`."),Se(null))}}function qt(u,v,T,P,q,V){{var A=U(u);if(!A){var O="";(u===void 0||typeof u=="object"&&u!==null&&Object.keys(u).length===0)&&(O+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var ne=po(q);ne?O+=ne:O+=Ot();var J;u===null?J="null":at(u)?J="array":u!==void 0&&u.$$typeof===t?(J="<"+(H(u.type)||"Unknown")+" />",O=" Did you accidentally export a JSX literal instead of a component?"):J=typeof u,w("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",J,O)}var Z=uo(u,v,T,q,V);if(Z==null)return Z;if(A){var ae=v.children;if(ae!==void 0)if(P)if(at(ae)){for(var we=0;we<ae.length;we++)Ft(ae[we],u);Object.freeze&&Object.freeze(ae)}else w("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Ft(ae,u)}return u===n?mo(Z):go(Z),Z}}function ho(u,v,T){return qt(u,v,T,!0)}function xo(u,v,T){return qt(u,v,T,!1)}var bo=xo,yo=ho;_e.Fragment=n,_e.jsx=bo,_e.jsxs=yo}()),_e}(function(e){process.env.NODE_ENV==="production"?e.exports=Co():e.exports=To()})(wo);const Io={small:s.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:s.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:s.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},Eo=(e,t,r=!1)=>s.css(["",""],({theme:n})=>{let i,c,a;switch(e){case"primary":i=t?n.colors.primary:`${n.colors.primary}20`,c=t?n.colors.textInverse:n.colors.primary,a=n.colors.primary;break;case"secondary":i=t?n.colors.secondary:`${n.colors.secondary}20`,c=t?n.colors.textInverse:n.colors.secondary,a=n.colors.secondary;break;case"success":i=t?n.colors.success:`${n.colors.success}20`,c=t?n.colors.textInverse:n.colors.success,a=n.colors.success;break;case"warning":i=t?n.colors.warning:`${n.colors.warning}20`,c=t?n.colors.textInverse:n.colors.warning,a=n.colors.warning;break;case"error":i=t?n.colors.error:`${n.colors.error}20`,c=t?n.colors.textInverse:n.colors.error,a=n.colors.error;break;case"info":i=t?n.colors.info:`${n.colors.info}20`,c=t?n.colors.textInverse:n.colors.info,a=n.colors.info;break;case"neutral":i=t?n.colors.textSecondary:`${n.colors.textSecondary}10`,c=t?n.colors.textInverse:n.colors.textSecondary,a=n.colors.textSecondary;break;default:i=t?n.colors.textSecondary:`${n.colors.textSecondary}20`,c=t?n.colors.textInverse:n.colors.textSecondary,a=n.colors.textSecondary}return r?`
          background-color: transparent;
          color: ${a};
          border: 1px solid ${a};
        `:`
        background-color: ${i};
        color: ${c};
        border: 1px solid transparent;
      `}),wr=s.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),jo=s(wr).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),No=s(wr).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),ko=s.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:t,dot:r})=>r?"50%":t?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>Io[e],({variant:e,solid:t,outlined:r})=>Eo(e,t,r||!1),({dot:e})=>e&&s.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&s.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),ze=({children:e,variant:t="default",size:r="medium",solid:n=!1,className:i="",style:c,onClick:a,rounded:p=!1,dot:f=!1,counter:l=!1,outlined:d=!1,startIcon:m,endIcon:h,max:y,inline:g=!0})=>{let x=e;return l&&typeof e=="number"&&y!==void 0&&e>y&&(x=`${y}+`),o.jsx(ko,{variant:t,size:r,solid:n,clickable:!!a,className:i,style:c,onClick:a,rounded:p,dot:f,counter:l,outlined:d,inline:g,children:!f&&o.jsxs(o.Fragment,{children:[m&&o.jsx(jo,{children:m}),x,h&&o.jsx(No,{children:h})]})})},Lo=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Ro=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],Lo,({theme:e})=>e.spacing.xs),_o={small:s.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},Mo={primary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:s.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:s.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),success:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},Po=s.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({size:e="medium"})=>_o[e],({variant:e="primary"})=>Mo[e],({fullWidth:e})=>e&&s.css(["width:100%;"]),({$hasStartIcon:e})=>e&&s.css(["& > *:first-child{margin-right:",";}"],({theme:t})=>t.spacing.xs),({$hasEndIcon:e})=>e&&s.css(["& > *:last-child{margin-left:",";}"],({theme:t})=>t.spacing.xs)),Do=s.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),ie=({children:e,variant:t="primary",disabled:r=!1,loading:n=!1,size:i="medium",fullWidth:c=!1,startIcon:a,endIcon:p,onClick:f,className:l="",type:d="button",...m})=>o.jsx(Po,{variant:t,disabled:r||n,size:i,fullWidth:c,onClick:f,className:l,type:d,$hasStartIcon:!!a&&!n,$hasEndIcon:!!p&&!n,...m,children:o.jsxs(Do,{children:[n&&o.jsx(Ro,{}),!n&&a,e,!n&&p]})}),$o=s.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Oo=s.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500}),zo=s.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:t,hasSuccess:r,isFocused:n})=>t?e.colors.error:r?e.colors.success:n?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({disabled:e,theme:t})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],t.colors.background),({isFocused:e,theme:t,hasError:r,hasSuccess:n})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],r?`${t.colors.error}33`:n?`${t.colors.success}33`:`${t.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),Yt=s.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Ao=s.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&s.css(["padding-right:0;"]),({$size:e,theme:t})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.xs,t.spacing.xxs,t.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.md,t.spacing.sm,t.spacing.md):s.css(["font-size:",";padding:"," ",";"],t.fontSizes.sm,t.spacing.xs,t.spacing.sm)),Fo=s.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),qo=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t,hasSuccess:r})=>t?e.colors.error:r?e.colors.success:e.colors.textSecondary),be=({value:e,onChange:t,placeholder:r="",disabled:n=!1,error:i="",type:c="text",name:a="",id:p="",className:f="",required:l=!1,autoComplete:d="",label:m="",helperText:h="",startIcon:y,endIcon:g,loading:x=!1,success:C=!1,clearable:S=!1,onClear:w,maxLength:_,showCharCount:z=!1,size:$="medium",fullWidth:L=!1,...j})=>{const[R,N]=b.useState(!1),U=b.useRef(null),Y=()=>{w?w():t(""),U.current&&U.current.focus()},k=oe=>{N(!0),j.onFocus&&j.onFocus(oe)},H=oe=>{N(!1),j.onBlur&&j.onBlur(oe)},ee=S&&e&&!n,re=(e==null?void 0:e.length)||0,de=z||_!==void 0&&_>0;return o.jsxs($o,{className:f,fullWidth:L,children:[m&&o.jsxs(Oo,{htmlFor:p,children:[m,l&&" *"]}),o.jsxs(zo,{hasError:!!i,hasSuccess:!!C,disabled:!!n,$size:$,hasStartIcon:!!y,hasEndIcon:!!(g||ee),isFocused:!!R,children:[y&&o.jsx(Yt,{children:y}),o.jsx(Ao,{ref:U,type:c,value:e,onChange:oe=>t(oe.target.value),placeholder:r,disabled:!!(n||x),name:a,id:p,required:!!l,autoComplete:d,hasStartIcon:!!y,hasEndIcon:!!(g||ee),$size:$,maxLength:_,onFocus:k,onBlur:H,...j}),ee&&o.jsx(Fo,{type:"button",onClick:Y,tabIndex:-1,children:"✕"}),g&&o.jsx(Yt,{children:g})]}),(i||h||de)&&o.jsxs(qo,{hasError:!!i,hasSuccess:!!C,children:[o.jsx("div",{children:i||h}),de&&o.jsxs("div",{children:[re,_!==void 0&&`/${_}`]})]})]})},Vt={small:s.css(["height:100px;"]),medium:s.css(["height:200px;"]),large:s.css(["height:300px;"]),custom:e=>s.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Bo={default:s.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:s.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:s.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},Ho=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Uo=s.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:t,customWidth:r})=>e==="custom"?Vt.custom({customHeight:t,customWidth:r}):Vt[e],({variant:e})=>Bo[e]),Yo=s.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,Ho,({theme:e})=>e.spacing.sm),Vo=s.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),Cr=({variant:e="default",size:t="medium",height:r="200px",width:n="",text:i="Loading...",showSpinner:c=!0,className:a=""})=>o.jsxs(Uo,{variant:e,size:t,customHeight:r,customWidth:n,className:a,children:[c&&o.jsx(Yo,{}),i&&o.jsx(Vo,{children:i})]}),Wo=s.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Go=s.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500}),Ko=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:t,hasSuccess:r,isFocused:n})=>t?e.colors.error:r?e.colors.success:n?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({disabled:e,theme:t})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],t.colors.background),({isFocused:e,theme:t,hasError:r,hasSuccess:n})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],r?`${t.colors.error}33`:n?`${t.colors.success}33`:`${t.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),Qo=s.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Xo=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({$size:e,theme:t})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.xs,t.spacing.xxs,t.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.md,t.spacing.sm,t.spacing.md):s.css(["font-size:",";padding:"," ",";"],t.fontSizes.sm,t.spacing.xs,t.spacing.sm)),Jo=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t,hasSuccess:r})=>t?e.colors.error:r?e.colors.success:e.colors.textSecondary),Zo=s.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>e.colors.textPrimary),Te=({options:e,value:t,onChange:r,disabled:n=!1,error:i="",name:c="",id:a="",className:p="",required:f=!1,placeholder:l="",label:d="",helperText:m="",size:h="medium",fullWidth:y=!0,loading:g=!1,success:x=!1,startIcon:C,...S})=>{const[w,_]=b.useState(!1),z=N=>{_(!0),S.onFocus&&S.onFocus(N)},$=N=>{_(!1),S.onBlur&&S.onBlur(N)},L={},j=[];e.forEach(N=>{N.group?(L[N.group]||(L[N.group]=[]),L[N.group].push(N)):j.push(N)});const R=Object.keys(L).length>0;return o.jsxs(Wo,{className:p,fullWidth:y,children:[d&&o.jsxs(Go,{htmlFor:a,children:[d,f&&" *"]}),o.jsxs(Ko,{hasError:!!i,hasSuccess:!!x,disabled:!!(n||g),$size:h,hasStartIcon:!!C,isFocused:!!w,children:[C&&o.jsx(Qo,{children:C}),o.jsxs(Xo,{value:t,onChange:N=>r(N.target.value),disabled:!!(n||g),name:c,id:a,required:!!f,hasStartIcon:!!C,$size:h,onFocus:z,onBlur:$,...S,children:[l&&o.jsx("option",{value:"",disabled:!0,children:l}),R?o.jsxs(o.Fragment,{children:[j.map(N=>o.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value)),Object.entries(L).map(([N,U])=>o.jsx(Zo,{label:N,children:U.map(Y=>o.jsx("option",{value:Y.value,disabled:Y.disabled,children:Y.label},Y.value))},N))]}):e.map(N=>o.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value))]})]}),(i||m)&&o.jsx(Jo,{hasError:!!i,hasSuccess:!!x,children:o.jsx("div",{children:i||m})})]})},Wt={small:"8px",medium:"12px",large:"16px"},en={small:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},tn=s.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),rn=s.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),on=s.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>Wt[e],({size:e})=>Wt[e],({status:e,theme:t,pulse:r})=>{let n,i;switch(e){case"success":n=t.colors.success,i="76, 175, 80";break;case"error":n=t.colors.error,i="244, 67, 54";break;case"warning":n=t.colors.warning,i="255, 152, 0";break;case"info":n=t.colors.info,i="33, 150, 243";break;default:n=t.colors.textSecondary,i="158, 158, 158"}return s.css(["background-color:",";",""],n,r&&s.css(["--pulse-color:",";",""],i,tn))}),nn=s.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>en[e],({status:e,theme:t})=>{let r;switch(e){case"success":r=t.colors.success;break;case"error":r=t.colors.error;break;case"warning":r=t.colors.warning;break;case"info":r=t.colors.info;break;default:r=t.colors.textSecondary}return s.css(["color:",";font-weight:",";"],r,t.fontWeights.medium)}),sn=({status:e,size:t="medium",pulse:r=!1,showLabel:n=!1,label:i="",className:c=""})=>{const a=i||e.charAt(0).toUpperCase()+e.slice(1);return o.jsxs(rn,{className:c,children:[o.jsx(on,{status:e,size:t,pulse:r}),n&&o.jsx(nn,{status:e,size:t,children:a})]})},an={small:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},cn=e=>s.css(["",""],({theme:t})=>{let r,n,i;switch(e){case"primary":r=`${t.colors.primary}10`,n=t.colors.primary,i=`${t.colors.primary}30`;break;case"secondary":r=`${t.colors.secondary}10`,n=t.colors.secondary,i=`${t.colors.secondary}30`;break;case"success":r=`${t.colors.success}10`,n=t.colors.success,i=`${t.colors.success}30`;break;case"warning":r=`${t.colors.warning}10`,n=t.colors.warning,i=`${t.colors.warning}30`;break;case"error":r=`${t.colors.error}10`,n=t.colors.error,i=`${t.colors.error}30`;break;case"info":r=`${t.colors.info}10`,n=t.colors.info,i=`${t.colors.info}30`;break;default:r=`${t.colors.textSecondary}10`,n=t.colors.textSecondary,i=`${t.colors.textSecondary}30`}return`
        background-color: ${r};
        color: ${n};
        border: 1px solid ${i};
      `}),ln=s.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>an[e],({variant:e})=>cn(e),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),dn=s.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:t})=>{const r={small:"12px",medium:"14px",large:"16px"};return`
      width: ${r[e]};
      height: ${r[e]};
      font-size: ${t.fontSizes.xs};
    `}),un=({children:e,variant:t="default",size:r="medium",removable:n=!1,onRemove:i,className:c="",onClick:a})=>{const p=f=>{f.stopPropagation(),i==null||i()};return o.jsxs(ln,{variant:t,size:r,clickable:!!a,className:c,onClick:a,children:[e,n&&o.jsx(dn,{size:r,onClick:p,children:"×"})]})},pn=s.div.withConfig({displayName:"TimePickerContainer",componentId:"sc-v5w9zw-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),fn=s.label.withConfig({displayName:"Label",componentId:"sc-v5w9zw-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),gn=s.input.withConfig({displayName:"TimeInput",componentId:"sc-v5w9zw-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),mn=({id:e,name:t,value:r,onChange:n,label:i,required:c=!1,disabled:a=!1,className:p,placeholder:f="HH:MM",min:l,max:d})=>o.jsxs(pn,{className:p,children:[i&&o.jsxs(fn,{htmlFor:e,children:[i,c&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsx(gn,{id:e,name:t,type:"time",value:r,onChange:n,required:c,disabled:a,placeholder:f,min:l,max:d})]}),hn=mn,Ae=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,Ge=(e,t=new Date)=>{const i=new Intl.DateTimeFormat("en",{timeZone:e,timeZoneName:"short"}).formatToParts(t).find(c=>c.type==="timeZoneName");return(i==null?void 0:i.value)||e},Ie=(e,t)=>{const r=t||Ae(),[n,i]=e.split(":").map(Number),c=new Date,a=new Date(c.getFullYear(),c.getMonth(),c.getDate(),n,i,0),p=new Date(a.toLocaleString("en-US",{timeZone:"America/New_York"})),l=new Date(a.toLocaleString("en-US",{timeZone:"UTC"})).getTime()-p.getTime();return new Date(a.getTime()+l).toLocaleTimeString("en-GB",{timeZone:r,hour:"2-digit",minute:"2-digit",hour12:!1})},xn=(e,t)=>{t||Ae();const r=new Date;return new Date(`${r.toDateString()} ${e}:00`).toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1})},Oe=e=>{const t=e||Ae(),r=new Date,n=r.toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=r.toLocaleTimeString("en-GB",{timeZone:t,hour:"2-digit",minute:"2-digit",hour12:!1}),c=Ge("America/New_York",r),a=Ge(t,r);return{nyTime:n,localTime:i,nyTimezone:c,localTimezone:a,formatted:`${n} ${c} | ${i} ${a}`}},bn=()=>{const t=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1});return ye(t)},yn=()=>{const e=new Date;return new Date(e.toLocaleString("en-US",{timeZone:"America/New_York"}))},Tr=e=>{const t=Math.floor(e/60),r=e%60;let n="";return t>0?n=r>0?`${t}h ${r}m`:`${t}h`:n=`${r}m`,{totalMinutes:e,hours:t,minutes:r,formatted:n}},ve=e=>{const t=new Date,r=new Date(t.toLocaleString("en-US",{timeZone:"America/New_York"})),[n,i]=e.split(":").map(Number),c=new Date(r);c.setHours(n,i,0,0),c<=r&&c.setDate(c.getDate()+1);const a=c.getTime()-r.getTime(),p=Math.floor(a/(1e3*60));return Tr(p)},Ir=e=>ve(e),Ee=(e,t,r)=>{const n=r||Ae(),i=Ie(e,n),c=Ie(t,n),a=Ge(n);return{nyStart:e,nyEnd:t,localStart:i,localEnd:c,formatted:`${e}-${t} NY | ${i}-${c} ${a}`}},Er=(e,t)=>{const n=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=ye(n),c=ye(e),a=ye(t);return i>=c&&i<=a},ye=e=>{const[t,r]=e.split(":").map(Number);return t*60+r},vn=e=>{const t=Math.floor(e/60),r=e%60;return`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`},Sn=e=>{const r=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),n=ye(r);for(const i of e)if(ye(i.nyStart)>n)return{nextSession:i.name,timeUntil:ve(i.nyStart),sessionTime:Ee(i.nyStart,i.nyEnd)};if(e.length>0){const i=e[0],c=ve(i.nyStart);return{nextSession:i.name,timeUntil:c,sessionTime:Ee(i.nyStart,i.nyEnd)}}return null},jr=e=>{const t=e.localTimezone.includes("GMT")?"🇮🇪":"🌍";return`${e.localTime} ${t} | ${e.nyTime} 🇺🇸`},wn=e=>`${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`,Cn=(e,t,r,n)=>{const i=Er(t,r),c=Ee(t,r,n);if(i)return{isActive:!0,timeRemaining:Ir(r),sessionTime:c,status:"active"};const a=ve(t);return{isActive:!1,timeUntilStart:a,sessionTime:c,status:a.totalMinutes<24*60?"upcoming":"ended"}},Tn=()=>{console.log("🕐 TIMEZONE CONVERSION TEST:"),console.log("09:00 NY →",Ie("09:00","Europe/Dublin")),console.log("11:50 NY →",Ie("11:50","Europe/Dublin")),console.log("15:15 NY →",Ie("15:15","Europe/Dublin"));const e=Oe("Europe/Dublin");console.log("Current dual time:",e.formatted)},he=s.div.withConfig({displayName:"TimeContainer",componentId:"sc-10dqpqu-0"})(["display:flex;align-items:center;gap:",";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"],({format:e})=>e==="mobile"?"4px":"8px"),je=s.span.withConfig({displayName:"NYTime",componentId:"sc-10dqpqu-1"})(["color:#3b82f6;font-size:inherit;"]),Ne=s.span.withConfig({displayName:"LocalTime",componentId:"sc-10dqpqu-2"})(["color:#10b981;font-size:inherit;"]),ke=s.span.withConfig({displayName:"Separator",componentId:"sc-10dqpqu-3"})(["color:#6b7280;font-size:inherit;"]),Ke=s.span.withConfig({displayName:"Timezone",componentId:"sc-10dqpqu-4"})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]),pt=s.span.withConfig({displayName:"LiveIndicator",componentId:"sc-10dqpqu-5"})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),Gt=s.div.withConfig({displayName:"CountdownContainer",componentId:"sc-10dqpqu-6"})(["display:flex;align-items:center;gap:8px;"]),Kt=s.span.withConfig({displayName:"CountdownValue",componentId:"sc-10dqpqu-7"})(["color:#f59e0b;font-weight:bold;"]),ft=s.span.withConfig({displayName:"CountdownLabel",componentId:"sc-10dqpqu-8"})(["color:#9ca3af;font-size:0.9em;"]),In=({format:e,showLive:t,updateInterval:r})=>{const[n,i]=b.useState(Oe());return b.useEffect(()=>{const c=setInterval(()=>{i(Oe())},r*1e3);return()=>clearInterval(c)},[r]),e==="mobile"?o.jsxs(he,{format:e,children:[o.jsx("span",{children:jr(n)}),t&&o.jsx(pt,{children:"LIVE"})]}):e==="compact"?o.jsxs(he,{format:e,children:[o.jsx(je,{children:n.nyTime}),o.jsx(ke,{children:"|"}),o.jsx(Ne,{children:n.localTime}),t&&o.jsx(pt,{children:"LIVE"})]}):o.jsxs(he,{format:e,children:[o.jsx(je,{children:n.nyTime}),o.jsx(Ke,{children:n.nyTimezone}),o.jsx(ke,{children:"|"}),o.jsx(Ne,{children:n.localTime}),o.jsx(Ke,{children:n.localTimezone}),t&&o.jsx(pt,{children:"LIVE"})]})},En=({nyTime:e,format:t})=>{const r=Oe(),n=Ee(e,e);return t==="mobile"?o.jsx(he,{format:t,children:o.jsxs("span",{children:[n.localStart," 🇮🇪 | ",e," 🇺🇸"]})}):t==="compact"?o.jsxs(he,{format:t,children:[o.jsx(je,{children:e}),o.jsx(ke,{children:"|"}),o.jsx(Ne,{children:n.localStart})]}):o.jsxs(he,{format:t,children:[o.jsx(je,{children:e}),o.jsx(Ke,{children:r.nyTimezone}),o.jsx(ke,{children:"|"}),o.jsx(Ne,{children:n.localStart}),o.jsx(Ke,{children:r.localTimezone})]})},jn=({targetNYTime:e,format:t,updateInterval:r})=>{const[n,i]=b.useState(ve(e));return b.useEffect(()=>{const c=setInterval(()=>{i(ve(e))},r*1e3);return()=>clearInterval(c)},[e,r]),t==="mobile"?o.jsxs(Gt,{children:[o.jsx(Kt,{children:n.formatted}),o.jsxs(ft,{children:["until ",e]})]}):o.jsxs(Gt,{children:[o.jsx(ft,{children:"Next in:"}),o.jsx(Kt,{children:n.formatted}),o.jsxs(ft,{children:["(",e," NY)"]})]})},Nn=({sessionStart:e,sessionEnd:t,format:r})=>{const n=Ee(e,t);return r==="mobile"?o.jsx(he,{format:r,children:o.jsx("span",{children:n.formatted})}):r==="compact"?o.jsxs(he,{format:r,children:[o.jsxs(je,{children:[e,"-",t]}),o.jsx(ke,{children:"|"}),o.jsxs(Ne,{children:[n.localStart,"-",n.localEnd]})]}):o.jsxs(he,{format:r,children:[o.jsx("div",{children:o.jsxs(je,{children:[e,"-",t," NY"]})}),o.jsx(ke,{children:"|"}),o.jsx("div",{children:o.jsxs(Ne,{children:[n.localStart,"-",n.localEnd," Local"]})})]})},kn=({mode:e="current",nyTime:t,targetNYTime:r,sessionStart:n,sessionEnd:i,format:c="desktop",showLive:a=!1,className:p,updateInterval:f=1})=>{const l={className:p,style:{fontSize:c==="mobile"?"14px":c==="compact"?"13px":"14px"}};switch(e){case"static":return t?o.jsx("div",{...l,children:o.jsx(En,{nyTime:t,format:c})}):(console.warn("DualTimeDisplay: nyTime is required for static mode"),null);case"countdown":return r?o.jsx("div",{...l,children:o.jsx(jn,{targetNYTime:r,format:c,updateInterval:f})}):(console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"),null);case"session":return!n||!i?(console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"),null):o.jsx("div",{...l,children:o.jsx(Nn,{sessionStart:n,sessionEnd:i,format:c})});case"current":default:return o.jsx("div",{...l,children:o.jsx(In,{format:c,showLive:a,updateInterval:f})})}},Ln=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-w0dp8e-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Rn=s.label.withConfig({displayName:"Label",componentId:"sc-w0dp8e-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),_n=s.select.withConfig({displayName:"Select",componentId:"sc-w0dp8e-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),Mn=({id:e,name:t,value:r,onChange:n,options:i,label:c,required:a=!1,disabled:p=!1,className:f,placeholder:l})=>o.jsxs(Ln,{className:f,children:[c&&o.jsxs(Rn,{htmlFor:e,children:[c,a&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsxs(_n,{id:e,name:t,value:r,onChange:n,required:a,disabled:p,children:[l&&o.jsx("option",{value:"",disabled:!0,children:l}),i.map(d=>o.jsx("option",{value:d.value,children:d.label},d.value))]})]}),Pn=Mn,Dn=s.span.withConfig({displayName:"StyledLoadingCell",componentId:"sc-1i0qdjp-0"})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;"," border-radius:",";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"],({$size:e,theme:t})=>{var r,n,i,c,a,p,f,l,d;switch(e){case"small":return s.css(["font-size:",";padding:"," ",";"],((r=t.fontSizes)==null?void 0:r.xs)||"12px",((n=t.spacing)==null?void 0:n.xxs)||"2px",((i=t.spacing)==null?void 0:i.xs)||"4px");case"large":return s.css(["font-size:",";padding:"," ",";"],((c=t.fontSizes)==null?void 0:c.lg)||"18px",((a=t.spacing)==null?void 0:a.sm)||"8px",((p=t.spacing)==null?void 0:p.md)||"12px");default:return s.css(["font-size:",";padding:"," ",";"],((f=t.fontSizes)==null?void 0:f.sm)||"14px",((l=t.spacing)==null?void 0:l.xs)||"4px",((d=t.spacing)==null?void 0:d.sm)||"8px")}},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"}),$n=s.span.withConfig({displayName:"LoadingPlaceholder",componentId:"sc-1i0qdjp-1"})(["display:inline-block;width:",";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"],({$width:e})=>e||"60px"),On=e=>{const{size:t="medium",width:r,className:n,"aria-label":i}=e;return o.jsx(Dn,{className:n,$size:t,$width:r,"aria-label":i||"Loading data",role:"cell","aria-busy":"true",children:o.jsx($n,{$width:r})})},zn=On,An=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Fn=s.keyframes(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]),qn=s.div.withConfig({displayName:"StyledSpinner",componentId:"sc-1hoaoss-0"})(["display:inline-block;position:relative;"," &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;"," animation:"," ","s linear infinite;}",""],({$size:e})=>{switch(e){case"xs":return s.css(["width:16px;height:16px;"]);case"sm":return s.css(["width:20px;height:20px;"]);case"md":return s.css(["width:32px;height:32px;"]);case"lg":return s.css(["width:48px;height:48px;"]);case"xl":return s.css(["width:64px;height:64px;"]);default:return s.css(["width:32px;height:32px;"])}},({$variant:e,theme:t})=>{var r,n,i,c,a,p;switch(e){case"primary":return s.css(["border-top-color:",";border-right-color:",";"],((r=t.colors)==null?void 0:r.primary)||"#dc2626",((n=t.colors)==null?void 0:n.primary)||"#dc2626");case"secondary":return s.css(["border-top-color:",";border-right-color:",";"],((i=t.colors)==null?void 0:i.textSecondary)||"#9ca3af",((c=t.colors)==null?void 0:c.textSecondary)||"#9ca3af");case"white":return s.css(["border-top-color:#ffffff;border-right-color:#ffffff;"]);case"red":return s.css(["border-top-color:#dc2626;border-right-color:#dc2626;"]);default:return s.css(["border-top-color:",";border-right-color:",";"],((a=t.colors)==null?void 0:a.primary)||"#dc2626",((p=t.colors)==null?void 0:p.primary)||"#dc2626")}},An,({$speed:e})=>1/e,({$showStripes:e,$variant:t,theme:r})=>e&&s.css(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:",";background-size:8px 8px;animation:"," ","s linear infinite;}"],t==="red"||t==="primary"?"linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)":"linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)",Fn,n=>2/n.$speed)),Bn=s.div.withConfig({displayName:"SpinnerContainer",componentId:"sc-1hoaoss-1"})(["display:inline-flex;align-items:center;justify-content:center;"]),Hn=e=>{const{size:t="md",variant:r="primary",className:n,"aria-label":i,speed:c=1,showStripes:a=!1}=e;return o.jsx(Bn,{className:n,children:o.jsx(qn,{$size:t,$variant:r,$speed:c,$showStripes:a,role:"status","aria-label":i||"Loading","aria-live":"polite"})})},Un=Hn,Yn={none:s.css(["padding:0;"]),small:s.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:s.css(["padding:",";"],({theme:e})=>e.spacing.md),large:s.css(["padding:",";"],({theme:e})=>e.spacing.lg)},Vn={default:s.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:s.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:s.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},Wn=s.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:t})=>e&&s.css(["border:1px solid ",";"],t.colors.border),({padding:e})=>Yn[e],({variant:e})=>Vn[e],({clickable:e})=>e&&s.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:t})=>t.shadows.md)),Gn=s.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),Kn=s.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),Qn=s.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Xn=s.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Jn=s.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Zn=s.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),es=s.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),ts=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),rs=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),os=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Nr=({children:e,title:t="",subtitle:r="",bordered:n=!0,variant:i="default",padding:c="medium",className:a="",footer:p,actions:f,isLoading:l=!1,hasError:d=!1,errorMessage:m="An error occurred",clickable:h=!1,onClick:y,...g})=>{const x=t||r||f;return o.jsxs(Wn,{bordered:n,variant:i,padding:c,clickable:h,className:a,onClick:h?y:void 0,...g,children:[l&&o.jsx(ts,{children:o.jsx(os,{})}),x&&o.jsxs(Gn,{children:[o.jsxs(Kn,{children:[t&&o.jsx(Qn,{children:t}),r&&o.jsx(Xn,{children:r})]}),f&&o.jsx(Jn,{children:f})]}),d&&o.jsx(rs,{children:o.jsx("p",{children:m})}),o.jsx(Zn,{children:e}),p&&o.jsx(es,{children:p})]})},ns=s.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:t})=>{const r={small:t.fontSizes.md,medium:t.fontSizes.lg,large:t.fontSizes.xl};return s.css(["font-size:",";"],r[e])}),ss=s.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:t})=>{const r={small:t.fontSizes.sm,medium:t.fontSizes.md,large:t.fontSizes.lg};return s.css(["font-size:",";"],r[e])}),is={default:s.css(["background-color:transparent;"]),compact:s.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},as=s.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>is[e],({size:e,theme:t})=>{switch(e){case"small":return s.css(["padding:",";min-height:120px;"],t.spacing.md);case"large":return s.css(["padding:",";min-height:300px;"],t.spacing.xl);default:return s.css(["padding:",";min-height:200px;"],t.spacing.lg)}}),cs=s.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:t})=>{const r={small:"32px",medium:"48px",large:"64px"};return s.css(["font-size:",";svg{width:",";height:",";color:",";}"],r[e],r[e],r[e],t.colors.textSecondary)}),ls=s.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),ds=s.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),ht=({title:e="",description:t="",icon:r,actionText:n="",onAction:i,variant:c="default",size:a="medium",className:p="",children:f})=>o.jsxs(as,{variant:c,size:a,className:p,children:[r&&o.jsx(cs,{size:a,children:r}),e&&o.jsx(ns,{size:a,children:e}),t&&o.jsx(ss,{size:a,children:t}),n&&i&&o.jsx(ls,{children:o.jsx(ie,{variant:"primary",size:a==="small"?"small":"medium",onClick:i,children:n})}),f&&o.jsx(ds,{children:f})]}),Qt=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),us=s.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),Xt=s.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),Ye=s.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),Jt=s.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),Zt=s.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),ps=s.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),kr=s.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),fs=s.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),gs=s(kr).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),ms=({error:e,resetError:t,isAppLevel:r,name:n,onSkip:i})=>{const c=()=>{window.location.reload()};return r?o.jsx(Qt,{isAppLevel:!0,children:o.jsxs(us,{children:[o.jsx(Xt,{isAppLevel:!0,children:"Something went wrong"}),o.jsx(Ye,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),o.jsxs(Jt,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Ye,{children:e.message}),e.stack&&o.jsx(Zt,{children:e.stack})]}),o.jsx(gs,{onClick:c,children:"Reload Application"})]})}):o.jsxs(Qt,{children:[o.jsx(Xt,{children:n?`Error in ${n}`:"Something went wrong"}),o.jsx(Ye,{children:n?`We encountered a problem while loading ${n}. You can try again${i?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),o.jsxs(Jt,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Ye,{children:e.message}),e.stack&&o.jsx(Zt,{children:e.stack})]}),o.jsxs(ps,{children:[o.jsx(kr,{onClick:t,children:"Try Again"}),i&&o.jsx(fs,{onClick:i,children:"Skip This Feature"})]})]})};class Lr extends b.Component{constructor(t){super(t),this.resetError=()=>{this.setState({hasError:!1,error:null})},this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,r){const{name:n}=this.props,i=n?`ErrorBoundary(${n})`:"ErrorBoundary";console.error(`Error caught by ${i}:`,t,r),this.props.onError&&this.props.onError(t,r)}componentDidUpdate(t){this.state.hasError&&this.props.resetOnPropsChange&&t.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:t,error:r}=this.state,{children:n,fallback:i,name:c,isFeatureBoundary:a,onSkip:p}=this.props;return t&&r?typeof i=="function"?i({error:r,resetError:this.resetError}):i||o.jsx(ms,{error:r,resetError:this.resetError,isAppLevel:!a,name:c,onSkip:p}):n}}const yt=({isAppLevel:e=!1,isFeatureBoundary:t=!1,children:r,...n})=>{const i=e?"app":t?"feature":"component",c={resetOnPropsChange:i!=="app",resetOnUnmount:i!=="app",isFeatureBoundary:i==="feature"};return o.jsx(Lr,{...c,...n,children:r})},hs=e=>o.jsx(yt,{isAppLevel:!0,...e}),xs=({featureName:e,children:t,...r})=>o.jsx(yt,{isFeatureBoundary:!0,name:e,children:t,...r}),bs=s.div.withConfig({displayName:"TabContainer",componentId:"sc-lgz9vh-0"})(["display:flex;flex-direction:column;width:100%;"]),ys=s.div.withConfig({displayName:"TabList",componentId:"sc-lgz9vh-1"})(["display:flex;border-bottom:1px solid ",";margin-bottom:",";"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),vs=s.button.withConfig({displayName:"TabButton",componentId:"sc-lgz9vh-2"})(["padding:"," ",";background:none;border:none;border-bottom:2px solid ",";color:",";font-weight:",";cursor:pointer;transition:all ",";&:hover{color:",";}&:focus{outline:none;color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({active:e,theme:t})=>e?t.colors.primary:"transparent",({active:e,theme:t})=>e?t.colors.primary:t.colors.textSecondary,({active:e,theme:t})=>e?t.fontWeights.semibold:t.fontWeights.regular,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),Ss=s.div.withConfig({displayName:"TabContent",componentId:"sc-lgz9vh-3"})(["padding:"," 0;"],({theme:e})=>e.spacing.sm),ws=({tabs:e,defaultTab:t,className:r,activeTab:n,onTabClick:i})=>{var l;const[c,a]=b.useState(t||e[0].id),p=n!==void 0?n:c,f=(d,m)=>{d.preventDefault(),d.stopPropagation(),i?i(m):a(m)};return o.jsxs(bs,{className:r,children:[o.jsx(ys,{children:e.map(d=>o.jsx(vs,{active:p===d.id,onClick:m=>f(m,d.id),type:"button",form:"",tabIndex:0,"data-tab-id":d.id,children:d.label},d.id))}),o.jsx(Ss,{children:(l=e.find(d=>d.id===p))==null?void 0:l.content})]})},Cs=ws,Rr={required:(e="This field is required")=>({validate:t=>typeof t=="string"?t.trim().length>0:typeof t=="number"?!isNaN(t):Array.isArray(t)?t.length>0:t!=null&&t!==void 0,message:e}),email:(e="Please enter a valid email address")=>({validate:t=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t),message:e}),minLength:(e,t)=>({validate:r=>r.length>=e,message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validate:r=>r.length<=e,message:t||`Must be no more than ${e} characters`}),min:(e,t)=>({validate:r=>r>=e,message:t||`Must be at least ${e}`}),max:(e,t)=>({validate:r=>r<=e,message:t||`Must be no more than ${e}`}),pattern:(e,t)=>({validate:r=>e.test(r),message:t})},_r=(e={})=>{const{initialValue:t="",required:r=!1,type:n="text",validationRules:i=[],validateOnChange:c=!1,validateOnBlur:a=!0,transform:p}=e,f=b.useMemo(()=>{const R=[...i];return r&&!i.some(N=>N.message.toLowerCase().includes("required"))&&R.unshift(Rr.required()),R},[r,i]),[l,d]=b.useState(t),[m,h]=b.useState(null),[y,g]=b.useState(!1),[x,C]=b.useState(!1),S=b.useMemo(()=>l!==t,[l,t]),w=b.useMemo(()=>m===null&&!x,[m,x]),_=b.useMemo(()=>m===null&&!x,[m,x]),z=b.useCallback(async()=>{C(!0);try{for(const R of f)if(!R.validate(l))return h(R.message),C(!1),!1;return h(null),C(!1),!0}catch{return h("Validation error occurred"),C(!1),!1}},[l,f]),$=b.useCallback(()=>{d(t),h(null),g(!1),C(!1)},[t]),L=b.useCallback(R=>{let N;n==="number"?N=parseFloat(R.target.value)||0:N=R.target.value,p&&(N=p(N)),d(N),c&&y&&setTimeout(()=>z(),0)},[n,p,c,y,z]),j=b.useCallback(R=>{g(!0),a&&z()},[a,z]);return{value:l,error:m,touched:y,dirty:S,valid:w,isValid:_,validating:x,setValue:d,setError:h,setTouched:g,validate:z,reset:$,handleChange:L,handleBlur:j}},Ts=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-oh07s1-0"})(["display:flex;flex-direction:column;gap:",";width:100%;margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),Is=s.label.withConfig({displayName:"Label",componentId:"sc-oh07s1-1"})(["font-size:",";font-weight:",";color:",";",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||"500"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$required:e})=>e&&s.css(["&::after{content:' *';color:",";}"],({theme:t})=>{var r;return((r=t.colors)==null?void 0:r.error)||"#dc2626"})),vt=s.css(["width:100%;border:1px solid ",";border-radius:",";background-color:",";color:",";font-size:",";padding:",";transition:",";&:focus{outline:none;border-color:",";box-shadow:0 0 0 2px ",";}&:disabled{background-color:",";color:",";cursor:not-allowed;}&::placeholder{color:",";}"],({theme:e,$hasError:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.error)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e,$size:t})=>{var r,n,i;switch(t){case"sm":return((r=e.fontSizes)==null?void 0:r.sm)||"14px";case"lg":return((n=e.fontSizes)==null?void 0:n.lg)||"18px";default:return((i=e.fontSizes)==null?void 0:i.md)||"16px"}},({theme:e,$size:t})=>{var r,n,i,c,a,p;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.primary?`${e.colors.primary}20`:"rgba(220, 38, 38, 0.2)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.chartGrid)||"#374151"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Es=s.input.withConfig({displayName:"StyledInput",componentId:"sc-oh07s1-2"})(["",""],vt),js=s.textarea.withConfig({displayName:"StyledTextarea",componentId:"sc-oh07s1-3"})([""," resize:vertical;min-height:80px;"],vt),Ns=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-oh07s1-4"})([""," cursor:pointer;"],vt),ks=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-oh07s1-5"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#dc2626"}),Ls=s.div.withConfig({displayName:"HelpText",componentId:"sc-oh07s1-6"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Rs=e=>{const{name:t,label:r,placeholder:n,disabled:i=!1,className:c,size:a="md",helpText:p,inputType:f="input",options:l=[],rows:d=4,onChange:m,onBlur:h,...y}=e,g=_r({...y,validateOnBlur:!0});b.useEffect(()=>{m&&m(g.value)},[g.value,m]);const x=w=>{g.handleBlur(w),h&&h()},C={id:t,name:t,value:g.value,onChange:g.handleChange,onBlur:x,disabled:i,placeholder:n,$hasError:!!g.error,$disabled:i,$size:a},S=()=>{switch(f){case"textarea":return o.jsx(js,{...C,rows:d});case"select":return o.jsxs(Ns,{...C,children:[n&&o.jsx("option",{value:"",disabled:!0,children:n}),l.map(w=>o.jsx("option",{value:w.value,children:w.label},w.value))]});default:return o.jsx(Es,{...C,type:y.type||"text"})}};return o.jsxs(Ts,{className:c,children:[r&&o.jsx(Is,{htmlFor:t,$required:!!y.required,children:r}),S(),g.error&&g.touched&&o.jsx(ks,{role:"alert",children:g.error}),p&&!g.error&&o.jsx(Ls,{children:p})]})},_s=Rs,Ms={string:e=>(t,r)=>{const n=String(t[e]||""),i=String(r[e]||"");return n.localeCompare(i)},number:e=>(t,r)=>{const n=Number(t[e])||0,i=Number(r[e])||0;return n-i},date:e=>(t,r)=>{const n=new Date(t[e]).getTime(),i=new Date(r[e]).getTime();return n-i},boolean:e=>(t,r)=>{const n=!!t[e],i=!!r[e];return Number(n)-Number(i)}},Mr=({data:e,columns:t,defaultSort:r})=>{const[n,i]=b.useState(r?{field:r.field,direction:r.direction}:null),c=b.useCallback(d=>{const m=t.find(h=>h.field===d);m!=null&&m.sortable&&i(h=>{var y;if((h==null?void 0:h.field)===d)return{field:d,direction:h.direction==="asc"?"desc":"asc"};{const g=typeof((y=e[0])==null?void 0:y[d])=="number"?"desc":"asc";return{field:d,direction:g}}})},[t,e]),a=b.useMemo(()=>{if(!n)return e;const d=t.find(h=>h.field===n.field);return d?[...e].sort((h,y)=>{let g=0;if(d.sortFn)g=d.sortFn(h,y);else{const x=h[n.field],C=y[n.field];typeof x=="string"&&typeof C=="string"?g=x.localeCompare(C):typeof x=="number"&&typeof C=="number"?g=x-C:g=String(x).localeCompare(String(C))}return n.direction==="asc"?g:-g}):e},[e,n,t]),p=b.useCallback(d=>!n||n.field!==d?null:n.direction==="asc"?"↑":"↓",[n]),f=b.useCallback(d=>(n==null?void 0:n.field)===d,[n]),l=b.useCallback(d=>(n==null?void 0:n.field)===d?n.direction:null,[n]);return{sortedData:a,sortConfig:n,handleSort:c,getSortIcon:p,isSorted:f,getSortDirection:l}},er=s.div.withConfig({displayName:"Container",componentId:"sc-13j9udn-0"})(["overflow-x:auto;border-radius:",";border:1px solid ",";"],({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),Ps=s.table.withConfig({displayName:"Table",componentId:"sc-13j9udn-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e,$size:t})=>{var r,n,i;switch(t){case"sm":return((r=e.fontSizes)==null?void 0:r.xs)||"12px";case"lg":return((n=e.fontSizes)==null?void 0:n.md)||"16px";default:return((i=e.fontSizes)==null?void 0:i.sm)||"14px"}}),Ds=s.thead.withConfig({displayName:"TableHead",componentId:"sc-13j9udn-2"})(["background-color:",";border-bottom:2px solid ",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),$s=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13j9udn-3"})([""]),tr=s.tr.withConfig({displayName:"TableRow",componentId:"sc-13j9udn-4"})([""," "," "," border-bottom:1px solid ",";"],({$striped:e,theme:t})=>{var r;return e&&s.css(["&:nth-child(even){background-color:",";}"],((r=t.colors)==null?void 0:r.background)||"#0f0f0f")},({$hoverable:e,theme:t})=>{var r;return e&&s.css(["&:hover{background-color:",";}"],((r=t.colors)==null?void 0:r.surface)||"#1f2937")},({$clickable:e})=>e&&s.css(["cursor:pointer;"]),({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),Os=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13j9udn-5"})(["text-align:left;font-weight:",";color:",";cursor:",";user-select:none;transition:",";padding:",";&:hover{","}&:focus{outline:2px solid ",";outline-offset:-2px;}"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||"600"},({theme:e,$active:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((n=e.colors)==null?void 0:n.textPrimary)||"#ffffff"},({$sortable:e})=>e?"pointer":"default",({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e,$size:t})=>{var r,n,i,c,a,p;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({$sortable:e,theme:t})=>{var r;return e&&s.css(["color:",";"],((r=t.colors)==null?void 0:r.primary)||"#dc2626")},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),zs=s.td.withConfig({displayName:"TableCell",componentId:"sc-13j9udn-6"})(["padding:",";color:",";"],({theme:e,$size:t})=>{var r,n,i,c,a,p;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),As=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13j9udn-7"})(["display:inline-block;margin-left:",";font-size:",";&::after{content:'","';}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({$direction:e})=>e==="asc"?"↑":"↓"),Fs=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13j9udn-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e,$size:t})=>{var r,n,i;switch(t){case"sm":return((r=e.spacing)==null?void 0:r.md)||"12px";case"lg":return((n=e.spacing)==null?void 0:n.xl)||"24px";default:return((i=e.spacing)==null?void 0:i.lg)||"16px"}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),qs=({data:e,columns:t,className:r,emptyMessage:n="No data available",defaultSort:i,renderCell:c,onRowClick:a,size:p="md",striped:f=!0,hoverable:l=!0})=>{const{sortedData:d,handleSort:m,getSortIcon:h,isSorted:y}=Mr({data:e,columns:t,defaultSort:i});return e.length===0?o.jsx(er,{className:r,children:o.jsx(Fs,{$size:p,children:n})}):o.jsx(er,{className:r,children:o.jsxs(Ps,{$size:p,$striped:f,$hoverable:l,children:[o.jsx(Ds,{children:o.jsx(tr,{$striped:!1,$hoverable:!1,$clickable:!1,children:t.map(g=>o.jsxs(Os,{$sortable:g.sortable||!1,$active:y(g.field),$size:p,onClick:()=>g.sortable&&m(g.field),tabIndex:g.sortable?0:-1,onKeyDown:x=>{g.sortable&&(x.key==="Enter"||x.key===" ")&&(x.preventDefault(),m(g.field))},role:g.sortable?"button":void 0,"aria-sort":y(g.field)?h(g.field)==="↑"?"ascending":"descending":void 0,children:[g.label,y(g.field)&&o.jsx(As,{$direction:h(g.field)==="↑"?"asc":"desc"})]},String(g.field)))})}),o.jsx($s,{children:d.map((g,x)=>o.jsx(tr,{$striped:f,$hoverable:l,$clickable:!!a,onClick:()=>a==null?void 0:a(g,x),tabIndex:a?0:-1,onKeyDown:C=>{a&&(C.key==="Enter"||C.key===" ")&&(C.preventDefault(),a(g,x))},role:a?"button":void 0,children:t.map(C=>{const S=g[C.field];return o.jsx(zs,{$size:p,children:c?c(S,g,C):String(S)},String(C.field))})},x))})]})})},Bs=qs,Hs=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),Us=s.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:t})=>t?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),Ys=s.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t})=>t?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),Vs=({children:e,label:t,helperText:r,required:n=!1,error:i,className:c,id:a,...p})=>{const f=a||`field-${Math.random().toString(36).substr(2,9)}`,l=b.Children.map(e,d=>b.isValidElement(d)?b.cloneElement(d,{id:f,required:n,error:i}):d);return o.jsxs(Hs,{className:c,...p,children:[o.jsxs(Us,{htmlFor:f,hasError:!!i,children:[t,n&&o.jsx("span",{className:"required-indicator",children:"*"})]}),l,(r||i)&&o.jsx(Ys,{hasError:!!i,children:i||r})]})},Ws=s.keyframes(["from{opacity:0;}to{opacity:1;}"]),Gs=s.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),Ks=s.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,Ws),Qs=s.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},Gs,({size:e})=>e==="fullscreen"&&s.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&s.css(["margin:auto;"])),Xs=s.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Js=s.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Zs=s.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),ei=s.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&s.css(["overflow-y:auto;flex:1;"])),ti=s.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),ri=({isOpen:e,title:t="",children:r,onClose:n,size:i="medium",closeOnOutsideClick:c=!0,showCloseButton:a=!0,footer:p,hasFooter:f=!0,primaryActionText:l="",onPrimaryAction:d,primaryActionDisabled:m=!1,primaryActionLoading:h=!1,secondaryActionText:y="",onSecondaryAction:g,secondaryActionDisabled:x=!1,className:C="",zIndex:S=1e3,centered:w=!0,scrollable:_=!0})=>{const z=b.useRef(null);b.useEffect(()=>{const R=N=>{N.key==="Escape"&&e&&c&&n()};return document.addEventListener("keydown",R),()=>{document.removeEventListener("keydown",R)}},[e,n,c]);const $=R=>{z.current&&!z.current.contains(R.target)&&c&&n()};b.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const L=o.jsxs(o.Fragment,{children:[y&&o.jsx(ie,{variant:"outline",onClick:g,disabled:x,children:y}),l&&o.jsx(ie,{onClick:d,disabled:m,loading:h,children:l})]});if(!e)return null;const j=o.jsx(Ks,{onClick:$,zIndex:S,children:o.jsxs(Qs,{ref:z,size:i,className:C,centered:w,scrollable:_,onClick:R=>R.stopPropagation(),children:[(t||a)&&o.jsxs(Xs,{children:[t&&o.jsx(Js,{children:t}),a&&o.jsx(Zs,{onClick:n,"aria-label":"Close",children:"×"})]}),o.jsx(ei,{scrollable:_,children:r}),f&&(p||l||y)&&o.jsx(ti,{children:p||L})]})});return vo.createPortal(j,document.body)},oi=s.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),ni=s.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:t})=>e&&s.css(["border:1px solid ",";border-radius:",";"],t.colors.border,t.borderRadius.sm),({compact:e,theme:t})=>e?s.css(["th,td{padding:"," ",";}"],t.spacing.xs,t.spacing.sm):s.css(["th,td{padding:"," ",";}"],t.spacing.sm,t.spacing.md)),si=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),ii=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),ai=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:t})=>t.colors.background),({isSorted:e,theme:t})=>e&&s.css(["color:",";"],t.colors.primary)),ci=s.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),li=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),di=s.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:t,isSelected:r})=>e&&!r&&s.css(["&:nth-child(even){background-color:","50;}"],t.colors.background),({hoverable:e,theme:t,isSelected:r})=>e&&!r&&s.css(["&:hover{background-color:","aa;}"],t.colors.background),({isSelected:e,theme:t})=>e&&s.css(["background-color:","15;"],t.colors.primary),({isClickable:e})=>e&&s.css(["cursor:pointer;"])),ui=s.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),pi=s.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),fi=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),gi=s.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),mi=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),hi=s.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),xi=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),bi=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function yi({columns:e,data:t,isLoading:r=!1,bordered:n=!0,striped:i=!0,hoverable:c=!0,compact:a=!1,stickyHeader:p=!1,height:f,onRowClick:l,isRowSelected:d,onSort:m,sortColumn:h,sortDirection:y,pagination:g=!1,currentPage:x=1,pageSize:C=10,totalRows:S=0,onPageChange:w,onPageSizeChange:_,className:z,emptyMessage:$="No data available",scrollable:L=!0}){const j=b.useMemo(()=>e.filter(k=>!k.hidden),[e]),R=b.useMemo(()=>Math.ceil(S/C),[S,C]),N=b.useMemo(()=>{if(!g)return t;const k=(x-1)*C,H=k+C;return S>0&&t.length<=C?t:t.slice(k,H)},[t,g,x,C,S]),U=k=>{if(!m)return;m(k,h===k&&y==="asc"?"desc":"asc")},Y=k=>{k<1||k>R||!w||w(k)};return o.jsxs("div",{style:{position:"relative"},children:[r&&o.jsx(xi,{children:o.jsx(bi,{})}),o.jsx(oi,{height:f,scrollable:L,children:o.jsxs(ni,{bordered:n,striped:i,compact:a,className:z,children:[o.jsx(si,{stickyHeader:p,children:o.jsx(ii,{children:j.map(k=>o.jsxs(ai,{sortable:k.sortable,isSorted:h===k.id,align:k.align,width:k.width,onClick:()=>k.sortable&&U(k.id),children:[k.header,k.sortable&&o.jsx(ci,{direction:h===k.id?y:void 0})]},k.id))})}),o.jsx(li,{children:N.length>0?N.map((k,H)=>o.jsx(di,{hoverable:c,striped:i,isSelected:d?d(k,H):!1,isClickable:!!l,onClick:()=>l&&l(k,H),children:j.map(ee=>o.jsx(ui,{align:ee.align,children:ee.cell(k,H)},ee.id))},H)):o.jsx("tr",{children:o.jsx("td",{colSpan:j.length,children:o.jsx(pi,{children:$})})})})]})}),g&&R>0&&o.jsxs(fi,{children:[o.jsxs(gi,{children:["Showing ",Math.min((x-1)*C+1,S)," to"," ",Math.min(x*C,S)," of ",S," entries"]}),o.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[_&&o.jsxs(hi,{children:[o.jsx("span",{children:"Show"}),o.jsx("select",{value:C,onChange:k=>_(Number(k.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(k=>o.jsx("option",{value:k,children:k},k))}),o.jsx("span",{children:"entries"})]}),o.jsxs(mi,{children:[o.jsx(ie,{size:"small",variant:"outline",onClick:()=>Y(1),disabled:x===1,children:"First"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>Y(x-1),disabled:x===1,children:"Prev"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>Y(x+1),disabled:x===R,children:"Next"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>Y(R),disabled:x===R,children:"Last"})]})]})]})]})}const ce={[M.MORNING_BREAKOUT]:{type:M.MORNING_BREAKOUT,name:"9:50-10:10 Macro",timeRange:{start:"09:50:00",end:"10:10:00"},description:"Morning breakout period - high volatility after market open",characteristics:["High Volume","Breakout Setups","Gap Fills","Opening Range"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[M.MID_MORNING_REVERSION]:{type:M.MID_MORNING_REVERSION,name:"10:50-11:10 Macro",timeRange:{start:"10:50:00",end:"11:10:00"},description:"Mid-morning reversion period - mean reversion opportunities",characteristics:["Mean Reversion","Pullback Setups","Support/Resistance Tests"],volatilityLevel:3,volumeLevel:3,isHighProbability:!0},[M.PRE_LUNCH]:{type:M.PRE_LUNCH,name:"11:50-12:10 Macro",timeRange:{start:"11:50:00",end:"12:10:00"},description:"Pre-lunch macro window - specific high-activity period within lunch session",characteristics:["Consolidation","Range Trading","Pre-Lunch Activity"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,parentMacro:M.LUNCH_MACRO_EXTENDED},[M.LUNCH_MACRO_EXTENDED]:{type:M.LUNCH_MACRO_EXTENDED,name:"Lunch Macro (11:30-13:30)",timeRange:{start:"11:30:00",end:"13:30:00"},description:"Extended lunch period spanning late morning through early afternoon",characteristics:["Multi-Session","Lunch Trading","Lower Volume","Transition Period"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,isMultiSession:!0,spansSessions:[G.NEW_YORK_AM,G.NEW_YORK_PM],subPeriods:[]},[M.LUNCH_MACRO]:{type:M.LUNCH_MACRO,name:"Lunch Macro (12:00-13:30)",timeRange:{start:"12:00:00",end:"13:30:00"},description:"Traditional lunch time trading - typically lower volume",characteristics:["Low Volume","Range Bound","Choppy Price Action"],volatilityLevel:2,volumeLevel:1,isHighProbability:!1},[M.POST_LUNCH]:{type:M.POST_LUNCH,name:"13:50-14:10 Macro",timeRange:{start:"13:50:00",end:"14:10:00"},description:"Post-lunch macro window",characteristics:["Volume Pickup","Trend Resumption"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1},[M.PRE_CLOSE]:{type:M.PRE_CLOSE,name:"14:50-15:10 Macro",timeRange:{start:"14:50:00",end:"15:10:00"},description:"Pre-close macro window",characteristics:["Institutional Activity","Position Adjustments"],volatilityLevel:3,volumeLevel:4,isHighProbability:!1},[M.POWER_HOUR]:{type:M.POWER_HOUR,name:"15:15-15:45 Macro (Power Hour)",timeRange:{start:"15:15:00",end:"15:45:00"},description:"Last hour macro - high activity before close",characteristics:["High Volume","Institutional Flows","EOD Positioning"],volatilityLevel:4,volumeLevel:5,isHighProbability:!0},[M.MOC]:{type:M.MOC,name:"MOC (Market on Close)",timeRange:{start:"15:45:00",end:"16:00:00"},description:"Market on close period",characteristics:["MOC Orders","Final Positioning","High Volume"],volatilityLevel:4,volumeLevel:5,isHighProbability:!1},[M.LONDON_OPEN]:{type:M.LONDON_OPEN,name:"London Open",timeRange:{start:"08:00:00",end:"09:00:00"},description:"London market opening hour",characteristics:["European Activity","Currency Moves","News Reactions"],volatilityLevel:4,volumeLevel:4,isHighProbability:!0},[M.LONDON_NY_OVERLAP]:{type:M.LONDON_NY_OVERLAP,name:"London/NY Overlap",timeRange:{start:"14:00:00",end:"16:00:00"},description:"London and New York session overlap",characteristics:["Highest Volume","Major Moves","Cross-Market Activity"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[M.CUSTOM]:{type:M.CUSTOM,name:"Custom Period",timeRange:{start:"00:00:00",end:"23:59:59"},description:"User-defined custom time period",characteristics:["Custom"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1}},vi=()=>{const e=Object.values(Si).map(i=>({id:i.type,...i})),r=[{...ce[M.LUNCH_MACRO_EXTENDED],id:"lunch-macro-extended",subPeriods:[{...ce[M.PRE_LUNCH],id:"pre-lunch-sub"}]}],n={};return e.forEach(i=>{i.macroPeriods.forEach(c=>{n[c.type]={...c,parentSession:i.type}})}),r.forEach(i=>{n[i.type]={...i,spansSessions:i.spansSessions}}),{sessions:e,sessionsByType:e.reduce((i,c)=>(i[c.type]=c,i),{}),macrosByType:n,multiSessionMacros:r}},Si={[G.NEW_YORK_AM]:{type:G.NEW_YORK_AM,name:"New York AM Session",timeRange:{start:"09:30:00",end:"12:00:00"},description:"New York morning session - high activity and volatility",timezone:"America/New_York",characteristics:["High Volume","Trend Development","Breakout Opportunities"],color:"#dc2626",macroPeriods:[{...ce[M.MORNING_BREAKOUT],id:"morning-breakout"},{...ce[M.MID_MORNING_REVERSION],id:"mid-morning-reversion"},{...ce[M.PRE_LUNCH],id:"pre-lunch"}]},[G.NEW_YORK_PM]:{type:G.NEW_YORK_PM,name:"New York PM Session",timeRange:{start:"12:00:00",end:"16:00:00"},description:"New York afternoon session - institutional activity increases toward close",timezone:"America/New_York",characteristics:["Institutional Flows","EOD Positioning","Power Hour Activity"],color:"#dc2626",macroPeriods:[{...ce[M.LUNCH_MACRO],id:"lunch-macro"},{...ce[M.POST_LUNCH],id:"post-lunch"},{...ce[M.PRE_CLOSE],id:"pre-close"},{...ce[M.POWER_HOUR],id:"power-hour"},{...ce[M.MOC],id:"moc"}]},[G.LONDON]:{type:G.LONDON,name:"London Session",timeRange:{start:"08:00:00",end:"16:00:00"},description:"London trading session - European market activity",timezone:"Europe/London",characteristics:["European Activity","Currency Focus","News-Driven"],color:"#1f2937",macroPeriods:[{...ce[M.LONDON_OPEN],id:"london-open"},{...ce[M.LONDON_NY_OVERLAP],id:"london-ny-overlap"}]},[G.ASIA]:{type:G.ASIA,name:"Asia Session",timeRange:{start:"18:00:00",end:"03:00:00"},description:"Asian trading session - typically lower volatility",timezone:"Asia/Tokyo",characteristics:["Lower Volume","Range Trading","News Reactions"],color:"#4b5563",macroPeriods:[]},[G.PRE_MARKET]:{type:G.PRE_MARKET,name:"Pre-Market",timeRange:{start:"04:00:00",end:"09:30:00"},description:"Pre-market trading hours",timezone:"America/New_York",characteristics:["Low Volume","News Reactions","Gap Setups"],color:"#6b7280",macroPeriods:[]},[G.AFTER_HOURS]:{type:G.AFTER_HOURS,name:"After Hours",timeRange:{start:"16:00:00",end:"20:00:00"},description:"After-hours trading",timezone:"America/New_York",characteristics:["Low Volume","Earnings Reactions","News-Driven"],color:"#6b7280",macroPeriods:[]},[G.OVERNIGHT]:{type:G.OVERNIGHT,name:"Overnight",timeRange:{start:"20:00:00",end:"04:00:00"},description:"Overnight session",timezone:"America/New_York",characteristics:["Very Low Volume","Futures Activity"],color:"#374151",macroPeriods:[]}};class te{static getSessionHierarchy(){return this.hierarchy||(this.hierarchy=this.buildHierarchy()),this.hierarchy}static buildHierarchy(){return vi()}static timeToMinutes(t){const[r,n,i=0]=t.split(":").map(Number);return r*60+n+i/60}static minutesToTime(t){const r=Math.floor(t/60),n=Math.floor(t%60),i=Math.floor(t%1*60);return`${r.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`}static isTimeInRange(t,r){const n=this.timeToMinutes(t),i=this.timeToMinutes(r.start),c=this.timeToMinutes(r.end);return c<i?n>=i||n<=c:n>=i&&n<=c}static validateTime(t){var c;if(!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(t))return{isValid:!1,error:"Invalid time format. Use HH:MM or HH:MM:SS format."};const n=this.getSessionHierarchy(),i=[];for(const[a,p]of Object.entries(n.macrosByType))this.isTimeInRange(t,p.timeRange)&&i.push({type:a,macro:p,isSubPeriod:!!p.parentMacro});if(i.length>0){const p=i.sort((l,d)=>{if(l.isSubPeriod&&!d.isSubPeriod)return-1;if(!l.isSubPeriod&&d.isSubPeriod)return 1;const m=this.timeToMinutes(l.macro.timeRange.end)-this.timeToMinutes(l.macro.timeRange.start),h=this.timeToMinutes(d.macro.timeRange.end)-this.timeToMinutes(d.macro.timeRange.start);return m-h})[0],f=i.length>1;return{isValid:!0,suggestedMacro:p.type,suggestedSession:p.macro.parentSession||((c=p.macro.spansSessions)==null?void 0:c[0]),warning:f?`Time falls within ${i.length} overlapping macro periods. Suggesting most specific: ${p.macro.name}`:void 0}}for(const a of n.sessions)if(this.isTimeInRange(t,a.timeRange))return{isValid:!0,suggestedSession:a.type,warning:"Time falls within session but not in a specific macro period."};return{isValid:!0,warning:"Time does not fall within any defined session or macro period."}}static getSession(t){return this.getSessionHierarchy().sessionsByType[t]||null}static getMacroPeriod(t){return this.getSessionHierarchy().macrosByType[t]||null}static getMacroPeriodsForSession(t){const r=this.getSession(t);return(r==null?void 0:r.macroPeriods)||[]}static createSessionSelection(t,r,n){if(r){const i=this.getMacroPeriod(r);return{session:i==null?void 0:i.parentSession,macroPeriod:r,displayLabel:(i==null?void 0:i.name)||"Unknown Macro",selectionType:"macro"}}if(t){const i=this.getSession(t);return{session:t,displayLabel:(i==null?void 0:i.name)||"Unknown Session",selectionType:"session"}}return n?{customTimeRange:n,displayLabel:`${n.start} - ${n.end}`,selectionType:"custom"}:{displayLabel:"No Selection",selectionType:"custom"}}static filterSessions(t={}){var c,a;const r=this.getSessionHierarchy();let n=[...r.sessions],i=Object.values(r.macrosByType);return t.activeOnly&&(n=n.filter(p=>p.isActive)),(c=t.sessionTypes)!=null&&c.length&&(n=n.filter(p=>t.sessionTypes.includes(p.type))),(a=t.macroTypes)!=null&&a.length&&(i=i.filter(p=>t.macroTypes.includes(p.type))),t.highProbabilityOnly&&(i=i.filter(p=>p.isHighProbability)),t.minVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel>=t.minVolatility)),t.maxVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel<=t.maxVolatility)),{sessions:n,macros:i}}static getCurrentSession(){const t=new Date,r=`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}:00`,n=this.validateTime(r);return n.suggestedMacro?this.createSessionSelection(n.suggestedSession,n.suggestedMacro):n.suggestedSession?this.createSessionSelection(n.suggestedSession):null}static timeRangesOverlap(t,r){const n=this.timeToMinutes(t.start),i=this.timeToMinutes(t.end),c=this.timeToMinutes(r.start),a=this.timeToMinutes(r.end);return Math.max(n,c)<Math.min(i,a)}static getDisplayOptions(){const t=this.getSessionHierarchy(),r=t.sessions.map(i=>({value:i.type,label:i.name,group:"Sessions"})),n=Object.values(t.macrosByType).map(i=>{var c;return{value:i.type,label:i.name,group:((c=t.sessionsByType[i.parentSession])==null?void 0:c.name)||"Other",parentSession:i.parentSession}});return{sessionOptions:r,macroOptions:n}}static getOverlappingMacros(t){const r=this.getSessionHierarchy(),n=[];for(const[i,c]of Object.entries(r.macrosByType))this.isTimeInRange(t,c.timeRange)&&n.push({type:i,macro:c,isSubPeriod:!!c.parentMacro,isMultiSession:!!c.spansSessions});return n.sort((i,c)=>{if(i.isSubPeriod&&!c.isSubPeriod)return-1;if(!i.isSubPeriod&&c.isSubPeriod)return 1;const a=this.timeToMinutes(i.macro.timeRange.end)-this.timeToMinutes(i.macro.timeRange.start),p=this.timeToMinutes(c.macro.timeRange.end)-this.timeToMinutes(c.macro.timeRange.start);return a-p})}static getMultiSessionMacros(){return this.getSessionHierarchy().multiSessionMacros||[]}static hasSubPeriods(t){const r=this.getMacroPeriod(t);return!!(r!=null&&r.subPeriods&&r.subPeriods.length>0)}static getSubPeriods(t){const r=this.getMacroPeriod(t);return(r==null?void 0:r.subPeriods)||[]}static convertLegacySession(t){const n={"NY Open":{session:G.NEW_YORK_AM},"London Open":{session:G.LONDON},"Lunch Macro":{macro:M.LUNCH_MACRO_EXTENDED},"Lunch Macro (11:30-13:30)":{macro:M.LUNCH_MACRO_EXTENDED},"Lunch Macro (12:00-13:30)":{macro:M.LUNCH_MACRO},MOC:{macro:M.MOC},Overnight:{session:G.OVERNIGHT},"Pre-Market":{session:G.PRE_MARKET},"After Hours":{session:G.AFTER_HOURS},"Power Hour":{macro:M.POWER_HOUR},"10:50-11:10":{macro:M.MID_MORNING_REVERSION},"11:50-12:10":{macro:M.PRE_LUNCH},"15:15-15:45":{macro:M.POWER_HOUR}}[t];return n?this.createSessionSelection(n.session,n.macro):null}}te.hierarchy=null;const Pr=(e={})=>{const{initialSelection:t,autoDetectCurrent:r=!1,filterOptions:n={},onSelectionChange:i,validateTimes:c=!0}=e,[a,p]=b.useState(t||{displayLabel:"No Selection",selectionType:"custom"}),f=b.useMemo(()=>te.getCurrentSession(),[]),l=b.useMemo(()=>f!==null,[f]),{availableSessions:d,availableMacros:m}=b.useMemo(()=>{const{sessions:L,macros:j}=te.filterSessions(n),{sessionOptions:R,macroOptions:N}=te.getDisplayOptions(),U=R.filter(k=>L.some(H=>H.type===k.value)),Y=N.filter(k=>j.some(H=>H.type===k.value));return{availableSessions:U,availableMacros:Y}},[n]),h=b.useMemo(()=>{const L=te.getSessionHierarchy();return d.map(j=>{L.sessionsByType[j.value];const R=m.filter(N=>N.parentSession===j.value).map(N=>({value:N.value,label:N.label}));return{session:j.value,sessionLabel:j.label,macros:R}})},[d,m]);b.useEffect(()=>{r&&f&&!t&&p(f)},[r,f,t]),b.useEffect(()=>{i==null||i(a)},[a,i]);const y=b.useCallback(L=>{const j=te.createSessionSelection(L);p(j)},[]),g=b.useCallback(L=>{const j=te.createSessionSelection(void 0,L);p(j)},[]),x=b.useCallback(L=>{const j=te.createSessionSelection(void 0,void 0,L);p(j)},[]),C=b.useCallback(()=>{p({displayLabel:"No Selection",selectionType:"custom"})},[]),S=b.useCallback(L=>c?te.validateTime(L):{isValid:!0},[c]),w=b.useMemo(()=>{if(a.selectionType==="session"&&a.session)return te.getSession(a.session)!==null;if(a.selectionType==="macro"&&a.macroPeriod)return te.getMacroPeriod(a.macroPeriod)!==null;if(a.selectionType==="custom"&&a.customTimeRange){const L=S(a.customTimeRange.start),j=S(a.customTimeRange.end);return L.isValid&&j.isValid}return a.selectionType==="custom"&&!a.customTimeRange},[a,S]),_=b.useCallback(L=>te.getSession(L),[]),z=b.useCallback(L=>te.getMacroPeriod(L),[]),$=b.useCallback(L=>te.convertLegacySession(L),[]);return{selection:a,selectSession:y,selectMacro:g,selectCustomRange:x,clearSelection:C,validateTime:S,isValidSelection:w,availableSessions:d,availableMacros:m,hierarchicalOptions:h,currentSession:f,isCurrentSessionActive:l,getSessionDetails:_,getMacroDetails:z,convertLegacySession:$}},wi=s.div.withConfig({displayName:"Container",componentId:"sc-1reqqnl-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Ci=s.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1reqqnl-1"})(["position:relative;border:1px solid ",";border-radius:",";background:",";transition:all 0.2s ease;opacity:",";pointer-events:",";&:hover{border-color:","40;}&:focus-within{border-color:",";box-shadow:0 0 0 3px ","20;}"],({theme:e,hasError:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.error)||"#ef4444":((n=e.colors)==null?void 0:n.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({disabled:e})=>e?.6:1,({disabled:e})=>e?"none":"auto",({theme:e,hasError:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.error)||"#ef4444":((n=e.colors)==null?void 0:n.primary)||"#dc2626"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),Ti=s.div.withConfig({displayName:"SelectedValue",componentId:"sc-1reqqnl-2"})(["padding:",";color:",";font-size:",";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.md)||"1rem"}),Ii=s.div.withConfig({displayName:"DropdownIcon",componentId:"sc-1reqqnl-3"})(["transition:transform 0.2s ease;transform:",";color:",";"],({isOpen:e})=>e?"rotate(180deg)":"rotate(0deg)",({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Ei=s.div.withConfig({displayName:"DropdownMenu",componentId:"sc-1reqqnl-4"})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:",";border:1px solid ",";border-radius:",";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({isOpen:e})=>e?"block":"none"),ji=s.div.withConfig({displayName:"MultiSessionGroup",componentId:"sc-1reqqnl-5"})(["border-bottom:1px solid ",";background:",";&:last-child{border-bottom:none;}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"}),Ni=s.div.withConfig({displayName:"MultiSessionHeader",componentId:"sc-1reqqnl-6"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ",";&:hover{background:","40;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e,isSelected:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((n=e.colors)==null?void 0:n.surface)||"#1f2937"},({theme:e,isSelected:t})=>{var r;return t?"#ffffff":((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.warning)||"#f59e0b"},({theme:e,isSelected:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"}),ki=s.div.withConfig({displayName:"MultiSessionIndicator",componentId:"sc-1reqqnl-7"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.warning)||"#f59e0b"}),Li=s.div.withConfig({displayName:"SessionGroup",componentId:"sc-1reqqnl-8"})(["border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),Ri=s.div.withConfig({displayName:"SessionHeader",componentId:"sc-1reqqnl-9"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:","40;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e,isSelected:t})=>{var r;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":"transparent"},({theme:e,isSelected:t})=>{var r;return t?"#ffffff":((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e,isSelected:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"}),_i=s.div.withConfig({displayName:"MacroList",componentId:"sc-1reqqnl-10"})(["background:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"}),Mi=s.div.withConfig({displayName:"MacroItem",componentId:"sc-1reqqnl-11"})(["padding:"," ",";color:",";cursor:pointer;font-size:",";transition:all 0.2s ease;border-left:3px solid ",";&:hover{background:","20;color:",";}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e,isSelected:t})=>{var r,n;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((n=e.colors)==null?void 0:n.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e,isSelected:t})=>{var r;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":"transparent"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),Pi=s.div.withConfig({displayName:"CurrentSessionIndicator",componentId:"sc-1reqqnl-12"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.success)||"#10b981"}),Di=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1reqqnl-13"})(["color:",";font-size:",";margin-top:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#ef4444"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),$i=({value:e,onChange:t,showMacroPeriods:r=!0,showCurrentSession:n=!0,allowCustomRange:i=!1,placeholder:c="Select session or macro period",disabled:a=!1,error:p,className:f})=>{const[l,d]=b.useState(!1),{hierarchicalOptions:m,currentSession:h,isCurrentSessionActive:y,selectSession:g,selectMacro:x}=Pr({onSelectionChange:t}),C=b.useMemo(()=>te.getMultiSessionMacros(),[]),S=b.useMemo(()=>e!=null&&e.displayLabel?e.displayLabel:c,[e,c]),w=j=>{g(j),d(!1)},_=j=>{x(j),d(!1)},z=j=>(e==null?void 0:e.session)===j&&(e==null?void 0:e.selectionType)==="session",$=j=>(e==null?void 0:e.macroPeriod)===j&&(e==null?void 0:e.selectionType)==="macro",L=j=>(h==null?void 0:h.session)===j;return o.jsxs(wi,{className:f,hasError:!!p,children:[o.jsxs(Ci,{hasError:!!p,disabled:a,onClick:()=>!a&&d(!l),children:[o.jsxs(Ti,{children:[o.jsx("span",{children:S}),o.jsx(Ii,{isOpen:l,children:"▼"})]}),o.jsxs(Ei,{isOpen:l,children:[r&&C.length>0&&o.jsx(ji,{children:C.map(j=>o.jsxs(Ni,{isSelected:$(j.type),onClick:R=>{R.stopPropagation(),_(j.type)},children:[o.jsx("span",{children:j.name}),o.jsx(ki,{children:"🌐 MULTI-SESSION"})]},j.type))}),m.map(({session:j,sessionLabel:R,macros:N})=>o.jsxs(Li,{children:[o.jsxs(Ri,{isSelected:z(j),onClick:U=>{U.stopPropagation(),w(j)},children:[o.jsx("span",{children:R}),n&&L(j)&&o.jsx(Pi,{children:"🔴 LIVE"})]}),r&&N.length>0&&o.jsx(_i,{children:N.map(({value:U,label:Y})=>o.jsxs(Mi,{isSelected:$(U),onClick:k=>{k.stopPropagation(),_(U)},children:[Y,te.hasSubPeriods(U)&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.75rem",opacity:.7},children:"📋 Has sub-periods"})]},U))})]},j))]})]}),p&&o.jsx(Di,{children:p})]})},I={DATE:"date",SYMBOL:"symbol",DIRECTION:"direction",MODEL_TYPE:"model_type",SESSION:"session",ENTRY_PRICE:"entry_price",EXIT_PRICE:"exit_price",R_MULTIPLE:"r_multiple",ACHIEVED_PL:"achieved_pl",WIN_LOSS:"win_loss",PATTERN_QUALITY:"pattern_quality_rating",ENTRY_TIME:"entry_time",EXIT_TIME:"exit_time"},St=s.span.withConfig({displayName:"ProfitLossCell",componentId:"sc-14bks31-0"})(["color:",";font-weight:",";"],({isProfit:e,theme:t})=>e?t.colors.success||"#10b981":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),Dr=s(ze).withConfig({displayName:"DirectionBadge",componentId:"sc-14bks31-1"})(["background-color:",";color:white;"],({direction:e,theme:t})=>e==="Long"?t.colors.success||"#10b981":t.colors.error||"#ef4444"),$r=s.span.withConfig({displayName:"QualityRating",componentId:"sc-14bks31-2"})(["color:",";font-weight:",";"],({rating:e,theme:t})=>e>=4?t.colors.success||"#10b981":e>=3?t.colors.warning||"#f59e0b":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),wt=s.span.withConfig({displayName:"RMultipleCell",componentId:"sc-14bks31-3"})(["color:",";font-weight:",";"],({rMultiple:e,theme:t})=>e>0?t.colors.success||"#10b981":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),$e=e=>e==null?"-":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e),Ct=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return e}},xt=e=>e||"-",Or=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ct(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"80px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Direction",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Dr,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION]})},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.ENTRY_PRICE,header:"Entry",sortable:!0,width:"100px",align:"right",cell:e=>$e(e.trade[I.ENTRY_PRICE])},{id:I.EXIT_PRICE,header:"Exit",sortable:!0,width:"100px",align:"right",cell:e=>$e(e.trade[I.EXIT_PRICE])},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(wt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${e.trade[I.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(St,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:$e(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(ze,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx($r,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.ENTRY_TIME,header:"Entry Time",sortable:!0,width:"100px",align:"center",cell:e=>xt(e.trade[I.ENTRY_TIME])},{id:I.EXIT_TIME,header:"Exit Time",sortable:!0,width:"100px",align:"center",cell:e=>xt(e.trade[I.EXIT_TIME])}],zr=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"90px",cell:e=>Ct(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"60px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Dir",sortable:!0,width:"50px",align:"center",cell:e=>o.jsx(Dr,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION].charAt(0)})},{id:I.R_MULTIPLE,header:"R",sortable:!0,width:"60px",align:"right",cell:e=>o.jsx(wt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${e.trade[I.R_MULTIPLE].toFixed(1)}R`:"-"})},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"80px",align:"right",cell:e=>o.jsx(St,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:$e(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"60px",align:"center",cell:e=>o.jsx(ze,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]==="Win"?"W":e.trade[I.WIN_LOSS]==="Loss"?"L":"-"})}],Ar=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ct(e.trade[I.DATE])},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(wt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${e.trade[I.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(St,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:$e(e.trade[I.ACHIEVED_PL])})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx($r,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(ze,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})}],Oi=s.tr.withConfig({displayName:"TableRow",componentId:"sc-uyrnn-0"})([""," "," "," "," ",""],({striped:e,theme:t,isSelected:r})=>{var n;return e&&!r&&s.css(["&:nth-child(even){background-color:","50;}"],((n=t.colors)==null?void 0:n.background)||"#f8f9fa")},({hoverable:e,theme:t,isSelected:r})=>{var n;return e&&!r&&s.css(["&:hover{background-color:","aa;}"],((n=t.colors)==null?void 0:n.background)||"#f8f9fa")},({isSelected:e,theme:t})=>{var r;return e&&s.css(["background-color:","15;"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")},({isClickable:e})=>e&&s.css(["cursor:pointer;"]),({isExpanded:e,theme:t})=>{var r;return e&&s.css(["border-bottom:2px solid ",";"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")}),rr=s.td.withConfig({displayName:"TableCell",componentId:"sc-uyrnn-1"})(["text-align:",";border-bottom:1px solid ",";color:",";padding:"," ",";vertical-align:middle;"],({align:e})=>e||"left",({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#111827"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"}),zi=s.tr.withConfig({displayName:"ExpandedRow",componentId:"sc-uyrnn-2"})(["display:",";"],({isVisible:e})=>e?"table-row":"none"),Ai=s.td.withConfig({displayName:"ExpandedCell",componentId:"sc-uyrnn-3"})(["padding:0;border-bottom:1px solid ",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),Fi=s.div.withConfig({displayName:"ExpandedContent",componentId:"sc-uyrnn-4"})(["padding:",";background-color:","30;border-left:3px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),qi=s.button.withConfig({displayName:"ExpandButton",componentId:"sc-uyrnn-5"})(["background:none;border:none;cursor:pointer;padding:",";color:",";font-size:",";display:flex;align-items:center;justify-content:center;border-radius:",";transition:all 0.2s ease;&:hover{background-color:",";color:",";}&:focus{outline:2px solid ",";outline-offset:2px;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),Bi=s.span.withConfig({displayName:"ExpandIcon",componentId:"sc-uyrnn-6"})(["display:inline-block;transition:transform 0.2s ease;transform:",";&::after{content:'▶';}"],({isExpanded:e})=>e?"rotate(90deg)":"rotate(0deg)"),Hi=s.div.withConfig({displayName:"TradeDetails",componentId:"sc-uyrnn-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"}),Me=s.div.withConfig({displayName:"DetailGroup",componentId:"sc-uyrnn-8"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),Pe=s.span.withConfig({displayName:"DetailLabel",componentId:"sc-uyrnn-9"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),se=s.span.withConfig({displayName:"DetailValue",componentId:"sc-uyrnn-10"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#111827"}),Ui=({trade:e})=>o.jsxs(Hi,{children:[e.fvg_details&&o.jsxs(Me,{children:[o.jsx(Pe,{children:"FVG Details"}),o.jsxs(se,{children:["Type: ",e.fvg_details.rd_type||"-"]}),o.jsxs(se,{children:["Entry Version: ",e.fvg_details.entry_version||"-"]}),o.jsxs(se,{children:["Draw on Liquidity: ",e.fvg_details.draw_on_liquidity||"-"]})]}),e.setup&&o.jsxs(Me,{children:[o.jsx(Pe,{children:"Setup Classification"}),o.jsxs(se,{children:["Primary: ",e.setup.primary_setup||"-"]}),o.jsxs(se,{children:["Secondary: ",e.setup.secondary_setup||"-"]}),o.jsxs(se,{children:["Liquidity: ",e.setup.liquidity_taken||"-"]})]}),e.analysis&&o.jsxs(Me,{children:[o.jsx(Pe,{children:"Analysis"}),o.jsxs(se,{children:["DOL Target: ",e.analysis.dol_target_type||"-"]}),o.jsxs(se,{children:["Path Quality: ",e.analysis.path_quality||"-"]}),o.jsxs(se,{children:["Clustering: ",e.analysis.clustering||"-"]})]}),o.jsxs(Me,{children:[o.jsx(Pe,{children:"Timing"}),o.jsxs(se,{children:["Entry: ",e.trade.entry_time||"-"]}),o.jsxs(se,{children:["Exit: ",e.trade.exit_time||"-"]}),o.jsxs(se,{children:["FVG: ",e.trade.fvg_time||"-"]}),o.jsxs(se,{children:["RD: ",e.trade.rd_time||"-"]})]}),e.trade.notes&&o.jsxs(Me,{style:{gridColumn:"1 / -1"},children:[o.jsx(Pe,{children:"Notes"}),o.jsx(se,{children:e.trade.notes})]})]}),Fr=({trade:e,index:t,columns:r,isSelected:n=!1,hoverable:i=!0,striped:c=!0,expandable:a=!1,isExpanded:p=!1,onRowClick:f,onToggleExpand:l,expandedContent:d})=>{const[m,h]=b.useState(!1),y=p!==void 0?p:m,g=S=>{S.target.closest("button")||f==null||f(e,t)},x=S=>{S.stopPropagation(),l?l(e,t):h(!m)},C=r.filter(S=>!S.hidden);return o.jsxs(o.Fragment,{children:[o.jsxs(Oi,{hoverable:i,striped:c,isSelected:n,isClickable:!!f,isExpanded:y,onClick:g,children:[a&&o.jsx(rr,{align:"center",style:{width:"40px",padding:"8px"},children:o.jsx(qi,{onClick:x,children:o.jsx(Bi,{isExpanded:y})})}),C.map(S=>o.jsx(rr,{align:S.align,children:S.cell(e,t)},S.id))]}),a&&o.jsx(zi,{isVisible:y,children:o.jsx(Ai,{colSpan:C.length+1,children:o.jsx(Fi,{children:d||o.jsx(Ui,{trade:e})})})})]})},ue={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",DATE_FROM:"dateFrom",DATE_TO:"dateTo",SESSION:"session",DIRECTION:"direction",MARKET:"market",MIN_R_MULTIPLE:"min_r_multiple",MAX_R_MULTIPLE:"max_r_multiple",MIN_PATTERN_QUALITY:"min_pattern_quality",MAX_PATTERN_QUALITY:"max_pattern_quality"},Yi=s.div.withConfig({displayName:"FiltersContainer",componentId:"sc-32k3gq-0"})(["display:flex;flex-direction:column;gap:",";padding:",";background-color:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),or=s.div.withConfig({displayName:"FilterRow",componentId:"sc-32k3gq-1"})(["display:flex;gap:",";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"}),ge=s.div.withConfig({displayName:"FilterGroup",componentId:"sc-32k3gq-2"})(["display:flex;flex-direction:column;gap:",";min-width:120px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),me=s.label.withConfig({displayName:"FilterLabel",componentId:"sc-32k3gq-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),Vi=s.div.withConfig({displayName:"FilterActions",componentId:"sc-32k3gq-4"})(["display:flex;gap:",";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"}),Wi=s.div.withConfig({displayName:"AdvancedFilters",componentId:"sc-32k3gq-5"})(["display:",";flex-direction:column;gap:",";padding-top:",";border-top:1px solid ",";"],({isVisible:e})=>e?"flex":"none",({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),nr=s.div.withConfig({displayName:"RangeInputGroup",componentId:"sc-32k3gq-6"})(["display:flex;gap:",";align-items:center;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),sr=s.span.withConfig({displayName:"RangeLabel",componentId:"sc-32k3gq-7"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),qr=({filters:e,onFiltersChange:t,onReset:r,isLoading:n=!1,showAdvanced:i=!1,onToggleAdvanced:c})=>{const a=(l,d)=>{t({...e,[l]:d})},p=()=>{t({}),r==null||r()},f=Object.values(e).some(l=>l!==void 0&&l!==""&&l!==null);return o.jsxs(Yi,{children:[o.jsxs(or,{children:[o.jsxs(ge,{children:[o.jsx(me,{children:"Date From"}),o.jsx(be,{type:"date",value:e.dateFrom||"",onChange:l=>a(ue.DATE_FROM,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Date To"}),o.jsx(be,{type:"date",value:e.dateTo||"",onChange:l=>a(ue.DATE_TO,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Model Type"}),o.jsx(Te,{options:[{value:"",label:"All Models"},{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"True-RD",label:"True-RD"},{value:"IMM-RD",label:"IMM-RD"},{value:"Dispersed-RD",label:"Dispersed-RD"},{value:"Wide-Gap-RD",label:"Wide-Gap-RD"}],value:e.model_type||"",onChange:l=>a(ue.MODEL_TYPE,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Session"}),o.jsx(Te,{options:[{value:"",label:"All Sessions"},{value:"Pre-Market",label:"Pre-Market"},{value:"NY Open",label:"NY Open"},{value:"10:50-11:10",label:"10:50-11:10"},{value:"11:50-12:10",label:"11:50-12:10"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"13:50-14:10",label:"13:50-14:10"},{value:"14:50-15:10",label:"14:50-15:10"},{value:"15:15-15:45",label:"15:15-15:45"},{value:"MOC",label:"MOC"},{value:"Post MOC",label:"Post MOC"}],value:e.session||"",onChange:l=>a(ue.SESSION,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Direction"}),o.jsx(Te,{options:[{value:"",label:"All Directions"},{value:"Long",label:"Long"},{value:"Short",label:"Short"}],value:e.direction||"",onChange:l=>a(ue.DIRECTION,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Result"}),o.jsx(Te,{options:[{value:"",label:"All Results"},{value:"Win",label:"Win"},{value:"Loss",label:"Loss"}],value:e.win_loss||"",onChange:l=>a(ue.WIN_LOSS,l),disabled:n})]}),o.jsxs(Vi,{children:[c&&o.jsxs(ie,{variant:"outline",size:"small",onClick:c,disabled:n,children:[i?"Hide":"Show"," Advanced"]}),o.jsx(ie,{variant:"outline",size:"small",onClick:p,disabled:n||!f,children:"Reset"})]})]}),o.jsx(Wi,{isVisible:i,children:o.jsxs(or,{children:[o.jsxs(ge,{children:[o.jsx(me,{children:"Market"}),o.jsx(Te,{options:[{value:"",label:"All Markets"},{value:"MNQ",label:"MNQ"},{value:"NQ",label:"NQ"},{value:"ES",label:"ES"},{value:"MES",label:"MES"},{value:"YM",label:"YM"},{value:"MYM",label:"MYM"}],value:e.market||"",onChange:l=>a(ue.MARKET,l),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"R Multiple Range"}),o.jsxs(nr,{children:[o.jsx(be,{type:"number",placeholder:"Min",step:"0.1",value:e.min_r_multiple||"",onChange:l=>a(ue.MIN_R_MULTIPLE,l?Number(l):void 0),disabled:n,style:{width:"80px"}}),o.jsx(sr,{children:"to"}),o.jsx(be,{type:"number",placeholder:"Max",step:"0.1",value:e.max_r_multiple||"",onChange:l=>a(ue.MAX_R_MULTIPLE,l?Number(l):void 0),disabled:n,style:{width:"80px"}})]})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Pattern Quality Range"}),o.jsxs(nr,{children:[o.jsx(be,{type:"number",placeholder:"Min",min:"1",max:"5",step:"0.1",value:e.min_pattern_quality||"",onChange:l=>a(ue.MIN_PATTERN_QUALITY,l?Number(l):void 0),disabled:n,style:{width:"80px"}}),o.jsx(sr,{children:"to"}),o.jsx(be,{type:"number",placeholder:"Max",min:"1",max:"5",step:"0.1",value:e.max_pattern_quality||"",onChange:l=>a(ue.MAX_PATTERN_QUALITY,l?Number(l):void 0),disabled:n,style:{width:"80px"}})]})]})]})})]})},Gi=s.div.withConfig({displayName:"TableContainer",componentId:"sc-13oxwmo-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),Ki=s.table.withConfig({displayName:"StyledTable",componentId:"sc-13oxwmo-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({bordered:e,theme:t})=>{var r,n;return e&&s.css(["border:1px solid ",";border-radius:",";"],((r=t.colors)==null?void 0:r.border)||"#e5e7eb",((n=t.borderRadius)==null?void 0:n.sm)||"4px")},({compact:e,theme:t})=>{var r,n,i,c;return e?s.css(["th,td{padding:"," ",";}"],((r=t.spacing)==null?void 0:r.xs)||"8px",((n=t.spacing)==null?void 0:n.sm)||"12px"):s.css(["th,td{padding:"," ",";}"],((i=t.spacing)==null?void 0:i.sm)||"12px",((c=t.spacing)==null?void 0:c.md)||"16px")}),Qi=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-13oxwmo-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),Xi=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-13oxwmo-3"})(["background-color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"}),ir=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13oxwmo-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"},({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:t})=>{var r;return((r=t.colors)==null?void 0:r.background)||"#f8f9fa"}),({isSorted:e,theme:t})=>{var r;return e&&s.css(["color:",";"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")}),Ji=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13oxwmo-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"},({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),Zi=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13oxwmo-6"})([""]),ea=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13oxwmo-7"})(["padding:",";text-align:center;color:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),ta=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-13oxwmo-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>{var t;return`${((t=e.colors)==null?void 0:t.background)||"#ffffff"}80`}),ra=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-13oxwmo-9"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),oa=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-13oxwmo-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"}),na=s.div.withConfig({displayName:"PageInfo",componentId:"sc-13oxwmo-11"})(["color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),sa=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-13oxwmo-12"})(["display:flex;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),ia=({data:e,isLoading:t=!1,bordered:r=!0,striped:n=!0,hoverable:i=!0,compact:c=!1,stickyHeader:a=!1,height:p="",onRowClick:f,isRowSelected:l,onSort:d,sortColumn:m="",sortDirection:h="asc",pagination:y=!1,currentPage:g=1,pageSize:x=10,totalRows:C=0,onPageChange:S,onPageSizeChange:w,className:_="",emptyMessage:z="No trades available",scrollable:$=!0,showFilters:L=!1,filters:j={},onFiltersChange:R,columnPreset:N="default",customColumns:U,expandableRows:Y=!1,renderExpandedContent:k})=>{const[H,ee]=b.useState(!1),re=b.useMemo(()=>{if(U)return U;switch(N){case"compact":return zr();case"performance":return Ar();default:return Or()}},[U,N]),de=b.useMemo(()=>re.filter(B=>!B.hidden),[re]),oe=b.useMemo(()=>Math.ceil(C/x),[C,x]),D=b.useMemo(()=>{if(!y)return e;const B=(g-1)*x,pe=B+x;return C>0&&e.length<=x?e:e.slice(B,pe)},[e,y,g,x,C]),X=B=>{if(!d)return;d(B,m===B&&h==="asc"?"desc":"asc")},xe=B=>{B<1||B>oe||!S||S(B)};return o.jsxs("div",{children:[L&&R&&o.jsx(qr,{filters:j,onFiltersChange:R,isLoading:t,showAdvanced:H,onToggleAdvanced:()=>ee(!H)}),o.jsxs("div",{style:{position:"relative"},children:[t&&o.jsx(ta,{children:o.jsx(ra,{})}),o.jsx(Gi,{height:p,scrollable:$,children:o.jsxs(Ki,{bordered:r,striped:n,compact:c,className:_,children:[o.jsx(Qi,{stickyHeader:a,children:o.jsxs(Xi,{children:[Y&&o.jsx(ir,{width:"40px",align:"center"}),de.map(B=>o.jsxs(ir,{sortable:B.sortable,isSorted:m===B.id,align:B.align,width:B.width,onClick:()=>B.sortable&&X(B.id),children:[B.header,B.sortable&&o.jsx(Ji,{direction:m===B.id?h:void 0})]},B.id))]})}),o.jsx(Zi,{children:D.length>0?D.map((B,pe)=>o.jsx(Fr,{trade:B,index:pe,columns:de,isSelected:l?l(B,pe):!1,hoverable:i,striped:n,expandable:Y,onRowClick:f,expandedContent:k==null?void 0:k(B)},B.trade.id||pe)):o.jsx("tr",{children:o.jsx("td",{colSpan:de.length+(Y?1:0),children:o.jsx(ea,{children:z})})})})]})}),y&&oe>0&&o.jsxs(oa,{children:[o.jsxs(na,{children:["Showing ",Math.min((g-1)*x+1,C)," to"," ",Math.min(g*x,C)," of ",C," entries"]}),o.jsxs(sa,{children:[o.jsx(ie,{size:"small",variant:"outline",onClick:()=>xe(1),disabled:g===1,children:"First"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>xe(g-1),disabled:g===1,children:"Prev"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>xe(g+1),disabled:g===oe,children:"Next"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>xe(oe),disabled:g===oe,children:"Last"})]})]})]})]})},aa=s.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),ca=({title:e,children:t,isLoading:r=!1,hasError:n=!1,errorMessage:i="An error occurred while loading data",showRetry:c=!0,onRetry:a,isEmpty:p=!1,emptyMessage:f="No data available",emptyActionText:l,onEmptyAction:d,actionButton:m,className:h,...y})=>{const g=o.jsx(aa,{children:m});let x;return r?x=o.jsx(Cr,{variant:"card",text:"Loading data..."}):n?x=o.jsx(ht,{title:"Error",description:i,variant:"compact",actionText:c?"Retry":void 0,onAction:c?a:void 0}):p?x=o.jsx(ht,{title:"No Data",description:f,variant:"compact",actionText:l,onAction:d}):x=t,o.jsx(Nr,{title:e,actions:g,className:h,...y,children:x})},la=s.div.withConfig({displayName:"SectionContainer",componentId:"sc-14y246p-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg),da=s.div.withConfig({displayName:"SectionHeader",componentId:"sc-14y246p-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border),ua=s.h2.withConfig({displayName:"SectionTitle",componentId:"sc-14y246p-2"})(["color:",";font-size:",";font-weight:600;margin:0;"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),pa=s.div.withConfig({displayName:"SectionActions",componentId:"sc-14y246p-3"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),fa=s.div.withConfig({displayName:"SectionContent",componentId:"sc-14y246p-4"})(["min-height:200px;"]),ar=s.div.withConfig({displayName:"LoadingState",componentId:"sc-14y246p-5"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),ga=s.div.withConfig({displayName:"ErrorState",componentId:"sc-14y246p-6"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";text-align:center;"],({theme:e})=>e.colors.danger),ma=({name:e,title:t,children:r,actions:n,isLoading:i=!1,error:c=null,className:a,collapsible:p=!1,defaultCollapsed:f=!1})=>{const[l,d]=b.useState(f),m=()=>{p&&d(!l)},h=t||e.charAt(0).toUpperCase()+e.slice(1),y=()=>c?o.jsx(ga,{children:o.jsxs("div",{children:[o.jsxs("div",{children:["Error loading ",e]}),o.jsx("div",{style:{fontSize:"0.9em",marginTop:"8px"},children:c})]})}):i?o.jsxs(ar,{children:["Loading ",e,"..."]}):r||o.jsxs(ar,{children:["No ",e," data available"]});return o.jsxs(la,{className:a,"data-section":e,children:[o.jsxs(da,{children:[o.jsxs(ua,{onClick:m,style:{cursor:p?"pointer":"default"},children:[h,p&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.8em"},children:l?"▶":"▼"})]}),n&&o.jsx(pa,{children:n})]}),!l&&o.jsx(fa,{children:y()})]})},ha=ma,xa=s.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),ba=s.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),ya=s.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),va=s.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),Sa=({header:e,sidebar:t,children:r,sidebarCollapsed:n=!1,className:i})=>o.jsxs(xa,{sidebarCollapsed:n,className:i,children:[o.jsx(ba,{children:e}),o.jsx(ya,{collapsed:n,children:t}),o.jsx(va,{children:r})]}),wa=s.div.withConfig({displayName:"BuilderContainer",componentId:"sc-5duzr2-0"})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]),Ca=s.h3.withConfig({displayName:"SectionTitle",componentId:"sc-5duzr2-1"})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]),Ta=s.div.withConfig({displayName:"MatrixGrid",componentId:"sc-5duzr2-2"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]),Ve=s.div.withConfig({displayName:"ElementSection",componentId:"sc-5duzr2-3"})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]),De=s.h4.withConfig({displayName:"ElementTitle",componentId:"sc-5duzr2-4"})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]),We=s.select.withConfig({displayName:"Select",componentId:"sc-5duzr2-5"})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]),Ia=s.div.withConfig({displayName:"PreviewContainer",componentId:"sc-5duzr2-6"})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]),Ea=s.div.withConfig({displayName:"PreviewText",componentId:"sc-5duzr2-7"})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]),cr=s.span.withConfig({displayName:"RequiredIndicator",componentId:"sc-5duzr2-8"})(["color:#dc2626;margin-left:4px;"]),lr=s.span.withConfig({displayName:"OptionalIndicator",componentId:"sc-5duzr2-9"})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]),ja=({onSetupChange:e,initialComponents:t})=>{const[r,n]=b.useState({constant:(t==null?void 0:t.constant)||"",action:(t==null?void 0:t.action)||"None",variable:(t==null?void 0:t.variable)||"None",entry:(t==null?void 0:t.entry)||""});b.useEffect(()=>{r.constant&&r.entry&&e(r)},[r,e]);const i=(a,p)=>{n(f=>({...f,[a]:p}))},c=()=>{const{constant:a,action:p,variable:f,entry:l}=r;if(!a||!l)return"Select required elements to see setup preview...";let d=a;return p&&p!=="None"&&(d+=` → ${p}`),f&&f!=="None"&&(d+=` → ${f}`),d+=` [${l}]`,d};return o.jsxs(wa,{children:[o.jsx(Ca,{children:"Setup Construction Matrix"}),o.jsxs(Ta,{children:[o.jsxs(Ve,{children:[o.jsxs(De,{children:["Constant Element",o.jsx(cr,{children:"*"})]}),o.jsxs(We,{value:r.constant,onChange:a=>i("constant",a.target.value),children:[o.jsx("option",{value:"",children:"Select Constant"}),Ce.constant.parentArrays.map(a=>o.jsx("option",{value:a,children:a},a)),Ce.constant.fvgTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Ve,{children:[o.jsxs(De,{children:["Action Element",o.jsx(lr,{children:"(optional)"})]}),o.jsxs(We,{value:r.action,onChange:a=>i("action",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),Ce.action.liquidityEvents.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Ve,{children:[o.jsxs(De,{children:["Variable Element",o.jsx(lr,{children:"(optional)"})]}),o.jsxs(We,{value:r.variable,onChange:a=>i("variable",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),Ce.variable.rdTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Ve,{children:[o.jsxs(De,{children:["Entry Method",o.jsx(cr,{children:"*"})]}),o.jsxs(We,{value:r.entry,onChange:a=>i("entry",a.target.value),children:[o.jsx("option",{value:"",children:"Select Entry Method"}),Ce.entry.methods.map(a=>o.jsx("option",{value:a,children:a},a))]})]})]}),o.jsxs(Ia,{children:[o.jsx(De,{children:"Setup Preview"}),o.jsx(Ea,{children:c()})]})]})},Na=ja,dr=s.div.withConfig({displayName:"MetricsContainer",componentId:"sc-opkdti-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),ur=s.div.withConfig({displayName:"MetricCard",componentId:"sc-opkdti-1"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md),pr=s.div.withConfig({displayName:"MetricLabel",componentId:"sc-opkdti-2"})(["color:",";font-size:",";margin-bottom:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs),fr=s.div.withConfig({displayName:"MetricValue",componentId:"sc-opkdti-3"})(["color:",";font-size:",";font-weight:600;"],({theme:e,positive:t,negative:r})=>t?e.colors.success:r?e.colors.danger:e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),ka=({metrics:e,isLoading:t})=>t?o.jsx(dr,{children:Array.from({length:4}).map((r,n)=>o.jsxs(ur,{children:[o.jsx(pr,{children:"Loading..."}),o.jsx(fr,{children:"--"})]},n))}):o.jsx(dr,{children:e.map((r,n)=>o.jsxs(ur,{children:[o.jsx(pr,{children:r.label}),o.jsx(fr,{positive:r.positive,negative:r.negative,children:r.value})]},n))}),La=ka,Ra=s.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-tp1ymt-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg),_a=s.h3.withConfig({displayName:"AnalysisTitle",componentId:"sc-tp1ymt-1"})(["color:",";font-size:",";font-weight:600;margin-bottom:",";"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg,({theme:e})=>e.spacing.md),Ma=s.div.withConfig({displayName:"AnalysisContent",componentId:"sc-tp1ymt-2"})(["color:",";line-height:1.6;"],({theme:e})=>e.colors.textSecondary),Pa=({title:e="Trade Analysis",children:t,isLoading:r})=>o.jsxs(Ra,{children:[o.jsx(_a,{children:e}),o.jsx(Ma,{children:r?o.jsx("div",{children:"Loading analysis..."}):t||o.jsx("div",{children:"No analysis data available"})})]}),Da=Pa,E={f1Red:"#dc2626",f1RedDark:"#b91c1c",f1RedLight:"#ef4444",f1Blue:"#1e5bc6",f1BlueDark:"#1a4da8",f1BlueLight:"#4a7dd8",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},F={background:"#0f0f0f",surface:"#1a1a1a",cardBackground:"#1a1a1a",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:E.green,warning:E.yellow,error:E.red,info:E.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:E.f1Red,profit:E.green,loss:E.red,neutral:E.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},K={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:E.green,warning:E.yellow,error:E.red,info:E.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:E.f1Red,profit:E.green,loss:E.red,neutral:E.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},Q={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},le={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},Qe={light:300,regular:400,medium:500,semibold:600,bold:700},Xe={tight:1.25,normal:1.5,relaxed:1.75},Je={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},Ze={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},et={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},tt={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},rt={fast:"0.1s",normal:"0.3s",slow:"0.5s"},ot={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},$a=s.div.withConfig({displayName:"HeaderContainer",componentId:"sc-e71xhh-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid #4b5563;margin-bottom:",";",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["padding:24px 0;margin-bottom:24px;"]);case"form":return s.css(["padding:16px 0;margin-bottom:16px;"]);default:return s.css(["padding:20px 0;margin-bottom:20px;"])}}),Oa=s.div.withConfig({displayName:"TitleSection",componentId:"sc-e71xhh-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),za=s.h1.withConfig({displayName:"MainTitle",componentId:"sc-e71xhh-2"})(["font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"," span{color:",";}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["font-size:",";"],le.xxxl);case"analysis":return s.css(["font-size:",";"],le.xxl);case"form":return s.css(["font-size:",";"],le.xl);default:return s.css(["font-size:",";"],le.xxl)}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),Aa=s.div.withConfig({displayName:"Subtitle",componentId:"sc-e71xhh-3"})(["font-size:",";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"],le.sm),Fa=s.div.withConfig({displayName:"ActionsSection",componentId:"sc-e71xhh-4"})(["display:flex;align-items:center;gap:",";flex-wrap:wrap;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),qa=s.div.withConfig({displayName:"StatusIndicator",componentId:"sc-e71xhh-5"})(["display:flex;align-items:center;gap:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({$isLive:e,$variant:t})=>e?s.css(["background:rgba(220,38,38,0.1);border:1px solid #dc2626;color:#dc2626;"]):t==="active"?s.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]):s.css(["background:rgba(156,163,175,0.1);border:1px solid #9ca3af;color:#9ca3af;"])),Ba=s.div.withConfig({displayName:"StatusDot",componentId:"sc-e71xhh-6"})(["width:6px;height:6px;border-radius:50%;background:",";",""],({$isLive:e})=>e?"#dc2626":"#22c55e",({$isLive:e})=>e&&s.css(["animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"])),Ha=s.button.withConfig({displayName:"RefreshButton",componentId:"sc-e71xhh-7"})(["padding:"," ",";background:transparent;color:",";border:1px solid #4b5563;border-radius:",";cursor:pointer;font-weight:500;font-size:",";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:",";border-color:",";}&:disabled{opacity:0.6;cursor:not-allowed;}",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({$isRefreshing:e})=>e&&s.css(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])),Ua=s.div.withConfig({displayName:"CustomActions",componentId:"sc-e71xhh-8"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Ya=e=>{const{title:t,subtitle:r,isLive:n=!1,liveText:i="LIVE SESSION",statusText:c,onRefresh:a,isRefreshing:p=!1,actions:f,variant:l="dashboard",className:d}=e,m=n?i:c;return o.jsxs($a,{$variant:l,className:d,children:[o.jsxs(Oa,{children:[o.jsx(za,{$variant:l,children:l==="dashboard"?o.jsxs(o.Fragment,{children:["🏎️ ",t.replace("Trading","TRADING").replace("Dashboard","DASHBOARD")]}):t}),r&&o.jsx(Aa,{children:r})]}),o.jsxs(Fa,{children:[m&&o.jsxs(qa,{$isLive:n,$variant:!n&&c?"active":void 0,children:[o.jsx(Ba,{$isLive:n}),m]}),a&&o.jsx(Ha,{onClick:a,disabled:p,$isRefreshing:p,children:p?"Refreshing...":"Refresh"}),f&&o.jsx(Ua,{children:f})]})]})},gt=s.div.withConfig({displayName:"Container",componentId:"sc-vuv4tf-0"})(["display:flex;flex-direction:column;width:100%;max-width:",";margin:0 auto;min-height:",";"," "," "," ",""],({$maxWidth:e})=>typeof e=="number"?`${e}px`:e,({$variant:e})=>e==="dashboard"?"100vh":"auto",({$padding:e})=>{const t={sm:Q.sm,md:Q.md,lg:Q.lg,xl:Q.xl};return s.css(["padding:",";"],t[e||"lg"])},({$background:e,theme:t})=>{const r={default:t.colors.background,surface:t.colors.surface,elevated:t.colors.elevated};return s.css(["background:",";"],r[e||"default"])},({$variant:e})=>{switch(e){case"dashboard":return s.css(["gap:24px;padding-top:0;"]);case"form":return s.css(["gap:16px;max-width:800px;"]);case"analysis":return s.css(["gap:20px;max-width:1400px;"]);case"settings":return s.css(["gap:16px;max-width:1000px;"]);default:return s.css(["gap:16px;"])}},({$animated:e})=>e&&s.css(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])),Va=s.div.withConfig({displayName:"LoadingContainer",componentId:"sc-vuv4tf-1"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]),Wa=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-vuv4tf-2"})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),Ga=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-vuv4tf-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]),Ka=s.div.withConfig({displayName:"ErrorIcon",componentId:"sc-vuv4tf-4"})(["font-size:48px;opacity:0.8;"]),Qa=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-vuv4tf-5"})(["font-size:16px;font-weight:500;"]),Xa=s.button.withConfig({displayName:"RetryButton",componentId:"sc-vuv4tf-6"})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]),gr=()=>o.jsxs(Va,{children:[o.jsx(Wa,{}),o.jsx("div",{children:"Loading..."})]}),Ja=({error:e,onRetry:t})=>o.jsxs(Ga,{children:[o.jsx(Ka,{children:"⚠️"}),o.jsx(Qa,{children:e}),t&&o.jsx(Xa,{onClick:t,children:"Retry"})]}),Za=e=>{const{children:t,variant:r="dashboard",maxWidth:n="100%",padding:i="lg",isLoading:c=!1,error:a=null,loadingFallback:p,errorFallback:f,className:l,animated:d=!0,background:m="default"}=e,h={$variant:r,$maxWidth:n,$padding:i,$animated:d,$background:m};return a?o.jsx(gt,{...h,className:l,children:f||o.jsx(Ja,{error:a})}):c?o.jsx(gt,{...h,className:l,children:p||o.jsx(gr,{})}):o.jsx(gt,{...h,className:l,children:o.jsx(b.Suspense,{fallback:p||o.jsx(gr,{}),children:t})})},ec=s.form.withConfig({displayName:"FormContainer",componentId:"sc-1gwzj6e-0"})(["display:flex;flex-direction:column;gap:",";background:",";border-radius:",";border:1px solid ",";position:relative;"," "," ",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({$variant:e})=>{switch(e){case"quick":return s.css(["padding:",";max-width:600px;"],Q.lg);case"detailed":return s.css(["padding:",";max-width:800px;"],Q.xl);case"modal":return s.css(["padding:",";max-width:500px;margin:0 auto;"],Q.lg);case"inline":return s.css(["padding:",";background:transparent;border:none;"],Q.md);default:return s.css(["padding:",";"],Q.lg)}},({$showAccent:e,theme:t})=>{var r,n,i,c,a;return e&&s.css(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,",",",","," );border-radius:"," "," 0 0;}"],((r=t.colors)==null?void 0:r.primary)||"#dc2626",((n=t.colors)==null?void 0:n.primaryDark)||"#b91c1c",((i=t.colors)==null?void 0:i.primary)||"#dc2626",((c=t.borderRadius)==null?void 0:c.lg)||"8px",((a=t.borderRadius)==null?void 0:a.lg)||"8px")},({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),tc=s.div.withConfig({displayName:"FormHeader",componentId:"sc-1gwzj6e-1"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),rc=s.h3.withConfig({displayName:"FormTitle",componentId:"sc-1gwzj6e-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),oc=s.div.withConfig({displayName:"FormSubtitle",componentId:"sc-1gwzj6e-3"})(["font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),nc=s.div.withConfig({displayName:"FormContent",componentId:"sc-1gwzj6e-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),mr=s.div.withConfig({displayName:"FormMessage",componentId:"sc-1gwzj6e-5"})(["padding:"," ",";border-radius:",";font-size:",";font-weight:500;display:flex;align-items:center;gap:",";",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$type:e})=>{switch(e){case"error":return s.css(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);case"success":return s.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);case"info":return s.css(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"])}}),sc=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-1gwzj6e-6"})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:",";z-index:10;"],({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"}),ic=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1gwzj6e-7"})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),ac=s.div.withConfig({displayName:"AutoSaveIndicator",componentId:"sc-1gwzj6e-8"})(["position:absolute;top:8px;right:8px;font-size:",";color:",";opacity:",";transition:opacity 0.3s ease;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({$visible:e})=>e?1:0),cc=e=>{const{children:t,onSubmit:r,title:n,subtitle:i,isSubmitting:c=!1,error:a=null,success:p=null,variant:f="quick",showAccent:l=!0,className:d,disabled:m=!1,autoSave:h=!1,autoSaveInterval:y=3e4}=e,g=async x=>{x.preventDefault(),r&&!c&&!m&&await r(x)};return o.jsxs(ec,{$variant:f,$showAccent:l,$disabled:m,className:d,onSubmit:g,noValidate:!0,children:[c&&o.jsx(sc,{children:o.jsx(ic,{})}),h&&o.jsx(ac,{$visible:!c,children:"Auto-save enabled"}),(n||i)&&o.jsxs(tc,{children:[n&&o.jsx(rc,{children:n}),i&&o.jsx(oc,{children:i})]}),a&&o.jsxs(mr,{$type:"error",children:["⚠️ ",a]}),p&&o.jsxs(mr,{$type:"success",children:["✅ ",p]}),o.jsx(nc,{children:t})]})},lc=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-sq94oz-0"})(["display:flex;flex-direction:column;gap:",";"],({$size:e})=>({sm:Q.xs,md:Q.sm,lg:Q.md})[e||"md"]),dc=s.label.withConfig({displayName:"Label",componentId:"sc-sq94oz-1"})(["font-size:",";font-weight:600;color:",";display:flex;align-items:center;gap:",";"," ",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$variant:e})=>{switch(e){case"trading":return s.css(["text-transform:uppercase;letter-spacing:0.025em;"]);case"analysis":return s.css(["font-weight:500;"]);default:return s.css([""])}},({$required:e,theme:t})=>{var r;return e&&s.css(["&::after{content:'*';color:",";margin-left:2px;}"],((r=t.colors)==null?void 0:r.primary)||"#dc2626")}),uc=s.div.withConfig({displayName:"InputContainer",componentId:"sc-sq94oz-2"})(["position:relative;display:flex;align-items:center;",""],({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),Tt=s.css(["width:100%;border:1px solid ",";border-radius:",";background:",";color:",";font-family:inherit;transition:all 0.2s ease;"," &:focus{outline:none;border-color:",";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"],({$hasError:e,theme:t})=>{var r;return e?((r=t.colors)==null?void 0:r.error)||"#f44336":"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$size:e})=>({sm:s.css(["padding:"," ",";font-size:",";"],Q.xs,Q.sm,le.sm),md:s.css(["padding:"," ",";font-size:",";"],Q.sm,Q.md,le.md),lg:s.css(["padding:"," ",";font-size:",";"],Q.md,Q.lg,le.lg)})[e||"md"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),pc=s.input.withConfig({displayName:"Input",componentId:"sc-sq94oz-3"})(["",""],Tt),fc=s.select.withConfig({displayName:"Select",componentId:"sc-sq94oz-4"})([""," cursor:pointer;option{background:",";color:",";}"],Tt,({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),gc=s.textarea.withConfig({displayName:"TextArea",componentId:"sc-sq94oz-5"})([""," resize:vertical;min-height:80px;font-family:inherit;"],Tt),mc=s.div.withConfig({displayName:"PrefixContainer",componentId:"sc-sq94oz-6"})(["position:absolute;left:12px;display:flex;align-items:center;color:",";pointer-events:none;z-index:1;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),hc=s.div.withConfig({displayName:"SuffixContainer",componentId:"sc-sq94oz-7"})(["position:absolute;right:12px;display:flex;align-items:center;color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),xc=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-sq94oz-8"})(["font-size:",";color:",";font-weight:500;display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#f44336"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),bc=s.div.withConfig({displayName:"HelpText",componentId:"sc-sq94oz-9"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),yc=s.div.withConfig({displayName:"ValidationIndicator",componentId:"sc-sq94oz-10"})(["position:absolute;right:8px;display:flex;align-items:center;"," ",""],({$validating:e})=>e&&s.css(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),({$valid:e,$validating:t})=>!t&&s.css(["color:",";&::after{content:'","';}"],e?"#22c55e":"#f44336",e?"✓":"✗")),vc=e=>{const{label:t,field:r,type:n="text",placeholder:i,required:c=!1,disabled:a=!1,helpText:p,options:f=[],inputProps:l={},className:d,size:m="md",variant:h="default",prefix:y,suffix:g}=e,x=!!(r.error&&r.touched),C=r.touched&&!r.validating,S=()=>{const w={id:l.id||t.toLowerCase().replace(/\s+/g,"-"),value:r.value,onChange:r.setValue,onBlur:()=>r.setTouched(!0),disabled:a,placeholder:i,$hasError:x,$size:m,...l};switch(n){case"select":return o.jsxs(fc,{...w,children:[i&&o.jsx("option",{value:"",disabled:!0,children:i}),f.map(_=>o.jsx("option",{value:_.value,children:_.label},_.value))]});case"textarea":return o.jsx(gc,{...w});default:return o.jsx(pc,{...w,type:n})}};return o.jsxs(lc,{$size:m,className:d,children:[o.jsx(dc,{$required:c,$variant:h,htmlFor:l.id||t.toLowerCase().replace(/\s+/g,"-"),children:t}),o.jsxs(uc,{$hasError:x,$disabled:a,children:[y&&o.jsx(mc,{children:y}),S(),g&&o.jsx(hc,{children:g}),C&&o.jsx(yc,{$valid:r.valid,$validating:r.validating})]}),x&&o.jsxs(xc,{children:["⚠️ ",r.error]}),p&&!x&&o.jsx(bc,{children:p})]})},Br=(e=!1)=>{const[t,r]=b.useState(e),[n,i]=b.useState(null),[c,a]=b.useState(!1),p=b.useCallback(y=>{r(y),y&&(i(null),a(!1))},[]),f=b.useCallback(y=>{i(y),r(!1),a(!1)},[]),l=b.useCallback(()=>{i(null)},[]),d=b.useCallback(()=>{r(!1),i(null),a(!1)},[]),m=b.useCallback(async y=>{p(!0);try{const g=await y();return a(!0),r(!1),g}catch(g){const x=g instanceof Error?g.message:"An unexpected error occurred";throw f(x),g}},[p,f]),h=b.useCallback(y=>async(...g)=>{try{await m(()=>y(...g))}catch(x){console.error("Operation failed:",x)}},[m]);return{isLoading:t,error:n,isSuccess:c,isError:n!==null,setLoading:p,setError:f,clearError:l,reset:d,withLoading:m,withLoadingCallback:h}};function Sc(e,t={}){const{fetchOnMount:r=!0,dependencies:n=[]}=t,[i,c]=b.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),a=b.useCallback(async(...p)=>{c(f=>({...f,isLoading:!0,error:null}));try{const f=await e(...p);return c({data:f,isLoading:!1,error:null,isInitialized:!0}),f}catch(f){const l=f instanceof Error?f:new Error(String(f));throw c(d=>({...d,isLoading:!1,error:l,isInitialized:!0})),l}},[e]);return b.useEffect(()=>{r&&a()},[r,a,...n]),{...i,fetchData:a,refetch:()=>a()}}function wc(e,t){const[r,n]=b.useState(e);return b.useEffect(()=>{const i=setTimeout(()=>{n(e)},t);return()=>{clearTimeout(i)}},[e,t]),r}function Cc(e={}){const{componentName:t,logToConsole:r=!0,reportToMonitoring:n=!0,onError:i}=e,[c,a]=b.useState(null),[p,f]=b.useState(!1),l=b.useCallback(h=>{if(a(h),f(!0),r){const y=t?`[${t}]`:"";console.error(`Error caught by useErrorHandler${y}:`,h)}i&&i(h)},[t,r,n,i]),d=b.useCallback(()=>{a(null),f(!1)},[]),m=b.useCallback(async h=>{try{return await h()}catch(y){l(y);return}},[l]);return b.useEffect(()=>()=>{a(null),f(!1)},[]),{error:c,hasError:p,handleError:l,resetError:d,tryExecute:m}}function bt(e,t){const r=()=>{if(typeof window>"u")return t;try{const a=window.localStorage.getItem(e);return a?JSON.parse(a):t}catch(a){return console.warn(`Error reading localStorage key "${e}":`,a),t}},[n,i]=b.useState(r),c=a=>{try{const p=a instanceof Function?a(n):a;i(p),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(p))}catch(p){console.warn(`Error setting localStorage key "${e}":`,p)}};return b.useEffect(()=>{const a=p=>{p.key===e&&p.newValue&&i(JSON.parse(p.newValue))};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e]),[n,c]}function Tc(e){const{totalItems:t,itemsPerPage:r=10,initialPage:n=1,persistKey:i}=e,[c,a]=i?bt(`${i}_page`,n):b.useState(n),[p,f]=i?bt(`${i}_itemsPerPage`,r):b.useState(r),l=b.useMemo(()=>Math.max(1,Math.ceil(t/p)),[t,p]),d=b.useMemo(()=>Math.min(Math.max(1,c),l),[c,l]);d!==c&&a(d);const m=(d-1)*p,h=Math.min(m+p-1,t-1),y=d>1,g=d<l,x=b.useMemo(()=>{const $=[];if(l<=5)for(let L=1;L<=l;L++)$.push(L);else{let L=Math.max(1,d-Math.floor(2.5));const j=Math.min(l,L+5-1);j===l&&(L=Math.max(1,j-5+1));for(let R=L;R<=j;R++)$.push(R)}return $},[d,l]),C=b.useCallback(()=>{g&&a(d+1)},[g,d,a]),S=b.useCallback(()=>{y&&a(d-1)},[y,d,a]),w=b.useCallback(z=>{const $=Math.min(Math.max(1,z),l);a($)},[l,a]),_=b.useCallback(z=>{f(z),a(1)},[f,a]);return{currentPage:d,itemsPerPage:p,totalPages:l,hasPreviousPage:y,hasNextPage:g,startIndex:m,endIndex:h,pageRange:x,nextPage:C,previousPage:S,goToPage:w,setItemsPerPage:_}}const Ic=(e,t="$",r=!1)=>{const i=Math.abs(e).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2});return e>0?r?`+${t}${i}`:`${t}${i}`:e<0?`-${t}${i}`:`${t}${i}`},Ec=(e,t={})=>{const{currency:r="$",showPositiveSign:n=!1,customAriaLabel:i}=t;return b.useMemo(()=>{if(e==null)return{formattedAmount:"",isProfit:!1,isLoss:!1,isNeutral:!1,isEmpty:!0,ariaLabel:i||"No profit/loss data available"};const c=e>0,a=e<0,p=e===0,f=Ic(e,r,n),l=`${c?"Profit":a?"Loss":"Breakeven"} of ${f}`;return{formattedAmount:f,isProfit:c,isLoss:a,isNeutral:p,isEmpty:!1,ariaLabel:i||l}},[e,r,n,i])},jc=e=>e==null?!0:Array.isArray(e)?e.length===0:typeof e=="object"?Object.keys(e).length===0:typeof e=="string"?e.trim().length===0:!1,Nc=e=>{const{fetchData:t,initialData:r=null,fetchOnMount:n=!0,refreshInterval:i,isEmpty:c=jc,transformError:a,dependencies:p=[]}=e,[f,l]=b.useState(r),[d,m]=b.useState(null),h=Br(),y=b.useMemo(()=>f===null||c(f),[f,c]),g=b.useCallback(async()=>{try{const w=await h.withLoading(t);l(w),m(new Date)}catch(w){const _=a&&w instanceof Error?a(w):w instanceof Error?w.message:"Failed to fetch data";h.setError(_),console.error("Data fetch failed:",w)}},[t,h,a]),x=b.useCallback(async()=>{await g()},[g]),C=b.useCallback(()=>{l(r),m(null),h.reset()},[r,h]),S=b.useCallback(w=>{l(w),m(new Date),h.clearError()},[h]);return b.useEffect(()=>{n&&g()},[n,g]),b.useEffect(()=>{p.length>0&&d!==null&&g()},p),b.useEffect(()=>{if(!i||i<=0)return;const w=setInterval(()=>{!h.isLoading&&!h.error&&g()},i);return()=>clearInterval(w)},[i,h.isLoading,h.error,g]),{data:f,isLoading:h.isLoading,error:h.error,isEmpty:y,isSuccess:h.isSuccess,isError:h.isError,lastFetched:d,refresh:x,clearError:h.clearError,reset:C,setData:S}},kc=(e="en-US")=>b.useMemo(()=>({formatCurrency:(f,l={})=>{const{currency:d="USD",locale:m=e,minimumFractionDigits:h=2,maximumFractionDigits:y=2,showPositiveSign:g=!1}=l,C=new Intl.NumberFormat(m,{style:"currency",currency:d,minimumFractionDigits:h,maximumFractionDigits:y}).format(Math.abs(f));return f>0&&g?`+${C}`:f<0?`-${C}`:C},formatPercent:(f,l={})=>{const{locale:d=e,minimumFractionDigits:m=2,maximumFractionDigits:h=2,showPositiveSign:y=!1}=l,g=new Intl.NumberFormat(d,{style:"percent",minimumFractionDigits:m,maximumFractionDigits:h}),x=f>1?f/100:f,C=g.format(Math.abs(x));return x>0&&y?`+${C}`:x<0?`-${C}`:C},formatNumber:(f,l={})=>{const{locale:d=e,minimumFractionDigits:m=0,maximumFractionDigits:h=2,useGrouping:y=!0}=l;return new Intl.NumberFormat(d,{minimumFractionDigits:m,maximumFractionDigits:h,useGrouping:y}).format(f)},formatDate:(f,l="medium")=>{const d=typeof f=="string"?new Date(f):f;return new Intl.DateTimeFormat(e,{dateStyle:l}).format(d)},formatTime:(f,l="short")=>{const d=typeof f=="string"?new Date(f):f;return new Intl.DateTimeFormat(e,{timeStyle:l}).format(d)},formatRelativeTime:f=>{const l=typeof f=="string"?new Date(f):f,m=Math.floor((new Date().getTime()-l.getTime())/1e3);if(typeof Intl.RelativeTimeFormat<"u"){const x=new Intl.RelativeTimeFormat(e,{numeric:"auto"}),C=[{unit:"year",seconds:31536e3},{unit:"month",seconds:2592e3},{unit:"day",seconds:86400},{unit:"hour",seconds:3600},{unit:"minute",seconds:60},{unit:"second",seconds:1}];for(const S of C){const w=Math.floor(Math.abs(m)/S.seconds);if(w>=1)return x.format(m>0?-w:w,S.unit)}return x.format(0,"second")}const h=Math.abs(m),y=m<0;if(h<60)return y?"in a few seconds":"a few seconds ago";if(h<3600){const x=Math.floor(h/60);return y?`in ${x} minute${x>1?"s":""}`:`${x} minute${x>1?"s":""} ago`}if(h<86400){const x=Math.floor(h/3600);return y?`in ${x} hour${x>1?"s":""}`:`${x} hour${x>1?"s":""} ago`}const g=Math.floor(h/86400);return y?`in ${g} day${g>1?"s":""}`:`${g} day${g>1?"s":""} ago`}}),[e]),Hr={small:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xxs)||"2px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),medium:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),large:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"18px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"})},Ur={profit:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.profit)||((r=e.colors)==null?void 0:r.success)||"#4caf50"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.profit?`${e.colors.profit}15`:"rgba(76, 175, 80, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.profit?`${e.colors.profit}30`:"rgba(76, 175, 80, 0.2)"}),loss:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.loss)||((r=e.colors)==null?void 0:r.error)||"#f44336"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.loss?`${e.colors.loss}15`:"rgba(244, 67, 54, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.loss?`${e.colors.loss}30`:"rgba(244, 67, 54, 0.2)"}),neutral:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.neutral)||((r=e.colors)==null?void 0:r.textSecondary)||"#757575"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.neutral?`${e.colors.neutral}15`:"rgba(117, 117, 117, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.neutral?`${e.colors.neutral}30`:"rgba(117, 117, 117, 0.2)"}),default:s.css(["color:",";background-color:transparent;border:1px solid transparent;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"})},Lc=s.css(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:",";font-family:",";transition:",";border-radius:",";&:hover{transform:translateY(-1px);box-shadow:",";}"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||"600"},({theme:e})=>{var t;return((t=e.fontFamilies)==null?void 0:t.mono)||"monospace"},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.shadows)==null?void 0:t.sm)||"0 2px 4px rgba(0, 0, 0, 0.1)"}),Rc=s.css(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]),_c=e=>Hr[e],Mc=(e,t,r)=>e?"profit":t?"loss":r?"neutral":"default",Pc=e=>Ur[e],It={name:"f1",colors:{primary:E.f1Red,primaryDark:E.f1RedDark,primaryLight:E.f1RedLight,secondary:E.f1Blue,secondaryDark:E.f1BlueDark,secondaryLight:E.f1BlueLight,accent:E.purple,accentDark:E.purpleDark,accentLight:E.purpleLight,success:F.success,warning:F.warning,error:F.error,danger:F.error,info:F.info,background:F.background,surface:F.surface,elevated:E.gray700,cardBackground:F.surface,border:F.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:F.textPrimary,textSecondary:F.textSecondary,textDisabled:F.textDisabled,textInverse:F.textInverse,chartGrid:F.chartGrid,chartLine:F.chartLine,chartAxis:E.gray400,chartTooltip:F.tooltipBackground,profit:F.profit,loss:F.loss,neutral:F.neutral,tabActive:E.f1Red,tabInactive:E.gray600,tooltipBackground:F.tooltipBackground,modalBackground:F.modalBackground,sidebarBackground:E.gray800,headerBackground:"rgba(0, 0, 0, 0.2)"},spacing:Q,breakpoints:Ze,fontSizes:le,fontWeights:Qe,lineHeights:Xe,fontFamilies:Je,borderRadius:et,shadows:tt,transitions:rt,zIndex:ot},Yr={name:"light",colors:{primary:E.f1Red,primaryDark:E.f1RedDark,primaryLight:E.f1RedLight,secondary:E.f1Blue,secondaryDark:E.f1BlueDark,secondaryLight:E.f1BlueLight,accent:E.purple,accentDark:E.purpleDark,accentLight:E.purpleLight,success:K.success,warning:K.warning,error:K.error,danger:K.error,info:K.info,background:K.background,surface:K.surface,elevated:E.gray100,cardBackground:K.surface,border:K.border,divider:E.blackTransparent10,textPrimary:K.textPrimary,textSecondary:K.textSecondary,textDisabled:K.textDisabled,textInverse:K.textInverse,chartGrid:K.chartGrid,chartLine:K.chartLine,chartAxis:E.gray600,chartTooltip:K.tooltipBackground,profit:K.profit,loss:K.loss,neutral:K.neutral,tabActive:E.f1Red,tabInactive:E.gray400,tooltipBackground:K.tooltipBackground,modalBackground:K.modalBackground,sidebarBackground:E.white,headerBackground:"rgba(0, 0, 0, 0.05)"},spacing:Q,breakpoints:Ze,fontSizes:le,fontWeights:Qe,lineHeights:Xe,fontFamilies:Je,borderRadius:et,shadows:tt,transitions:rt,zIndex:ot},Vr={name:"dark",colors:{primary:E.f1Blue,primaryDark:E.f1BlueDark,primaryLight:E.f1BlueLight,secondary:E.f1Blue,secondaryDark:E.f1BlueDark,secondaryLight:E.f1BlueLight,accent:E.purple,accentDark:E.purpleDark,accentLight:E.purpleLight,success:F.success,warning:F.warning,error:F.error,danger:F.error,info:F.info,background:E.gray900,surface:E.gray800,elevated:E.gray700,cardBackground:E.gray800,border:E.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:E.white,textSecondary:E.gray300,textDisabled:E.gray500,textInverse:E.gray900,chartGrid:F.chartGrid,chartLine:E.f1Blue,chartAxis:E.gray400,chartTooltip:F.tooltipBackground,profit:F.profit,loss:F.loss,neutral:F.neutral,tabActive:E.f1Blue,tabInactive:E.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:E.gray900,headerBackground:"rgba(0, 0, 0, 0.3)"},spacing:Q,breakpoints:Ze,fontSizes:le,fontWeights:Qe,lineHeights:Xe,fontFamilies:Je,borderRadius:et,shadows:tt,transitions:rt,zIndex:ot},Dc=s.createGlobalStyle(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),$c=Dc,Oc={f1:It,light:Yr,dark:Vr},Et=It,mt=e=>Oc[e]||Et,jt=b.createContext({theme:Et,setTheme:()=>{}}),zc=()=>b.useContext(jt),Ac=({initialTheme:e=Et,persistTheme:t=!0,storageKey:r="adhd-dashboard-theme",children:n})=>{const[i,c]=b.useState(()=>{if(t&&typeof window<"u"){const l=window.localStorage.getItem(r);if(l)try{const d=mt(l);return d||JSON.parse(l)}catch(d){console.error("Failed to parse stored theme:",d)}}return typeof e=="string"?mt(e):e}),a=f=>{const l=typeof f=="string"?mt(f):f;c(l),t&&typeof window<"u"&&window.localStorage.setItem(r,l.name||JSON.stringify(l))},p=({children:f})=>o.jsxs(s.ThemeProvider,{theme:i,children:[o.jsx($c,{}),f]});return o.jsx(jt.Provider,{value:{theme:i,setTheme:a},children:o.jsx(p,{children:n})})};function Fc(e,t,r="StoreContext"){const n=b.createContext(void 0);n.displayName=r;const i=({children:l,initialState:d})=>{const[m,h]=b.useReducer(e,d||t),y=b.useMemo(()=>({state:m,dispatch:h}),[m]);return o.jsx(n.Provider,{value:y,children:l})};function c(){const l=b.useContext(n);if(l===void 0)throw new Error(`use${r} must be used within a ${r}Provider`);return l}function a(l){const{state:d}=c();return l(d)}function p(l){const{dispatch:d}=c();return b.useMemo(()=>(...m)=>{d(l(...m))},[d,l])}function f(l){const{dispatch:d}=c();return b.useMemo(()=>{const m={};for(const h in l)m[h]=(...y)=>{d(l[h](...y))};return m},[d,l])}return{Context:n,Provider:i,useStore:c,useSelector:a,useAction:p,useActions:f}}function qc(...e){const t=e.pop(),r=e;let n=null,i=null;return c=>{const a=r.map(p=>p(c));return(n===null||a.length!==n.length||a.some((p,f)=>p!==n[f]))&&(i=t(...a),n=a),i}}function Bc(e,t){const{key:r,initialState:n,version:i=1,migrate:c,serialize:a=JSON.stringify,deserialize:p=JSON.parse,filter:f=S=>S,merge:l=(S,w)=>({...w,...S}),debug:d=!1}=t,m=()=>{try{const S=localStorage.getItem(r);if(S===null)return null;const{state:w,version:_}=p(S);return _!==i&&c?(d&&console.log(`Migrating state from version ${_} to ${i}`),c(w,_)):w}catch(S){return d&&console.error("Error loading state from local storage:",S),null}},h=S=>{try{const w=f(S),_=a({state:w,version:i});localStorage.setItem(r,_)}catch(w){d&&console.error("Error saving state to local storage:",w)}},y=()=>{try{localStorage.removeItem(r)}catch(S){d&&console.error("Error clearing state from local storage:",S)}},g=m(),x=g?l(g,n):n;return d&&g&&(console.log("Loaded persisted state:",g),console.log("Merged initial state:",x)),{reducer:(S,w)=>{const _=e(S,w);return h(_),_},initialState:x,clear:y}}function Hc(e,t="$"){return`${t}${e.toFixed(2)}`}function Uc(e,t=1){return`${(e*100).toFixed(t)}%`}function Yc(e,t="short"){const r=typeof e=="string"?new Date(e):e;switch(t){case"medium":return r.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return r.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function Vc(e,t=50){return e.length<=t?e:`${e.substring(0,t-3)}...`}function Wc(){return Math.random().toString(36).substring(2,9)}function Gc(e,t){let r=null;return function(...n){const i=()=>{r=null,e(...n)};r&&clearTimeout(r),r=setTimeout(i,t)}}function Kc(e,t){let r=!1;return function(...n){r||(e(...n),r=!0,setTimeout(()=>{r=!1},t))}}function Qc(e={}){console.log("Monitoring service initialized",e)}function Xc(e,t){console.error("Error captured by monitoring service:",e,t)}function Jc(e){console.log("User set for monitoring service:",e)}function Zc(e,t){const r=performance.now();return{name:e,startTime:r,finish:()=>{const i=performance.now()-r;console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`,t)}}}const W={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class el{constructor(){this.dbName="adhd-trading-dashboard",this.version=2,this.db=null,this.stores={trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"}}async initDB(){return this.db?this.db:new Promise((t,r)=>{const n=indexedDB.open(this.dbName,this.version);n.onupgradeneeded=i=>{var a;const c=i.target.result;if(!c.objectStoreNames.contains(this.stores.trades)){const p=c.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});p.createIndex(W.DATE,W.DATE,{unique:!1}),p.createIndex(W.MODEL_TYPE,W.MODEL_TYPE,{unique:!1}),p.createIndex(W.SESSION,W.SESSION,{unique:!1}),p.createIndex(W.WIN_LOSS,W.WIN_LOSS,{unique:!1}),p.createIndex(W.R_MULTIPLE,W.R_MULTIPLE,{unique:!1})}if(c.objectStoreNames.contains(this.stores.fvg_details)||c.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.setups)||c.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.analysis)||c.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!c.objectStoreNames.contains(this.stores.sessions)){c.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const f=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(a=n.transaction)==null||a.addEventListener("complete",()=>{const d=c.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);f.forEach(m=>d.add(m))})}},n.onsuccess=i=>{this.db=i.target.result,t(this.db)},n.onerror=i=>{console.error("Error opening IndexedDB:",i),r(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(t){try{const r=await this.initDB();return new Promise((n,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=l=>{console.error("Transaction error:",l),i(new Error("Failed to save trade with details"))};const a=c.objectStore(this.stores.trades),p={...t.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},f=a.add(p);f.onsuccess=()=>{const l=f.result,d=[];if(t.fvg_details){const m=c.objectStore(this.stores.fvg_details),h={...t.fvg_details,trade_id:l};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save FVG details"))}))}if(t.setup){const m=c.objectStore(this.stores.setups),h={...t.setup,trade_id:l};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save setup data"))}))}if(t.analysis){const m=c.objectStore(this.stores.analysis),h={...t.analysis,trade_id:l};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save analysis data"))}))}c.oncomplete=()=>{n(l)}},f.onerror=l=>{console.error("Error saving trade:",l),i(new Error("Failed to save trade"))}})}catch(r){throw console.error("Error in saveTradeWithDetails:",r),new Error("Failed to save trade with details")}}async getTradeById(t){try{const r=await this.initDB();return new Promise((n,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).get(t);p.onsuccess=()=>{const f=p.result;if(!f){n(null);return}const l={trade:f},h=c.objectStore(this.stores.fvg_details).index("trade_id").get(t);h.onsuccess=()=>{h.result&&(l.fvg_details=h.result);const x=c.objectStore(this.stores.setups).index("trade_id").get(t);x.onsuccess=()=>{x.result&&(l.setup=x.result);const w=c.objectStore(this.stores.analysis).index("trade_id").get(t);w.onsuccess=()=>{w.result&&(l.analysis=w.result),n(l)},w.onerror=_=>{console.error("Error getting analysis data:",_),n(l)}},x.onerror=C=>{console.error("Error getting setup data:",C),n(l)}},h.onerror=y=>{console.error("Error getting FVG details:",y),n(l)}},p.onerror=f=>{console.error("Error getting trade:",f),i(new Error("Failed to get trade"))}})}catch(r){return console.error("Error in getTradeById:",r),null}}async getPerformanceMetrics(){try{const t=await this.initDB();return new Promise((r,n)=>{const a=t.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();a.onsuccess=()=>{const p=a.result;if(p.length===0){r({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const f=p.length,l=p.filter(D=>D[W.WIN_LOSS]==="Win").length,d=p.filter(D=>D[W.WIN_LOSS]==="Loss").length,m=f>0?l/f*100:0,h=p.filter(D=>D.achieved_pl!==void 0).map(D=>D.achieved_pl),y=h.reduce((D,X)=>D+X,0),g=h.filter(D=>D>0),x=h.filter(D=>D<0),C=g.length>0?g.reduce((D,X)=>D+X,0)/g.length:0,S=x.length>0?Math.abs(x.reduce((D,X)=>D+X,0)/x.length):0,w=g.length>0?Math.max(...g):0,_=x.length>0?Math.abs(Math.min(...x)):0,z=g.reduce((D,X)=>D+X,0),$=Math.abs(x.reduce((D,X)=>D+X,0)),L=$>0?z/$:0,j=p.filter(D=>D[W.R_MULTIPLE]!==void 0).map(D=>D[W.R_MULTIPLE]),R=j.length>0?j.reduce((D,X)=>D+X,0)/j.length:0,N=R*(m/100);let U=0,Y=0,k=0;for(const D of p)if(D.achieved_pl!==void 0){U+=D.achieved_pl,U>Y&&(Y=U);const X=Y-U;X>k&&(k=X)}const H=Y>0?k/Y*100:0,ee=j.length>0?Math.sqrt(j.length)*R/Math.sqrt(j.reduce((D,X)=>D+Math.pow(X-R,2),0)/j.length):0,re=p.map(D=>D.date).sort(),de=re.length>0?re[0]:"",oe=re.length>0?re[re.length-1]:"";r({totalTrades:f,winningTrades:l,losingTrades:d,winRate:m,profitFactor:L,averageWin:C,averageLoss:S,largestWin:w,largestLoss:_,totalPnl:y,maxDrawdown:k,maxDrawdownPercent:H,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:R,expectancy:N,sqn:ee,period:"all",startDate:de,endDate:oe})},a.onerror=p=>{console.error("Error getting performance metrics:",p),n(new Error("Failed to get performance metrics"))}})}catch(t){throw console.error("Error in getPerformanceMetrics:",t),new Error("Failed to get performance metrics")}}async filterTrades(t){try{const r=await this.initDB();return new Promise((n,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).getAll();p.onsuccess=async()=>{let f=p.result;t.dateFrom&&(f=f.filter(d=>d.date>=t.dateFrom)),t.dateTo&&(f=f.filter(d=>d.date<=t.dateTo)),t.model_type&&(f=f.filter(d=>d[W.MODEL_TYPE]===t.model_type)),t.session&&(f=f.filter(d=>d[W.SESSION]===t.session)),t.direction&&(f=f.filter(d=>d[W.DIRECTION]===t.direction)),t.win_loss&&(f=f.filter(d=>d[W.WIN_LOSS]===t.win_loss)),t.market&&(f=f.filter(d=>d[W.MARKET]===t.market)),t.min_r_multiple!==void 0&&(f=f.filter(d=>d[W.R_MULTIPLE]!==void 0&&d[W.R_MULTIPLE]>=t.min_r_multiple)),t.max_r_multiple!==void 0&&(f=f.filter(d=>d[W.R_MULTIPLE]!==void 0&&d[W.R_MULTIPLE]<=t.max_r_multiple)),t.min_pattern_quality!==void 0&&(f=f.filter(d=>d[W.PATTERN_QUALITY_RATING]!==void 0&&d[W.PATTERN_QUALITY_RATING]>=t.min_pattern_quality)),t.max_pattern_quality!==void 0&&(f=f.filter(d=>d[W.PATTERN_QUALITY_RATING]!==void 0&&d[W.PATTERN_QUALITY_RATING]<=t.max_pattern_quality));const l=[];for(const d of f){const m={trade:d},g=c.objectStore(this.stores.fvg_details).index("trade_id").get(d.id);await new Promise($=>{g.onsuccess=()=>{g.result&&(m.fvg_details=g.result),$()},g.onerror=()=>$()});const S=c.objectStore(this.stores.setups).index("trade_id").get(d.id);await new Promise($=>{S.onsuccess=()=>{S.result&&(m.setup=S.result),$()},S.onerror=()=>$()});const z=c.objectStore(this.stores.analysis).index("trade_id").get(d.id);await new Promise($=>{z.onsuccess=()=>{z.result&&(m.analysis=z.result),$()},z.onerror=()=>$()}),l.push(m)}n(l)},p.onerror=f=>{console.error("Error filtering trades:",f),i(new Error("Failed to filter trades"))}})}catch(r){throw console.error("Error in filterTrades:",r),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(t){return console.error("Error in getAllTrades:",t),[]}}async deleteTrade(t){try{const r=await this.initDB();return new Promise((n,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=S=>{console.error("Transaction error:",S),i(new Error("Failed to delete trade"))};const f=c.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(t));f.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const m=c.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(t));m.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const g=c.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(t));g.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const C=c.objectStore(this.stores.trades).delete(t);c.oncomplete=()=>{n()},C.onerror=S=>{console.error("Error deleting trade:",S),i(new Error("Failed to delete trade"))}})}catch(r){throw console.error("Error in deleteTrade:",r),new Error("Failed to delete trade")}}async updateTradeWithDetails(t,r){try{const n=await this.initDB();return new Promise((i,c)=>{const a=n.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");a.onerror=d=>{console.error("Transaction error:",d),c(new Error("Failed to update trade"))};const p=a.objectStore(this.stores.trades),f={...r.trade,id:t,updated_at:new Date().toISOString()},l=p.put(f);l.onsuccess=()=>{if(r.fvg_details){const d=a.objectStore(this.stores.fvg_details),m={...r.fvg_details,trade_id:t};d.put(m)}if(r.setup){const d=a.objectStore(this.stores.setups),m={...r.setup,trade_id:t};d.put(m)}if(r.analysis){const d=a.objectStore(this.stores.analysis),m={...r.analysis,trade_id:t};d.put(m)}},a.oncomplete=()=>{i()},l.onerror=d=>{console.error("Error updating trade:",d),c(new Error("Failed to update trade"))}})}catch(n){throw console.error("Error in updateTradeWithDetails:",n),new Error("Failed to update trade")}}}const Wr=new el,tl=Wr,rl=Wr;exports.AppErrorBoundary=hs;exports.Badge=ze;exports.Button=ie;exports.Card=Nr;exports.DashboardSection=ha;exports.DashboardTemplate=Sa;exports.DataCard=ca;exports.DualTimeDisplay=kn;exports.EmptyState=ht;exports.EnhancedFormField=_s;exports.ErrorBoundary=Lr;exports.F1Container=Za;exports.F1Form=cc;exports.F1FormField=vc;exports.F1Header=Ya;exports.FeatureErrorBoundary=xs;exports.FormField=Vs;exports.HierarchicalSessionSelector=$i;exports.Input=be;exports.LoadingCell=zn;exports.LoadingPlaceholder=Cr;exports.LoadingSpinner=Un;exports.MacroPeriodType=M;exports.Modal=ri;exports.OrderSide=yr;exports.OrderStatus=vr;exports.OrderType=br;exports.SETUP_ELEMENTS=Ce;exports.Select=Te;exports.SelectDropdown=Pn;exports.SessionType=G;exports.SessionUtils=te;exports.SetupBuilder=Na;exports.SortableTable=Bs;exports.StatusIndicator=sn;exports.TRADE_COLUMN_IDS=I;exports.TabPanel=Cs;exports.Table=yi;exports.Tag=un;exports.ThemeContext=jt;exports.ThemeProvider=Ac;exports.TimeInForce=Sr;exports.TimePicker=hn;exports.TradeAnalysis=Da;exports.TradeDirection=hr;exports.TradeMetrics=La;exports.TradeStatus=xr;exports.TradeTable=ia;exports.TradeTableFilters=qr;exports.TradeTableRow=Fr;exports.UnifiedErrorBoundary=yt;exports.VALID_TRADING_MODELS=So;exports.baseColors=E;exports.borderRadius=et;exports.breakpoints=Ze;exports.captureError=Xc;exports.convertLocalToNY=xn;exports.convertNYToLocal=Ie;exports.convertSessionToDualTime=Ee;exports.createSelector=qc;exports.createStoreContext=Fc;exports.darkModeColors=F;exports.darkTheme=Vr;exports.debounce=Gc;exports.f1Theme=It;exports.fontFamilies=Je;exports.fontSizes=le;exports.fontWeights=Qe;exports.formatCurrency=Hc;exports.formatDate=Yc;exports.formatPercentage=Uc;exports.formatTime=xt;exports.formatTimeForDesktop=wn;exports.formatTimeForMobile=jr;exports.formatTimeInterval=Tr;exports.generateId=Wc;exports.getCompactTradeTableColumns=zr;exports.getCurrentDualTime=Oe;exports.getCurrentNYMinutes=bn;exports.getCurrentNYTime=yn;exports.getNextSessionInfo=Sn;exports.getPerformanceTradeTableColumns=Ar;exports.getProfitLossColors=Pc;exports.getProfitLossSize=_c;exports.getProfitLossVariant=Mc;exports.getSessionStatus=Cn;exports.getTimeRemainingInWindow=Ir;exports.getTimeUntilNYTime=ve;exports.getTimezoneAbbreviation=Ge;exports.getTradeTableColumns=Or;exports.getUserTimezone=Ae;exports.initMonitoring=Qc;exports.isCurrentTimeInNYWindow=Er;exports.lightModeColors=K;exports.lightTheme=Yr;exports.lineHeights=Xe;exports.minutesToTime=vn;exports.persistState=Bc;exports.profitLossBaseStyles=Lc;exports.profitLossColors=Ur;exports.profitLossLoadingStyles=Rc;exports.profitLossSizes=Hr;exports.setUser=Jc;exports.shadows=tt;exports.sortFunctions=Ms;exports.spacing=Q;exports.startTransaction=Zc;exports.testTimezoneConversion=Tn;exports.throttle=Kc;exports.timeToMinutes=ye;exports.tradeStorage=rl;exports.tradeStorageService=tl;exports.transitions=rt;exports.truncateText=Vc;exports.useAsyncData=Sc;exports.useDataFormatting=kc;exports.useDataSection=Nc;exports.useDebounce=wc;exports.useErrorHandler=Cc;exports.useFormField=_r;exports.useLoadingState=Br;exports.useLocalStorage=bt;exports.usePagination=Tc;exports.useProfitLossFormatting=Ec;exports.useSessionSelection=Pr;exports.useSortableTable=Mr;exports.useTheme=zc;exports.validationRules=Rr;exports.zIndex=ot;
