import { jsx as _jsx } from "react/jsx-runtime";
import { DailyGuide as FeatureDailyGuide } from '../features/daily-guide';
/**
 * DailyGuide Page
 *
 * Routes to the actual Daily Guide feature implementation
 * which includes Session Focus and real trade data analytics.
 */
const DailyGuide = () => {
    return _jsx(FeatureDailyGuide, {});
};
export default DailyGuide;
//# sourceMappingURL=DailyGuide.js.map