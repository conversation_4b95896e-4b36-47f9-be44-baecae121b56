/**
 * Time Zone Utilities
 *
 * Comprehensive time zone handling for international traders working with NY sessions.
 * Provides dual-timezone display, proper time formatting, and session time conversion.
 */
export interface DualTimeDisplay {
    nyTime: string;
    localTime: string;
    nyTimezone: string;
    localTimezone: string;
    formatted: string;
}
export interface TimeInterval {
    totalMinutes: number;
    hours: number;
    minutes: number;
    formatted: string;
}
export interface SessionTimeInfo {
    nyStart: string;
    nyEnd: string;
    localStart: string;
    localEnd: string;
    formatted: string;
}
/**
 * Get user's local timezone
 */
export declare const getUserTimezone: () => string;
/**
 * Get timezone abbreviation
 */
export declare const getTimezoneAbbreviation: (timezone: string, date?: Date) => string;
/**
 * Convert NY time to user's local time
 */
export declare const convertNYToLocal: (nyTime: string, userTimezone?: string) => string;
/**
 * Convert local time to NY time
 */
export declare const convertLocalToNY: (localTime: string, userTimezone?: string) => string;
/**
 * Get current time in both NY and local timezone
 */
export declare const getCurrentDualTime: (userTimezone?: string) => DualTimeDisplay;
/**
 * Get current NY time as minutes since midnight (CRITICAL FOR SESSION CALCULATIONS)
 */
export declare const getCurrentNYMinutes: () => number;
/**
 * Get current NY time as Date object
 */
export declare const getCurrentNYTime: () => Date;
/**
 * Format time interval in user-friendly format
 */
export declare const formatTimeInterval: (totalMinutes: number) => TimeInterval;
/**
 * Calculate time until a specific NY time
 */
export declare const getTimeUntilNYTime: (targetNYTime: string) => TimeInterval;
/**
 * Calculate time remaining in a session window
 */
export declare const getTimeRemainingInWindow: (windowEndNY: string) => TimeInterval;
/**
 * Convert session time range to dual timezone
 */
export declare const convertSessionToDualTime: (nyStart: string, nyEnd: string, userTimezone?: string) => SessionTimeInfo;
/**
 * Check if current time is within a NY session window
 */
export declare const isCurrentTimeInNYWindow: (nyStart: string, nyEnd: string) => boolean;
/**
 * Convert time string to minutes since midnight
 */
export declare const timeToMinutes: (timeStr: string) => number;
/**
 * Convert minutes since midnight to time string
 */
export declare const minutesToTime: (minutes: number) => string;
/**
 * Get next session information
 */
export declare const getNextSessionInfo: (sessions: Array<{
    name: string;
    nyStart: string;
    nyEnd: string;
}>) => {
    nextSession: string;
    timeUntil: TimeInterval;
    sessionTime: SessionTimeInfo;
} | null;
/**
 * Format time for mobile display (compact)
 */
export declare const formatTimeForMobile: (dualTime: DualTimeDisplay) => string;
/**
 * Format time for desktop display (full)
 */
export declare const formatTimeForDesktop: (dualTime: DualTimeDisplay) => string;
/**
 * Get session status with time context
 */
export declare const getSessionStatus: (sessionName: string, nyStart: string, nyEnd: string, userTimezone?: string) => {
    isActive: boolean;
    timeRemaining?: TimeInterval;
    timeUntilStart?: TimeInterval;
    sessionTime: SessionTimeInfo;
    status: "active" | "upcoming" | "ended";
};
/**
 * Test timezone conversion (for debugging)
 */
export declare const testTimezoneConversion: () => void;
//# sourceMappingURL=timeZoneUtils.d.ts.map