import Se, { useState as V, useRef as yt, useEffect as le, Component as ro, useMemo as Y, use<PERSON>allback as F, Suspense as to, createContext as vt, useContext as wt, useReducer as oo } from "react";
import c, { css as g, keyframes as Le, createGlobalStyle as no, Theme<PERSON><PERSON>ider as so } from "styled-components";
import { createPortal as io } from "react-dom";
var ao = /* @__PURE__ */ ((e) => (e.LONG = "LONG", e.SHORT = "SHORT", e))(ao || {}), co = /* @__PURE__ */ ((e) => (e.OPEN = "OPEN", e.CLOSED = "CLOSED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e.PENDING = "PENDING", e))(co || {}), lo = /* @__PURE__ */ ((e) => (e.MARKET = "MARKET", e.LIMIT = "LIMIT", e.STOP = "STOP", e.STOP_LIMIT = "STOP_LIMIT", e))(lo || {}), uo = /* @__PURE__ */ ((e) => (e.BUY = "BUY", e.SELL = "SELL", e))(uo || {}), po = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.FILLED = "FILLED", e.PARTIALLY_FILLED = "PARTIALLY_FILLED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e))(po || {}), fo = /* @__PURE__ */ ((e) => (e.GTC = "GTC", e.IOC = "IOC", e.FOK = "FOK", e.DAY = "DAY", e))(fo || {}), X = /* @__PURE__ */ ((e) => (e.LONDON = "london", e.NEW_YORK_AM = "new-york-am", e.NEW_YORK_PM = "new-york-pm", e.ASIA = "asia", e.PRE_MARKET = "pre-market", e.AFTER_HOURS = "after-hours", e.OVERNIGHT = "overnight", e))(X || {}), P = /* @__PURE__ */ ((e) => (e.MORNING_BREAKOUT = "morning-breakout", e.MID_MORNING_REVERSION = "mid-morning-reversion", e.PRE_LUNCH = "pre-lunch", e.LUNCH_MACRO_EXTENDED = "lunch-macro-extended", e.LUNCH_MACRO = "lunch-macro", e.POST_LUNCH = "post-lunch", e.PRE_CLOSE = "pre-close", e.POWER_HOUR = "power-hour", e.MOC = "moc", e.LONDON_OPEN = "london-open", e.LONDON_NY_OVERLAP = "london-ny-overlap", e.CUSTOM = "custom", e))(P || {});
const Me = {
  constant: {
    parentArrays: ["NWOG", "Old-NWOG", "NDOG", "Old-NDOG", "Monthly-FVG", "Weekly-FVG", "Daily-FVG", "15min-Top/Bottom-FVG", "1h-Top/Bottom-FVG"],
    fvgTypes: ["Strong-FVG", "AM-FPFVG", "PM-FPFVG", "Asia-FPFVG", "Premarket-FPFVG", "MNOR-FVG", "Macro-FVG", "News-FVG", "Top/Bottom-FVG"]
  },
  action: {
    liquidityEvents: ["None", "London-H/L", "Premarket-H/L", "09:30-Opening-Range-H/L", "Lunch-H/L", "Prev-Day-H/L", "Prev-Week-H/L", "Monthly-H/L", "Macro-H/L"]
  },
  variable: {
    rdTypes: ["None", "True-RD", "IMM-RD", "Dispersed-RD", "Wide-Gap-RD"]
  },
  entry: {
    methods: ["Simple-Entry", "Complex-Entry", "Complex-Entry/Mini"]
  }
}, ac = ["RD-Cont", "FVG-RD", "Combined"];
var o = {}, go = {
  get exports() {
    return o;
  },
  set exports(e) {
    o = e;
  }
}, Pe = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Hr;
function mo() {
  if (Hr)
    return Pe;
  Hr = 1;
  var e = Se, r = Symbol.for("react.element"), t = Symbol.for("react.fragment"), n = Object.prototype.hasOwnProperty, s = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, a = { key: !0, ref: !0, __self: !0, __source: !0 };
  function i(p, f, l) {
    var d, h = {}, x = null, y = null;
    l !== void 0 && (x = "" + l), f.key !== void 0 && (x = "" + f.key), f.ref !== void 0 && (y = f.ref);
    for (d in f)
      n.call(f, d) && !a.hasOwnProperty(d) && (h[d] = f[d]);
    if (p && p.defaultProps)
      for (d in f = p.defaultProps, f)
        h[d] === void 0 && (h[d] = f[d]);
    return { $$typeof: r, type: p, key: x, ref: y, props: h, _owner: s.current };
  }
  return Pe.Fragment = t, Pe.jsx = i, Pe.jsxs = i, Pe;
}
var De = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ur;
function ho() {
  return Ur || (Ur = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Se, r = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), s = Symbol.for("react.strict_mode"), a = Symbol.for("react.profiler"), i = Symbol.for("react.provider"), p = Symbol.for("react.context"), f = Symbol.for("react.forward_ref"), l = Symbol.for("react.suspense"), d = Symbol.for("react.suspense_list"), h = Symbol.for("react.memo"), x = Symbol.for("react.lazy"), y = Symbol.for("react.offscreen"), m = Symbol.iterator, b = "@@iterator";
    function C(u) {
      if (u === null || typeof u != "object")
        return null;
      var v = m && u[m] || u[b];
      return typeof v == "function" ? v : null;
    }
    var w = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function S(u) {
      {
        for (var v = arguments.length, I = new Array(v > 1 ? v - 1 : 0), M = 1; M < v; M++)
          I[M - 1] = arguments[M];
        _("error", u, I);
      }
    }
    function _(u, v, I) {
      {
        var M = w.ReactDebugCurrentFrame, q = M.getStackAddendum();
        q !== "" && (v += "%s", I = I.concat([q]));
        var K = I.map(function(A) {
          return String(A);
        });
        K.unshift("Warning: " + v), Function.prototype.apply.call(console[u], console, K);
      }
    }
    var z = !1, $ = !1, R = !1, E = !1, L = !1, N;
    N = Symbol.for("react.module.reference");
    function W(u) {
      return !!(typeof u == "string" || typeof u == "function" || u === n || u === a || L || u === s || u === l || u === d || E || u === y || z || $ || R || typeof u == "object" && u !== null && (u.$$typeof === x || u.$$typeof === h || u.$$typeof === i || u.$$typeof === p || u.$$typeof === f || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      u.$$typeof === N || u.getModuleId !== void 0));
    }
    function G(u, v, I) {
      var M = u.displayName;
      if (M)
        return M;
      var q = v.displayName || v.name || "";
      return q !== "" ? I + "(" + q + ")" : I;
    }
    function k(u) {
      return u.displayName || "Context";
    }
    function U(u) {
      if (u == null)
        return null;
      if (typeof u.tag == "number" && S("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof u == "function")
        return u.displayName || u.name || null;
      if (typeof u == "string")
        return u;
      switch (u) {
        case n:
          return "Fragment";
        case t:
          return "Portal";
        case a:
          return "Profiler";
        case s:
          return "StrictMode";
        case l:
          return "Suspense";
        case d:
          return "SuspenseList";
      }
      if (typeof u == "object")
        switch (u.$$typeof) {
          case p:
            var v = u;
            return k(v) + ".Consumer";
          case i:
            var I = u;
            return k(I._context) + ".Provider";
          case f:
            return G(u, u.render, "ForwardRef");
          case h:
            var M = u.displayName || null;
            return M !== null ? M : U(u.type) || "Memo";
          case x: {
            var q = u, K = q._payload, A = q._init;
            try {
              return U(A(K));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var oe = Object.assign, ne = 0, fe, se, D, J, we, H, he;
    function Nr() {
    }
    Nr.__reactDisabledLog = !0;
    function Rt() {
      {
        if (ne === 0) {
          fe = console.log, se = console.info, D = console.warn, J = console.error, we = console.group, H = console.groupCollapsed, he = console.groupEnd;
          var u = {
            configurable: !0,
            enumerable: !0,
            value: Nr,
            writable: !0
          };
          Object.defineProperties(console, {
            info: u,
            log: u,
            warn: u,
            error: u,
            group: u,
            groupCollapsed: u,
            groupEnd: u
          });
        }
        ne++;
      }
    }
    function Lt() {
      {
        if (ne--, ne === 0) {
          var u = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: oe({}, u, {
              value: fe
            }),
            info: oe({}, u, {
              value: se
            }),
            warn: oe({}, u, {
              value: D
            }),
            error: oe({}, u, {
              value: J
            }),
            group: oe({}, u, {
              value: we
            }),
            groupCollapsed: oe({}, u, {
              value: H
            }),
            groupEnd: oe({}, u, {
              value: he
            })
          });
        }
        ne < 0 && S("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var rr = w.ReactCurrentDispatcher, tr;
    function He(u, v, I) {
      {
        if (tr === void 0)
          try {
            throw Error();
          } catch (q) {
            var M = q.stack.trim().match(/\n( *(at )?)/);
            tr = M && M[1] || "";
          }
        return `
` + tr + u;
      }
    }
    var or = !1, Ue;
    {
      var _t = typeof WeakMap == "function" ? WeakMap : Map;
      Ue = new _t();
    }
    function kr(u, v) {
      if (!u || or)
        return "";
      {
        var I = Ue.get(u);
        if (I !== void 0)
          return I;
      }
      var M;
      or = !0;
      var q = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var K;
      K = rr.current, rr.current = null, Rt();
      try {
        if (v) {
          var A = function() {
            throw Error();
          };
          if (Object.defineProperty(A.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(A, []);
            } catch (xe) {
              M = xe;
            }
            Reflect.construct(u, [], A);
          } else {
            try {
              A.call();
            } catch (xe) {
              M = xe;
            }
            u.call(A.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (xe) {
            M = xe;
          }
          u();
        }
      } catch (xe) {
        if (xe && M && typeof xe.stack == "string") {
          for (var O = xe.stack.split(`
`), ae = M.stack.split(`
`), Z = O.length - 1, te = ae.length - 1; Z >= 1 && te >= 0 && O[Z] !== ae[te]; )
            te--;
          for (; Z >= 1 && te >= 0; Z--, te--)
            if (O[Z] !== ae[te]) {
              if (Z !== 1 || te !== 1)
                do
                  if (Z--, te--, te < 0 || O[Z] !== ae[te]) {
                    var ue = `
` + O[Z].replace(" at new ", " at ");
                    return u.displayName && ue.includes("<anonymous>") && (ue = ue.replace("<anonymous>", u.displayName)), typeof u == "function" && Ue.set(u, ue), ue;
                  }
                while (Z >= 1 && te >= 0);
              break;
            }
        }
      } finally {
        or = !1, rr.current = K, Lt(), Error.prepareStackTrace = q;
      }
      var Ie = u ? u.displayName || u.name : "", Br = Ie ? He(Ie) : "";
      return typeof u == "function" && Ue.set(u, Br), Br;
    }
    function Mt(u, v, I) {
      return kr(u, !1);
    }
    function Pt(u) {
      var v = u.prototype;
      return !!(v && v.isReactComponent);
    }
    function Ye(u, v, I) {
      if (u == null)
        return "";
      if (typeof u == "function")
        return kr(u, Pt(u));
      if (typeof u == "string")
        return He(u);
      switch (u) {
        case l:
          return He("Suspense");
        case d:
          return He("SuspenseList");
      }
      if (typeof u == "object")
        switch (u.$$typeof) {
          case f:
            return Mt(u.render);
          case h:
            return Ye(u.type, v, I);
          case x: {
            var M = u, q = M._payload, K = M._init;
            try {
              return Ye(K(q), v, I);
            } catch {
            }
          }
        }
      return "";
    }
    var Ve = Object.prototype.hasOwnProperty, Rr = {}, Lr = w.ReactDebugCurrentFrame;
    function We(u) {
      if (u) {
        var v = u._owner, I = Ye(u.type, u._source, v ? v.type : null);
        Lr.setExtraStackFrame(I);
      } else
        Lr.setExtraStackFrame(null);
    }
    function Dt(u, v, I, M, q) {
      {
        var K = Function.call.bind(Ve);
        for (var A in u)
          if (K(u, A)) {
            var O = void 0;
            try {
              if (typeof u[A] != "function") {
                var ae = Error((M || "React class") + ": " + I + " type `" + A + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof u[A] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ae.name = "Invariant Violation", ae;
              }
              O = u[A](v, A, M, I, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (Z) {
              O = Z;
            }
            O && !(O instanceof Error) && (We(q), S("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", M || "React class", I, A, typeof O), We(null)), O instanceof Error && !(O.message in Rr) && (Rr[O.message] = !0, We(q), S("Failed %s type: %s", I, O.message), We(null));
          }
      }
    }
    var $t = Array.isArray;
    function nr(u) {
      return $t(u);
    }
    function Ot(u) {
      {
        var v = typeof Symbol == "function" && Symbol.toStringTag, I = v && u[Symbol.toStringTag] || u.constructor.name || "Object";
        return I;
      }
    }
    function zt(u) {
      try {
        return _r(u), !1;
      } catch {
        return !0;
      }
    }
    function _r(u) {
      return "" + u;
    }
    function Mr(u) {
      if (zt(u))
        return S("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Ot(u)), _r(u);
    }
    var _e = w.ReactCurrentOwner, At = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, Pr, Dr, sr;
    sr = {};
    function Ft(u) {
      if (Ve.call(u, "ref")) {
        var v = Object.getOwnPropertyDescriptor(u, "ref").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return u.ref !== void 0;
    }
    function qt(u) {
      if (Ve.call(u, "key")) {
        var v = Object.getOwnPropertyDescriptor(u, "key").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return u.key !== void 0;
    }
    function Bt(u, v) {
      if (typeof u.ref == "string" && _e.current && v && _e.current.stateNode !== v) {
        var I = U(_e.current.type);
        sr[I] || (S('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', U(_e.current.type), u.ref), sr[I] = !0);
      }
    }
    function Ht(u, v) {
      {
        var I = function() {
          Pr || (Pr = !0, S("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        I.isReactWarning = !0, Object.defineProperty(u, "key", {
          get: I,
          configurable: !0
        });
      }
    }
    function Ut(u, v) {
      {
        var I = function() {
          Dr || (Dr = !0, S("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        I.isReactWarning = !0, Object.defineProperty(u, "ref", {
          get: I,
          configurable: !0
        });
      }
    }
    var Yt = function(u, v, I, M, q, K, A) {
      var O = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: r,
        // Built-in properties that belong on the element
        type: u,
        key: v,
        ref: I,
        props: A,
        // Record the component responsible for creating this element.
        _owner: K
      };
      return O._store = {}, Object.defineProperty(O._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(O, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: M
      }), Object.defineProperty(O, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: q
      }), Object.freeze && (Object.freeze(O.props), Object.freeze(O)), O;
    };
    function Vt(u, v, I, M, q) {
      {
        var K, A = {}, O = null, ae = null;
        I !== void 0 && (Mr(I), O = "" + I), qt(v) && (Mr(v.key), O = "" + v.key), Ft(v) && (ae = v.ref, Bt(v, q));
        for (K in v)
          Ve.call(v, K) && !At.hasOwnProperty(K) && (A[K] = v[K]);
        if (u && u.defaultProps) {
          var Z = u.defaultProps;
          for (K in Z)
            A[K] === void 0 && (A[K] = Z[K]);
        }
        if (O || ae) {
          var te = typeof u == "function" ? u.displayName || u.name || "Unknown" : u;
          O && Ht(A, te), ae && Ut(A, te);
        }
        return Yt(u, O, ae, q, M, _e.current, A);
      }
    }
    var ir = w.ReactCurrentOwner, $r = w.ReactDebugCurrentFrame;
    function Ce(u) {
      if (u) {
        var v = u._owner, I = Ye(u.type, u._source, v ? v.type : null);
        $r.setExtraStackFrame(I);
      } else
        $r.setExtraStackFrame(null);
    }
    var ar;
    ar = !1;
    function cr(u) {
      return typeof u == "object" && u !== null && u.$$typeof === r;
    }
    function Or() {
      {
        if (ir.current) {
          var u = U(ir.current.type);
          if (u)
            return `

Check the render method of \`` + u + "`.";
        }
        return "";
      }
    }
    function Wt(u) {
      {
        if (u !== void 0) {
          var v = u.fileName.replace(/^.*[\\\/]/, ""), I = u.lineNumber;
          return `

Check your code at ` + v + ":" + I + ".";
        }
        return "";
      }
    }
    var zr = {};
    function Gt(u) {
      {
        var v = Or();
        if (!v) {
          var I = typeof u == "string" ? u : u.displayName || u.name;
          I && (v = `

Check the top-level render call using <` + I + ">.");
        }
        return v;
      }
    }
    function Ar(u, v) {
      {
        if (!u._store || u._store.validated || u.key != null)
          return;
        u._store.validated = !0;
        var I = Gt(v);
        if (zr[I])
          return;
        zr[I] = !0;
        var M = "";
        u && u._owner && u._owner !== ir.current && (M = " It was passed a child from " + U(u._owner.type) + "."), Ce(u), S('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', I, M), Ce(null);
      }
    }
    function Fr(u, v) {
      {
        if (typeof u != "object")
          return;
        if (nr(u))
          for (var I = 0; I < u.length; I++) {
            var M = u[I];
            cr(M) && Ar(M, v);
          }
        else if (cr(u))
          u._store && (u._store.validated = !0);
        else if (u) {
          var q = C(u);
          if (typeof q == "function" && q !== u.entries)
            for (var K = q.call(u), A; !(A = K.next()).done; )
              cr(A.value) && Ar(A.value, v);
        }
      }
    }
    function Kt(u) {
      {
        var v = u.type;
        if (v == null || typeof v == "string")
          return;
        var I;
        if (typeof v == "function")
          I = v.propTypes;
        else if (typeof v == "object" && (v.$$typeof === f || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        v.$$typeof === h))
          I = v.propTypes;
        else
          return;
        if (I) {
          var M = U(v);
          Dt(I, u.props, "prop", M, u);
        } else if (v.PropTypes !== void 0 && !ar) {
          ar = !0;
          var q = U(v);
          S("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", q || "Unknown");
        }
        typeof v.getDefaultProps == "function" && !v.getDefaultProps.isReactClassApproved && S("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Qt(u) {
      {
        for (var v = Object.keys(u.props), I = 0; I < v.length; I++) {
          var M = v[I];
          if (M !== "children" && M !== "key") {
            Ce(u), S("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", M), Ce(null);
            break;
          }
        }
        u.ref !== null && (Ce(u), S("Invalid attribute `ref` supplied to `React.Fragment`."), Ce(null));
      }
    }
    function qr(u, v, I, M, q, K) {
      {
        var A = W(u);
        if (!A) {
          var O = "";
          (u === void 0 || typeof u == "object" && u !== null && Object.keys(u).length === 0) && (O += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ae = Wt(q);
          ae ? O += ae : O += Or();
          var Z;
          u === null ? Z = "null" : nr(u) ? Z = "array" : u !== void 0 && u.$$typeof === r ? (Z = "<" + (U(u.type) || "Unknown") + " />", O = " Did you accidentally export a JSX literal instead of a component?") : Z = typeof u, S("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", Z, O);
        }
        var te = Vt(u, v, I, q, K);
        if (te == null)
          return te;
        if (A) {
          var ue = v.children;
          if (ue !== void 0)
            if (M)
              if (nr(ue)) {
                for (var Ie = 0; Ie < ue.length; Ie++)
                  Fr(ue[Ie], u);
                Object.freeze && Object.freeze(ue);
              } else
                S("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              Fr(ue, u);
        }
        return u === n ? Qt(te) : Kt(te), te;
      }
    }
    function Xt(u, v, I) {
      return qr(u, v, I, !0);
    }
    function Jt(u, v, I) {
      return qr(u, v, I, !1);
    }
    var Zt = Jt, eo = Xt;
    De.Fragment = n, De.jsx = Zt, De.jsxs = eo;
  }()), De;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = mo() : e.exports = ho();
})(go);
const xo = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, bo = (e, r, t = !1) => g(["", ""], ({
  theme: n
}) => {
  let s, a, i;
  switch (e) {
    case "primary":
      s = r ? n.colors.primary : `${n.colors.primary}20`, a = r ? n.colors.textInverse : n.colors.primary, i = n.colors.primary;
      break;
    case "secondary":
      s = r ? n.colors.secondary : `${n.colors.secondary}20`, a = r ? n.colors.textInverse : n.colors.secondary, i = n.colors.secondary;
      break;
    case "success":
      s = r ? n.colors.success : `${n.colors.success}20`, a = r ? n.colors.textInverse : n.colors.success, i = n.colors.success;
      break;
    case "warning":
      s = r ? n.colors.warning : `${n.colors.warning}20`, a = r ? n.colors.textInverse : n.colors.warning, i = n.colors.warning;
      break;
    case "error":
      s = r ? n.colors.error : `${n.colors.error}20`, a = r ? n.colors.textInverse : n.colors.error, i = n.colors.error;
      break;
    case "info":
      s = r ? n.colors.info : `${n.colors.info}20`, a = r ? n.colors.textInverse : n.colors.info, i = n.colors.info;
      break;
    case "neutral":
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}10`, a = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
      break;
    default:
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}20`, a = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
  }
  return t ? `
          background-color: transparent;
          color: ${i};
          border: 1px solid ${i};
        ` : `
        background-color: ${s};
        color: ${a};
        border: 1px solid transparent;
      `;
}), St = /* @__PURE__ */ c.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), yo = /* @__PURE__ */ c(St).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), vo = /* @__PURE__ */ c(St).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), wo = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: r,
  dot: t
}) => t ? "50%" : r ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => xo[e], ({
  variant: e,
  solid: r,
  outlined: t
}) => bo(e, r, t || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), Ze = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  solid: n = !1,
  className: s = "",
  style: a,
  onClick: i,
  rounded: p = !1,
  dot: f = !1,
  counter: l = !1,
  outlined: d = !1,
  startIcon: h,
  endIcon: x,
  max: y,
  inline: m = !0
}) => {
  let b = e;
  return l && typeof e == "number" && y !== void 0 && e > y && (b = `${y}+`), /* @__PURE__ */ o.jsx(wo, { variant: r, size: t, solid: n, clickable: !!i, className: s, style: a, onClick: i, rounded: p, dot: f, counter: l, outlined: d, inline: m, children: !f && /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    h && /* @__PURE__ */ o.jsx(yo, { children: h }),
    b,
    x && /* @__PURE__ */ o.jsx(vo, { children: x })
  ] }) });
}, So = /* @__PURE__ */ Le(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Co = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], So, ({
  theme: e
}) => e.spacing.xs), Io = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, jo = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, To = /* @__PURE__ */ c.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => Io[e], ({
  variant: e = "primary"
}) => jo[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: r
}) => r.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: r
}) => r.spacing.xs)), Eo = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), de = ({
  children: e,
  variant: r = "primary",
  disabled: t = !1,
  loading: n = !1,
  size: s = "medium",
  fullWidth: a = !1,
  startIcon: i,
  endIcon: p,
  onClick: f,
  className: l = "",
  type: d = "button",
  ...h
}) => /* @__PURE__ */ o.jsx(To, { variant: r, disabled: t || n, size: s, fullWidth: a, onClick: f, className: l, type: d, $hasStartIcon: !!i && !n, $hasEndIcon: !!p && !n, ...h, children: /* @__PURE__ */ o.jsxs(Eo, { children: [
  n && /* @__PURE__ */ o.jsx(Co, {}),
  !n && i,
  e,
  !n && p
] }) }), No = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), ko = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Ro = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Yr = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Lo = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), _o = /* @__PURE__ */ c.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Mo = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), je = ({
  value: e,
  onChange: r,
  placeholder: t = "",
  disabled: n = !1,
  error: s = "",
  type: a = "text",
  name: i = "",
  id: p = "",
  className: f = "",
  required: l = !1,
  autoComplete: d = "",
  label: h = "",
  helperText: x = "",
  startIcon: y,
  endIcon: m,
  loading: b = !1,
  success: C = !1,
  clearable: w = !1,
  onClear: S,
  maxLength: _,
  showCharCount: z = !1,
  size: $ = "medium",
  fullWidth: R = !1,
  ...E
}) => {
  const [L, N] = V(!1), W = yt(null), G = () => {
    S ? S() : r(""), W.current && W.current.focus();
  }, k = (se) => {
    N(!0), E.onFocus && E.onFocus(se);
  }, U = (se) => {
    N(!1), E.onBlur && E.onBlur(se);
  }, oe = w && e && !n, ne = (e == null ? void 0 : e.length) || 0, fe = z || _ !== void 0 && _ > 0;
  return /* @__PURE__ */ o.jsxs(No, { className: f, fullWidth: R, children: [
    h && /* @__PURE__ */ o.jsxs(ko, { htmlFor: p, children: [
      h,
      l && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(Ro, { hasError: !!s, hasSuccess: !!C, disabled: !!n, $size: $, hasStartIcon: !!y, hasEndIcon: !!(m || oe), isFocused: !!L, children: [
      y && /* @__PURE__ */ o.jsx(Yr, { children: y }),
      /* @__PURE__ */ o.jsx(
        Lo,
        {
          ref: W,
          type: a,
          value: e,
          onChange: (se) => r(se.target.value),
          placeholder: t,
          disabled: !!(n || b),
          name: i,
          id: p,
          required: !!l,
          autoComplete: d,
          hasStartIcon: !!y,
          hasEndIcon: !!(m || oe),
          $size: $,
          maxLength: _,
          onFocus: k,
          onBlur: U,
          ...E
        }
      ),
      oe && /* @__PURE__ */ o.jsx(_o, { type: "button", onClick: G, tabIndex: -1, children: "✕" }),
      m && /* @__PURE__ */ o.jsx(Yr, { children: m })
    ] }),
    (s || x || fe) && /* @__PURE__ */ o.jsxs(Mo, { hasError: !!s, hasSuccess: !!C, children: [
      /* @__PURE__ */ o.jsx("div", { children: s || x }),
      fe && /* @__PURE__ */ o.jsxs("div", { children: [
        ne,
        _ !== void 0 && `/${_}`
      ] })
    ] })
  ] });
}, Vr = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, Po = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, Do = /* @__PURE__ */ Le(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), $o = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: r,
  customWidth: t
}) => e === "custom" ? Vr.custom({
  customHeight: r,
  customWidth: t
}) : Vr[e], ({
  variant: e
}) => Po[e]), Oo = /* @__PURE__ */ c.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, Do, ({
  theme: e
}) => e.spacing.sm), zo = /* @__PURE__ */ c.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), Ao = ({
  variant: e = "default",
  size: r = "medium",
  height: t = "200px",
  width: n = "",
  text: s = "Loading...",
  showSpinner: a = !0,
  className: i = ""
}) => /* @__PURE__ */ o.jsxs($o, { variant: e, size: r, customHeight: t, customWidth: n, className: i, children: [
  a && /* @__PURE__ */ o.jsx(Oo, {}),
  s && /* @__PURE__ */ o.jsx(zo, { children: s })
] }), Fo = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), qo = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Bo = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Ho = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Uo = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Yo = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), Vo = /* @__PURE__ */ c.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), $e = ({
  options: e,
  value: r,
  onChange: t,
  disabled: n = !1,
  error: s = "",
  name: a = "",
  id: i = "",
  className: p = "",
  required: f = !1,
  placeholder: l = "",
  label: d = "",
  helperText: h = "",
  size: x = "medium",
  fullWidth: y = !0,
  loading: m = !1,
  success: b = !1,
  startIcon: C,
  ...w
}) => {
  const [S, _] = V(!1), z = (N) => {
    _(!0), w.onFocus && w.onFocus(N);
  }, $ = (N) => {
    _(!1), w.onBlur && w.onBlur(N);
  }, R = {}, E = [];
  e.forEach((N) => {
    N.group ? (R[N.group] || (R[N.group] = []), R[N.group].push(N)) : E.push(N);
  });
  const L = Object.keys(R).length > 0;
  return /* @__PURE__ */ o.jsxs(Fo, { className: p, fullWidth: y, children: [
    d && /* @__PURE__ */ o.jsxs(qo, { htmlFor: i, children: [
      d,
      f && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(Bo, { hasError: !!s, hasSuccess: !!b, disabled: !!(n || m), $size: x, hasStartIcon: !!C, isFocused: !!S, children: [
      C && /* @__PURE__ */ o.jsx(Ho, { children: C }),
      /* @__PURE__ */ o.jsxs(
        Uo,
        {
          value: r,
          onChange: (N) => t(N.target.value),
          disabled: !!(n || m),
          name: a,
          id: i,
          required: !!f,
          hasStartIcon: !!C,
          $size: x,
          onFocus: z,
          onBlur: $,
          ...w,
          children: [
            l && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: l }),
            L ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
              E.map((N) => /* @__PURE__ */ o.jsx("option", { value: N.value, disabled: N.disabled, children: N.label }, N.value)),
              Object.entries(R).map(([N, W]) => /* @__PURE__ */ o.jsx(Vo, { label: N, children: W.map((G) => /* @__PURE__ */ o.jsx("option", { value: G.value, disabled: G.disabled, children: G.label }, G.value)) }, N))
            ] }) : (
              // Render all options without groups
              e.map((N) => /* @__PURE__ */ o.jsx("option", { value: N.value, disabled: N.disabled, children: N.label }, N.value))
            )
          ]
        }
      )
    ] }),
    (s || h) && /* @__PURE__ */ o.jsx(Yo, { hasError: !!s, hasSuccess: !!b, children: /* @__PURE__ */ o.jsx("div", { children: s || h }) })
  ] });
}, Wr = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, Wo = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, Go = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), Ko = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), Qo = /* @__PURE__ */ c.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => Wr[e], ({
  size: e
}) => Wr[e], ({
  status: e,
  theme: r,
  pulse: t
}) => {
  let n, s;
  switch (e) {
    case "success":
      n = r.colors.success, s = "76, 175, 80";
      break;
    case "error":
      n = r.colors.error, s = "244, 67, 54";
      break;
    case "warning":
      n = r.colors.warning, s = "255, 152, 0";
      break;
    case "info":
      n = r.colors.info, s = "33, 150, 243";
      break;
    default:
      n = r.colors.textSecondary, s = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], n, t && g(["--pulse-color:", ";", ""], s, Go));
}), Xo = /* @__PURE__ */ c.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => Wo[e], ({
  status: e,
  theme: r
}) => {
  let t;
  switch (e) {
    case "success":
      t = r.colors.success;
      break;
    case "error":
      t = r.colors.error;
      break;
    case "warning":
      t = r.colors.warning;
      break;
    case "info":
      t = r.colors.info;
      break;
    default:
      t = r.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], t, r.fontWeights.medium);
}), cc = ({
  status: e,
  size: r = "medium",
  pulse: t = !1,
  showLabel: n = !1,
  label: s = "",
  className: a = ""
}) => {
  const i = s || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ o.jsxs(Ko, { className: a, children: [
    /* @__PURE__ */ o.jsx(Qo, { status: e, size: r, pulse: t }),
    n && /* @__PURE__ */ o.jsx(Xo, { status: e, size: r, children: i })
  ] });
}, Jo = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, Zo = (e) => g(["", ""], ({
  theme: r
}) => {
  let t, n, s;
  switch (e) {
    case "primary":
      t = `${r.colors.primary}10`, n = r.colors.primary, s = `${r.colors.primary}30`;
      break;
    case "secondary":
      t = `${r.colors.secondary}10`, n = r.colors.secondary, s = `${r.colors.secondary}30`;
      break;
    case "success":
      t = `${r.colors.success}10`, n = r.colors.success, s = `${r.colors.success}30`;
      break;
    case "warning":
      t = `${r.colors.warning}10`, n = r.colors.warning, s = `${r.colors.warning}30`;
      break;
    case "error":
      t = `${r.colors.error}10`, n = r.colors.error, s = `${r.colors.error}30`;
      break;
    case "info":
      t = `${r.colors.info}10`, n = r.colors.info, s = `${r.colors.info}30`;
      break;
    default:
      t = `${r.colors.textSecondary}10`, n = r.colors.textSecondary, s = `${r.colors.textSecondary}30`;
  }
  return `
        background-color: ${t};
        color: ${n};
        border: 1px solid ${s};
      `;
}), en = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => Jo[e], ({
  variant: e
}) => Zo(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), rn = /* @__PURE__ */ c.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${t[e]};
      height: ${t[e]};
      font-size: ${r.fontSizes.xs};
    `;
}), lc = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  removable: n = !1,
  onRemove: s,
  className: a = "",
  onClick: i
}) => {
  const p = (f) => {
    f.stopPropagation(), s == null || s();
  };
  return /* @__PURE__ */ o.jsxs(en, { variant: r, size: t, clickable: !!i, className: a, onClick: i, children: [
    e,
    n && /* @__PURE__ */ o.jsx(rn, { size: t, onClick: p, children: "×" })
  ] });
}, tn = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimePickerContainer",
  componentId: "sc-v5w9zw-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), on = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-v5w9zw-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), nn = /* @__PURE__ */ c.input.withConfig({
  displayName: "TimeInput",
  componentId: "sc-v5w9zw-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), sn = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  label: s,
  required: a = !1,
  disabled: i = !1,
  className: p,
  placeholder: f = "HH:MM",
  min: l,
  max: d
}) => /* @__PURE__ */ o.jsxs(tn, { className: p, children: [
  s && /* @__PURE__ */ o.jsxs(on, { htmlFor: e, children: [
    s,
    a && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsx(nn, { id: e, name: r, type: "time", value: t, onChange: n, required: a, disabled: i, placeholder: f, min: l, max: d })
] }), dc = sn, er = () => Intl.DateTimeFormat().resolvedOptions().timeZone, fr = (e, r = /* @__PURE__ */ new Date()) => {
  const s = new Intl.DateTimeFormat("en", {
    timeZone: e,
    timeZoneName: "short"
  }).formatToParts(r).find((a) => a.type === "timeZoneName");
  return (s == null ? void 0 : s.value) || e;
}, Fe = (e, r) => {
  const t = r || er(), [n, s] = e.split(":").map(Number), a = /* @__PURE__ */ new Date(), i = new Date(a.getFullYear(), a.getMonth(), a.getDate(), n, s, 0), p = new Date(i.toLocaleString("en-US", {
    timeZone: "America/New_York"
  })), l = new Date(i.toLocaleString("en-US", {
    timeZone: "UTC"
  })).getTime() - p.getTime();
  return new Date(i.getTime() + l).toLocaleTimeString("en-GB", {
    timeZone: t,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, uc = (e, r) => {
  r || er();
  const t = /* @__PURE__ */ new Date();
  return (/* @__PURE__ */ new Date(`${t.toDateString()} ${e}:00`)).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, Xe = (e) => {
  const r = e || er(), t = /* @__PURE__ */ new Date(), n = t.toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), s = t.toLocaleTimeString("en-GB", {
    timeZone: r,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), a = fr("America/New_York", t), i = fr(r, t);
  return {
    nyTime: n,
    localTime: s,
    nyTimezone: a,
    localTimezone: i,
    formatted: `${n} ${a} | ${s} ${i}`
  };
}, pc = () => {
  const r = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
  return Te(r);
}, fc = () => {
  const e = /* @__PURE__ */ new Date();
  return new Date(e.toLocaleString("en-US", {
    timeZone: "America/New_York"
  }));
}, an = (e) => {
  const r = Math.floor(e / 60), t = e % 60;
  let n = "";
  return r > 0 ? n = t > 0 ? `${r}h ${t}m` : `${r}h` : n = `${t}m`, {
    totalMinutes: e,
    hours: r,
    minutes: t,
    formatted: n
  };
}, Ee = (e) => {
  const r = /* @__PURE__ */ new Date(), t = new Date(r.toLocaleString("en-US", {
    timeZone: "America/New_York"
  })), [n, s] = e.split(":").map(Number), a = new Date(t);
  a.setHours(n, s, 0, 0), a <= t && a.setDate(a.getDate() + 1);
  const i = a.getTime() - t.getTime(), p = Math.floor(i / (1e3 * 60));
  return an(p);
}, cn = (e) => Ee(e), Be = (e, r, t) => {
  const n = t || er(), s = Fe(e, n), a = Fe(r, n), i = fr(n);
  return {
    nyStart: e,
    nyEnd: r,
    localStart: s,
    localEnd: a,
    formatted: `${e}-${r} NY | ${s}-${a} ${i}`
  };
}, ln = (e, r) => {
  const n = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), s = Te(n), a = Te(e), i = Te(r);
  return s >= a && s <= i;
}, Te = (e) => {
  const [r, t] = e.split(":").map(Number);
  return r * 60 + t;
}, gc = (e) => {
  const r = Math.floor(e / 60), t = e % 60;
  return `${r.toString().padStart(2, "0")}:${t.toString().padStart(2, "0")}`;
}, mc = (e) => {
  const t = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), n = Te(t);
  for (const s of e)
    if (Te(s.nyStart) > n)
      return {
        nextSession: s.name,
        timeUntil: Ee(s.nyStart),
        sessionTime: Be(s.nyStart, s.nyEnd)
      };
  if (e.length > 0) {
    const s = e[0], a = Ee(s.nyStart);
    return {
      nextSession: s.name,
      timeUntil: a,
      sessionTime: Be(s.nyStart, s.nyEnd)
    };
  }
  return null;
}, dn = (e) => {
  const r = e.localTimezone.includes("GMT") ? "🇮🇪" : "🌍";
  return `${e.localTime} ${r} | ${e.nyTime} 🇺🇸`;
}, hc = (e) => `${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`, xc = (e, r, t, n) => {
  const s = ln(r, t), a = Be(r, t, n);
  if (s)
    return {
      isActive: !0,
      timeRemaining: cn(t),
      sessionTime: a,
      status: "active"
    };
  const i = Ee(r);
  return {
    isActive: !1,
    timeUntilStart: i,
    sessionTime: a,
    status: i.totalMinutes < 24 * 60 ? "upcoming" : "ended"
  };
}, bc = () => {
  console.log("🕐 TIMEZONE CONVERSION TEST:"), console.log("09:00 NY →", Fe("09:00", "Europe/Dublin")), console.log("11:50 NY →", Fe("11:50", "Europe/Dublin")), console.log("15:15 NY →", Fe("15:15", "Europe/Dublin"));
  const e = Xe("Europe/Dublin");
  console.log("Current dual time:", e.formatted);
}, ve = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimeContainer",
  componentId: "sc-10dqpqu-0"
})(["display:flex;align-items:center;gap:", ";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"], ({
  format: e
}) => e === "mobile" ? "4px" : "8px"), Ne = /* @__PURE__ */ c.span.withConfig({
  displayName: "NYTime",
  componentId: "sc-10dqpqu-1"
})(["color:#3b82f6;font-size:inherit;"]), ke = /* @__PURE__ */ c.span.withConfig({
  displayName: "LocalTime",
  componentId: "sc-10dqpqu-2"
})(["color:#10b981;font-size:inherit;"]), Re = /* @__PURE__ */ c.span.withConfig({
  displayName: "Separator",
  componentId: "sc-10dqpqu-3"
})(["color:#6b7280;font-size:inherit;"]), Je = /* @__PURE__ */ c.span.withConfig({
  displayName: "Timezone",
  componentId: "sc-10dqpqu-4"
})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]), lr = /* @__PURE__ */ c.span.withConfig({
  displayName: "LiveIndicator",
  componentId: "sc-10dqpqu-5"
})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]), Gr = /* @__PURE__ */ c.div.withConfig({
  displayName: "CountdownContainer",
  componentId: "sc-10dqpqu-6"
})(["display:flex;align-items:center;gap:8px;"]), Kr = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownValue",
  componentId: "sc-10dqpqu-7"
})(["color:#f59e0b;font-weight:bold;"]), dr = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownLabel",
  componentId: "sc-10dqpqu-8"
})(["color:#9ca3af;font-size:0.9em;"]), un = ({
  format: e,
  showLive: r,
  updateInterval: t
}) => {
  const [n, s] = V(Xe());
  return le(() => {
    const a = setInterval(() => {
      s(Xe());
    }, t * 1e3);
    return () => clearInterval(a);
  }, [t]), e === "mobile" ? /* @__PURE__ */ o.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ o.jsx("span", { children: dn(n) }),
    r && /* @__PURE__ */ o.jsx(lr, { children: "LIVE" })
  ] }) : e === "compact" ? /* @__PURE__ */ o.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ o.jsx(Ne, { children: n.nyTime }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsx(ke, { children: n.localTime }),
    r && /* @__PURE__ */ o.jsx(lr, { children: "LIVE" })
  ] }) : /* @__PURE__ */ o.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ o.jsx(Ne, { children: n.nyTime }),
    /* @__PURE__ */ o.jsx(Je, { children: n.nyTimezone }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsx(ke, { children: n.localTime }),
    /* @__PURE__ */ o.jsx(Je, { children: n.localTimezone }),
    r && /* @__PURE__ */ o.jsx(lr, { children: "LIVE" })
  ] });
}, pn = ({
  nyTime: e,
  format: r
}) => {
  const t = Xe(), n = Be(e, e);
  return r === "mobile" ? /* @__PURE__ */ o.jsx(ve, { format: r, children: /* @__PURE__ */ o.jsxs("span", { children: [
    n.localStart,
    " 🇮🇪 | ",
    e,
    " 🇺🇸"
  ] }) }) : r === "compact" ? /* @__PURE__ */ o.jsxs(ve, { format: r, children: [
    /* @__PURE__ */ o.jsx(Ne, { children: e }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsx(ke, { children: n.localStart })
  ] }) : /* @__PURE__ */ o.jsxs(ve, { format: r, children: [
    /* @__PURE__ */ o.jsx(Ne, { children: e }),
    /* @__PURE__ */ o.jsx(Je, { children: t.nyTimezone }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsx(ke, { children: n.localStart }),
    /* @__PURE__ */ o.jsx(Je, { children: t.localTimezone })
  ] });
}, fn = ({
  targetNYTime: e,
  format: r,
  updateInterval: t
}) => {
  const [n, s] = V(Ee(e));
  return le(() => {
    const a = setInterval(() => {
      s(Ee(e));
    }, t * 1e3);
    return () => clearInterval(a);
  }, [e, t]), r === "mobile" ? /* @__PURE__ */ o.jsxs(Gr, { children: [
    /* @__PURE__ */ o.jsx(Kr, { children: n.formatted }),
    /* @__PURE__ */ o.jsxs(dr, { children: [
      "until ",
      e
    ] })
  ] }) : /* @__PURE__ */ o.jsxs(Gr, { children: [
    /* @__PURE__ */ o.jsx(dr, { children: "Next in:" }),
    /* @__PURE__ */ o.jsx(Kr, { children: n.formatted }),
    /* @__PURE__ */ o.jsxs(dr, { children: [
      "(",
      e,
      " NY)"
    ] })
  ] });
}, gn = ({
  sessionStart: e,
  sessionEnd: r,
  format: t
}) => {
  const n = Be(e, r);
  return t === "mobile" ? /* @__PURE__ */ o.jsx(ve, { format: t, children: /* @__PURE__ */ o.jsx("span", { children: n.formatted }) }) : t === "compact" ? /* @__PURE__ */ o.jsxs(ve, { format: t, children: [
    /* @__PURE__ */ o.jsxs(Ne, { children: [
      e,
      "-",
      r
    ] }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsxs(ke, { children: [
      n.localStart,
      "-",
      n.localEnd
    ] })
  ] }) : /* @__PURE__ */ o.jsxs(ve, { format: t, children: [
    /* @__PURE__ */ o.jsx("div", { children: /* @__PURE__ */ o.jsxs(Ne, { children: [
      e,
      "-",
      r,
      " NY"
    ] }) }),
    /* @__PURE__ */ o.jsx(Re, { children: "|" }),
    /* @__PURE__ */ o.jsx("div", { children: /* @__PURE__ */ o.jsxs(ke, { children: [
      n.localStart,
      "-",
      n.localEnd,
      " Local"
    ] }) })
  ] });
}, yc = ({
  mode: e = "current",
  nyTime: r,
  targetNYTime: t,
  sessionStart: n,
  sessionEnd: s,
  format: a = "desktop",
  showLive: i = !1,
  className: p,
  updateInterval: f = 1
}) => {
  const l = {
    className: p,
    style: {
      fontSize: a === "mobile" ? "14px" : a === "compact" ? "13px" : "14px"
    }
  };
  switch (e) {
    case "static":
      return r ? /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(pn, { nyTime: r, format: a }) }) : (console.warn("DualTimeDisplay: nyTime is required for static mode"), null);
    case "countdown":
      return t ? /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(fn, { targetNYTime: t, format: a, updateInterval: f }) }) : (console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"), null);
    case "session":
      return !n || !s ? (console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"), null) : /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(gn, { sessionStart: n, sessionEnd: s, format: a }) });
    case "current":
    default:
      return /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(un, { format: a, showLive: i, updateInterval: f }) });
  }
}, mn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-w0dp8e-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), hn = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-w0dp8e-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), xn = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-w0dp8e-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), bn = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  options: s,
  label: a,
  required: i = !1,
  disabled: p = !1,
  className: f,
  placeholder: l
}) => /* @__PURE__ */ o.jsxs(mn, { className: f, children: [
  a && /* @__PURE__ */ o.jsxs(hn, { htmlFor: e, children: [
    a,
    i && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsxs(xn, { id: e, name: r, value: t, onChange: n, required: i, disabled: p, children: [
    l && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: l }),
    s.map((d) => /* @__PURE__ */ o.jsx("option", { value: d.value, children: d.label }, d.value))
  ] })
] }), vc = bn, yn = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledLoadingCell",
  componentId: "sc-1i0qdjp-0"
})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;", " border-radius:", ";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"], ({
  $size: e,
  theme: r
}) => {
  var t, n, s, a, i, p, f, l, d;
  switch (e) {
    case "small":
      return g(["font-size:", ";padding:", " ", ";"], ((t = r.fontSizes) == null ? void 0 : t.xs) || "12px", ((n = r.spacing) == null ? void 0 : n.xxs) || "2px", ((s = r.spacing) == null ? void 0 : s.xs) || "4px");
    case "large":
      return g(["font-size:", ";padding:", " ", ";"], ((a = r.fontSizes) == null ? void 0 : a.lg) || "18px", ((i = r.spacing) == null ? void 0 : i.sm) || "8px", ((p = r.spacing) == null ? void 0 : p.md) || "12px");
    default:
      return g(["font-size:", ";padding:", " ", ";"], ((f = r.fontSizes) == null ? void 0 : f.sm) || "14px", ((l = r.spacing) == null ? void 0 : l.xs) || "4px", ((d = r.spacing) == null ? void 0 : d.sm) || "8px");
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}), vn = /* @__PURE__ */ c.span.withConfig({
  displayName: "LoadingPlaceholder",
  componentId: "sc-1i0qdjp-1"
})(["display:inline-block;width:", ";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"], ({
  $width: e
}) => e || "60px"), wn = (e) => {
  const {
    size: r = "medium",
    width: t,
    className: n,
    "aria-label": s
  } = e;
  return /* @__PURE__ */ o.jsx(yn, { className: n, $size: r, $width: t, "aria-label": s || "Loading data", role: "cell", "aria-busy": "true", children: /* @__PURE__ */ o.jsx(vn, { $width: t }) });
}, wc = wn, Sn = /* @__PURE__ */ Le(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Cn = /* @__PURE__ */ Le(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]), In = /* @__PURE__ */ c.div.withConfig({
  displayName: "StyledSpinner",
  componentId: "sc-1hoaoss-0"
})(["display:inline-block;position:relative;", " &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;", " animation:", " ", "s linear infinite;}", ""], ({
  $size: e
}) => {
  switch (e) {
    case "xs":
      return g(["width:16px;height:16px;"]);
    case "sm":
      return g(["width:20px;height:20px;"]);
    case "md":
      return g(["width:32px;height:32px;"]);
    case "lg":
      return g(["width:48px;height:48px;"]);
    case "xl":
      return g(["width:64px;height:64px;"]);
    default:
      return g(["width:32px;height:32px;"]);
  }
}, ({
  $variant: e,
  theme: r
}) => {
  var t, n, s, a, i, p;
  switch (e) {
    case "primary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primary) || "#dc2626");
    case "secondary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((s = r.colors) == null ? void 0 : s.textSecondary) || "#9ca3af", ((a = r.colors) == null ? void 0 : a.textSecondary) || "#9ca3af");
    case "white":
      return g(["border-top-color:#ffffff;border-right-color:#ffffff;"]);
    case "red":
      return g(["border-top-color:#dc2626;border-right-color:#dc2626;"]);
    default:
      return g(["border-top-color:", ";border-right-color:", ";"], ((i = r.colors) == null ? void 0 : i.primary) || "#dc2626", ((p = r.colors) == null ? void 0 : p.primary) || "#dc2626");
  }
}, Sn, ({
  $speed: e
}) => 1 / e, ({
  $showStripes: e,
  $variant: r,
  theme: t
}) => e && g(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:", ";background-size:8px 8px;animation:", " ", "s linear infinite;}"], r === "red" || r === "primary" ? "linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)" : "linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)", Cn, (n) => 2 / n.$speed)), jn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SpinnerContainer",
  componentId: "sc-1hoaoss-1"
})(["display:inline-flex;align-items:center;justify-content:center;"]), Tn = (e) => {
  const {
    size: r = "md",
    variant: t = "primary",
    className: n,
    "aria-label": s,
    speed: a = 1,
    showStripes: i = !1
  } = e;
  return /* @__PURE__ */ o.jsx(jn, { className: n, children: /* @__PURE__ */ o.jsx(In, { $size: r, $variant: t, $speed: a, $showStripes: i, role: "status", "aria-label": s || "Loading", "aria-live": "polite" }) });
}, Sc = Tn, En = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, Nn = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, kn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";"], r.colors.border), ({
  padding: e
}) => En[e], ({
  variant: e
}) => Nn[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: r
}) => r.shadows.md)), Rn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), Ln = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), _n = /* @__PURE__ */ c.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Mn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), Pn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Dn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), $n = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), On = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), zn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), An = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), Fn = ({
  children: e,
  title: r = "",
  subtitle: t = "",
  bordered: n = !0,
  variant: s = "default",
  padding: a = "medium",
  className: i = "",
  footer: p,
  actions: f,
  isLoading: l = !1,
  hasError: d = !1,
  errorMessage: h = "An error occurred",
  clickable: x = !1,
  onClick: y,
  ...m
}) => {
  const b = r || t || f;
  return /* @__PURE__ */ o.jsxs(kn, { bordered: n, variant: s, padding: a, clickable: x, className: i, onClick: x ? y : void 0, ...m, children: [
    l && /* @__PURE__ */ o.jsx(On, { children: /* @__PURE__ */ o.jsx(An, {}) }),
    b && /* @__PURE__ */ o.jsxs(Rn, { children: [
      /* @__PURE__ */ o.jsxs(Ln, { children: [
        r && /* @__PURE__ */ o.jsx(_n, { children: r }),
        t && /* @__PURE__ */ o.jsx(Mn, { children: t })
      ] }),
      f && /* @__PURE__ */ o.jsx(Pn, { children: f })
    ] }),
    d && /* @__PURE__ */ o.jsx(zn, { children: /* @__PURE__ */ o.jsx("p", { children: h }) }),
    /* @__PURE__ */ o.jsx(Dn, { children: e }),
    p && /* @__PURE__ */ o.jsx($n, { children: p })
  ] });
}, qn = /* @__PURE__ */ c.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.md,
    medium: r.fontSizes.lg,
    large: r.fontSizes.xl
  };
  return g(["font-size:", ";"], t[e]);
}), Bn = /* @__PURE__ */ c.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.sm,
    medium: r.fontSizes.md,
    large: r.fontSizes.lg
  };
  return g(["font-size:", ";"], t[e]);
}), Hn = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, Un = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => Hn[e], ({
  size: e,
  theme: r
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], r.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], r.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], r.spacing.lg);
  }
}), Yn = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], t[e], t[e], t[e], r.colors.textSecondary);
}), Vn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), Wn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), Qr = ({
  title: e = "",
  description: r = "",
  icon: t,
  actionText: n = "",
  onAction: s,
  variant: a = "default",
  size: i = "medium",
  className: p = "",
  children: f
}) => /* @__PURE__ */ o.jsxs(Un, { variant: a, size: i, className: p, children: [
  t && /* @__PURE__ */ o.jsx(Yn, { size: i, children: t }),
  e && /* @__PURE__ */ o.jsx(qn, { size: i, children: e }),
  r && /* @__PURE__ */ o.jsx(Bn, { size: i, children: r }),
  n && s && /* @__PURE__ */ o.jsx(Vn, { children: /* @__PURE__ */ o.jsx(de, { variant: "primary", size: i === "small" ? "small" : "medium", onClick: s, children: n }) }),
  f && /* @__PURE__ */ o.jsx(Wn, { children: f })
] }), Xr = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), Gn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), Jr = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Ge = /* @__PURE__ */ c.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), Zr = /* @__PURE__ */ c.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), et = /* @__PURE__ */ c.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), Kn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), Ct = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), Qn = /* @__PURE__ */ c.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), Xn = /* @__PURE__ */ c(Ct).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), Jn = ({
  error: e,
  resetError: r,
  isAppLevel: t,
  name: n,
  onSkip: s
}) => {
  const a = () => {
    window.location.reload();
  };
  return t ? /* @__PURE__ */ o.jsx(Xr, { isAppLevel: !0, children: /* @__PURE__ */ o.jsxs(Gn, { children: [
    /* @__PURE__ */ o.jsx(Jr, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Ge, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ o.jsxs(Zr, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Ge, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(et, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsx(Xn, { onClick: a, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ o.jsxs(Xr, { children: [
    /* @__PURE__ */ o.jsx(Jr, { children: n ? `Error in ${n}` : "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Ge, { children: n ? `We encountered a problem while loading ${n}. You can try again${s ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ o.jsxs(Zr, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Ge, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(et, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsxs(Kn, { children: [
      /* @__PURE__ */ o.jsx(Ct, { onClick: r, children: "Try Again" }),
      s && /* @__PURE__ */ o.jsx(Qn, { onClick: s, children: "Skip This Feature" })
    ] })
  ] });
};
class Zn extends ro {
  constructor(r) {
    super(r), this.resetError = () => {
      this.setState({
        hasError: !1,
        error: null
      });
    }, this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(r) {
    return {
      hasError: !0,
      error: r
    };
  }
  componentDidCatch(r, t) {
    const {
      name: n
    } = this.props, s = n ? `ErrorBoundary(${n})` : "ErrorBoundary";
    console.error(`Error caught by ${s}:`, r, t), this.props.onError && this.props.onError(r, t);
  }
  componentDidUpdate(r) {
    this.state.hasError && this.props.resetOnPropsChange && r.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: r,
      error: t
    } = this.state, {
      children: n,
      fallback: s,
      name: a,
      isFeatureBoundary: i,
      onSkip: p
    } = this.props;
    return r && t ? typeof s == "function" ? s({
      error: t,
      resetError: this.resetError
    }) : s || /* @__PURE__ */ o.jsx(Jn, { error: t, resetError: this.resetError, isAppLevel: !i, name: a, onSkip: p }) : n;
  }
}
const It = ({
  isAppLevel: e = !1,
  isFeatureBoundary: r = !1,
  children: t,
  ...n
}) => {
  const s = e ? "app" : r ? "feature" : "component", a = {
    resetOnPropsChange: s !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: s !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: s === "feature"
  };
  return /* @__PURE__ */ o.jsx(Zn, { ...a, ...n, children: t });
}, Cc = (e) => /* @__PURE__ */ o.jsx(It, { isAppLevel: !0, ...e }), Ic = ({
  featureName: e,
  children: r,
  ...t
}) => /* @__PURE__ */ o.jsx(It, { isFeatureBoundary: !0, name: e, children: r, ...t }), es = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContainer",
  componentId: "sc-lgz9vh-0"
})(["display:flex;flex-direction:column;width:100%;"]), rs = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabList",
  componentId: "sc-lgz9vh-1"
})(["display:flex;border-bottom:1px solid ", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md), ts = /* @__PURE__ */ c.button.withConfig({
  displayName: "TabButton",
  componentId: "sc-lgz9vh-2"
})(["padding:", " ", ";background:none;border:none;border-bottom:2px solid ", ";color:", ";font-weight:", ";cursor:pointer;transition:all ", ";&:hover{color:", ";}&:focus{outline:none;color:", ";}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md, ({
  active: e,
  theme: r
}) => e ? r.colors.primary : "transparent", ({
  active: e,
  theme: r
}) => e ? r.colors.primary : r.colors.textSecondary, ({
  active: e,
  theme: r
}) => e ? r.fontWeights.semibold : r.fontWeights.regular, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary), os = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContent",
  componentId: "sc-lgz9vh-3"
})(["padding:", " 0;"], ({
  theme: e
}) => e.spacing.sm), ns = ({
  tabs: e,
  defaultTab: r,
  className: t,
  activeTab: n,
  onTabClick: s
}) => {
  var l;
  const [a, i] = V(r || e[0].id), p = n !== void 0 ? n : a, f = (d, h) => {
    d.preventDefault(), d.stopPropagation(), s ? s(h) : i(h);
  };
  return /* @__PURE__ */ o.jsxs(es, { className: t, children: [
    /* @__PURE__ */ o.jsx(rs, { children: e.map((d) => /* @__PURE__ */ o.jsx(
      ts,
      {
        active: p === d.id,
        onClick: (h) => f(h, d.id),
        type: "button",
        form: "",
        tabIndex: 0,
        "data-tab-id": d.id,
        children: d.label
      },
      d.id
    )) }),
    /* @__PURE__ */ o.jsx(os, { children: (l = e.find((d) => d.id === p)) == null ? void 0 : l.content })
  ] });
}, jc = ns, ss = {
  required: (e = "This field is required") => ({
    validate: (r) => typeof r == "string" ? r.trim().length > 0 : typeof r == "number" ? !isNaN(r) : Array.isArray(r) ? r.length > 0 : r != null && r !== void 0,
    message: e
  }),
  email: (e = "Please enter a valid email address") => ({
    validate: (r) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r),
    message: e
  }),
  minLength: (e, r) => ({
    validate: (t) => t.length >= e,
    message: r || `Must be at least ${e} characters`
  }),
  maxLength: (e, r) => ({
    validate: (t) => t.length <= e,
    message: r || `Must be no more than ${e} characters`
  }),
  min: (e, r) => ({
    validate: (t) => t >= e,
    message: r || `Must be at least ${e}`
  }),
  max: (e, r) => ({
    validate: (t) => t <= e,
    message: r || `Must be no more than ${e}`
  }),
  pattern: (e, r) => ({
    validate: (t) => e.test(t),
    message: r
  })
}, is = (e = {}) => {
  const {
    initialValue: r = "",
    required: t = !1,
    type: n = "text",
    validationRules: s = [],
    validateOnChange: a = !1,
    validateOnBlur: i = !0,
    transform: p
  } = e, f = Y(() => {
    const L = [...s];
    return t && !s.some((N) => N.message.toLowerCase().includes("required")) && L.unshift(ss.required()), L;
  }, [t, s]), [l, d] = V(r), [h, x] = V(null), [y, m] = V(!1), [b, C] = V(!1), w = Y(() => l !== r, [l, r]), S = Y(() => h === null && !b, [h, b]), _ = Y(() => h === null && !b, [h, b]), z = F(async () => {
    C(!0);
    try {
      for (const L of f)
        if (!L.validate(l))
          return x(L.message), C(!1), !1;
      return x(null), C(!1), !0;
    } catch {
      return x("Validation error occurred"), C(!1), !1;
    }
  }, [l, f]), $ = F(() => {
    d(r), x(null), m(!1), C(!1);
  }, [r]), R = F((L) => {
    let N;
    n === "number" ? N = parseFloat(L.target.value) || 0 : N = L.target.value, p && (N = p(N)), d(N), a && y && setTimeout(() => z(), 0);
  }, [n, p, a, y, z]), E = F((L) => {
    m(!0), i && z();
  }, [i, z]);
  return {
    // State
    value: l,
    error: h,
    touched: y,
    dirty: w,
    valid: S,
    isValid: _,
    validating: b,
    // Actions
    setValue: d,
    setError: x,
    setTouched: m,
    validate: z,
    reset: $,
    handleChange: R,
    handleBlur: E
  };
}, as = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-oh07s1-0"
})(["display:flex;flex-direction:column;gap:", ";width:100%;margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), cs = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-oh07s1-1"
})(["font-size:", ";font-weight:", ";color:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || "500";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $required: e
}) => e && g(["&::after{content:' *';color:", ";}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.error) || "#dc2626";
})), gr = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background-color:", ";color:", ";font-size:", ";padding:", ";transition:", ";&:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px ", ";}&:disabled{background-color:", ";color:", ";cursor:not-allowed;}&::placeholder{color:", ";}"], ({
  theme: e,
  $hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.lg) || "18px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.md) || "16px";
  }
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, p;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((p = e.spacing) == null ? void 0 : p.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return (r = e.colors) != null && r.primary ? `${e.colors.primary}20` : "rgba(220, 38, 38, 0.2)";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.chartGrid) || "#374151";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), ls = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-oh07s1-2"
})(["", ""], gr), ds = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "StyledTextarea",
  componentId: "sc-oh07s1-3"
})(["", " resize:vertical;min-height:80px;"], gr), us = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-oh07s1-4"
})(["", " cursor:pointer;"], gr), ps = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-oh07s1-5"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#dc2626";
}), fs = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-oh07s1-6"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), gs = (e) => {
  const {
    name: r,
    label: t,
    placeholder: n,
    disabled: s = !1,
    className: a,
    size: i = "md",
    helpText: p,
    inputType: f = "input",
    options: l = [],
    rows: d = 4,
    onChange: h,
    onBlur: x,
    ...y
  } = e, m = is({
    ...y,
    validateOnBlur: !0
  });
  Se.useEffect(() => {
    h && h(m.value);
  }, [m.value, h]);
  const b = (S) => {
    m.handleBlur(S), x && x();
  }, C = {
    id: r,
    name: r,
    value: m.value,
    onChange: m.handleChange,
    onBlur: b,
    disabled: s,
    placeholder: n,
    $hasError: !!m.error,
    $disabled: s,
    $size: i
  }, w = () => {
    switch (f) {
      case "textarea":
        return /* @__PURE__ */ o.jsx(ds, { ...C, rows: d });
      case "select":
        return /* @__PURE__ */ o.jsxs(us, { ...C, children: [
          n && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: n }),
          l.map((S) => /* @__PURE__ */ o.jsx("option", { value: S.value, children: S.label }, S.value))
        ] });
      default:
        return /* @__PURE__ */ o.jsx(ls, { ...C, type: y.type || "text" });
    }
  };
  return /* @__PURE__ */ o.jsxs(as, { className: a, children: [
    t && /* @__PURE__ */ o.jsx(cs, { htmlFor: r, $required: !!y.required, children: t }),
    w(),
    m.error && m.touched && /* @__PURE__ */ o.jsx(ps, { role: "alert", children: m.error }),
    p && !m.error && /* @__PURE__ */ o.jsx(fs, { children: p })
  ] });
}, Tc = gs, Ec = {
  string: (e) => (r, t) => {
    const n = String(r[e] || ""), s = String(t[e] || "");
    return n.localeCompare(s);
  },
  number: (e) => (r, t) => {
    const n = Number(r[e]) || 0, s = Number(t[e]) || 0;
    return n - s;
  },
  date: (e) => (r, t) => {
    const n = new Date(r[e]).getTime(), s = new Date(t[e]).getTime();
    return n - s;
  },
  boolean: (e) => (r, t) => {
    const n = !!r[e], s = !!t[e];
    return Number(n) - Number(s);
  }
}, ms = ({
  data: e,
  columns: r,
  defaultSort: t
}) => {
  const [n, s] = V(t ? {
    field: t.field,
    direction: t.direction
  } : null), a = F((d) => {
    const h = r.find((x) => x.field === d);
    h != null && h.sortable && s((x) => {
      var y;
      if ((x == null ? void 0 : x.field) === d)
        return {
          field: d,
          direction: x.direction === "asc" ? "desc" : "asc"
        };
      {
        const m = typeof ((y = e[0]) == null ? void 0 : y[d]) == "number" ? "desc" : "asc";
        return {
          field: d,
          direction: m
        };
      }
    });
  }, [r, e]), i = Y(() => {
    if (!n)
      return e;
    const d = r.find((x) => x.field === n.field);
    return d ? [...e].sort((x, y) => {
      let m = 0;
      if (d.sortFn)
        m = d.sortFn(x, y);
      else {
        const b = x[n.field], C = y[n.field];
        typeof b == "string" && typeof C == "string" ? m = b.localeCompare(C) : typeof b == "number" && typeof C == "number" ? m = b - C : m = String(b).localeCompare(String(C));
      }
      return n.direction === "asc" ? m : -m;
    }) : e;
  }, [e, n, r]), p = F((d) => !n || n.field !== d ? null : n.direction === "asc" ? "↑" : "↓", [n]), f = F((d) => (n == null ? void 0 : n.field) === d, [n]), l = F((d) => (n == null ? void 0 : n.field) === d ? n.direction : null, [n]);
  return {
    sortedData: i,
    sortConfig: n,
    handleSort: a,
    getSortIcon: p,
    isSorted: f,
    getSortDirection: l
  };
}, rt = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-13j9udn-0"
})(["overflow-x:auto;border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), hs = /* @__PURE__ */ c.table.withConfig({
  displayName: "Table",
  componentId: "sc-13j9udn-1"
})(["width:100%;border-collapse:collapse;font-size:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.md) || "16px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.sm) || "14px";
  }
}), xs = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHead",
  componentId: "sc-13j9udn-2"
})(["background-color:", ";border-bottom:2px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), bs = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13j9udn-3"
})([""]), tt = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-13j9udn-4"
})(["", " ", " ", " border-bottom:1px solid ", ";"], ({
  $striped: e,
  theme: r
}) => {
  var t;
  return e && g(["&:nth-child(even){background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.background) || "#0f0f0f");
}, ({
  $hoverable: e,
  theme: r
}) => {
  var t;
  return e && g(["&:hover{background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.surface) || "#1f2937");
}, ({
  $clickable: e
}) => e && g(["cursor:pointer;"]), ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), ys = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13j9udn-5"
})(["text-align:left;font-weight:", ";color:", ";cursor:", ";user-select:none;transition:", ";padding:", ";&:hover{", "}&:focus{outline:2px solid ", ";outline-offset:-2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e,
  $active: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textPrimary) || "#ffffff";
}, ({
  $sortable: e
}) => e ? "pointer" : "default", ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, p;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((p = e.spacing) == null ? void 0 : p.md) || "12px"}`;
  }
}, ({
  $sortable: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), vs = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-13j9udn-6"
})(["padding:", ";color:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, p;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((p = e.spacing) == null ? void 0 : p.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), ws = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13j9udn-7"
})(["display:inline-block;margin-left:", ";font-size:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  $direction: e
}) => e === "asc" ? "↑" : "↓"), Ss = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13j9udn-8"
})(["padding:", ";text-align:center;color:", ";font-style:italic;"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
    case "lg":
      return ((n = e.spacing) == null ? void 0 : n.xl) || "24px";
    default:
      return ((s = e.spacing) == null ? void 0 : s.lg) || "16px";
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Cs = ({
  data: e,
  columns: r,
  className: t,
  emptyMessage: n = "No data available",
  defaultSort: s,
  renderCell: a,
  onRowClick: i,
  size: p = "md",
  striped: f = !0,
  hoverable: l = !0
}) => {
  const {
    sortedData: d,
    handleSort: h,
    getSortIcon: x,
    isSorted: y
  } = ms({
    data: e,
    columns: r,
    defaultSort: s
  });
  return e.length === 0 ? /* @__PURE__ */ o.jsx(rt, { className: t, children: /* @__PURE__ */ o.jsx(Ss, { $size: p, children: n }) }) : /* @__PURE__ */ o.jsx(rt, { className: t, children: /* @__PURE__ */ o.jsxs(hs, { $size: p, $striped: f, $hoverable: l, children: [
    /* @__PURE__ */ o.jsx(xs, { children: /* @__PURE__ */ o.jsx(tt, { $striped: !1, $hoverable: !1, $clickable: !1, children: r.map((m) => /* @__PURE__ */ o.jsxs(ys, { $sortable: m.sortable || !1, $active: y(m.field), $size: p, onClick: () => m.sortable && h(m.field), tabIndex: m.sortable ? 0 : -1, onKeyDown: (b) => {
      m.sortable && (b.key === "Enter" || b.key === " ") && (b.preventDefault(), h(m.field));
    }, role: m.sortable ? "button" : void 0, "aria-sort": y(m.field) ? x(m.field) === "↑" ? "ascending" : "descending" : void 0, children: [
      m.label,
      y(m.field) && /* @__PURE__ */ o.jsx(ws, { $direction: x(m.field) === "↑" ? "asc" : "desc" })
    ] }, String(m.field))) }) }),
    /* @__PURE__ */ o.jsx(bs, { children: d.map((m, b) => /* @__PURE__ */ o.jsx(tt, { $striped: f, $hoverable: l, $clickable: !!i, onClick: () => i == null ? void 0 : i(m, b), tabIndex: i ? 0 : -1, onKeyDown: (C) => {
      i && (C.key === "Enter" || C.key === " ") && (C.preventDefault(), i(m, b));
    }, role: i ? "button" : void 0, children: r.map((C) => {
      const w = m[C.field];
      return /* @__PURE__ */ o.jsx(vs, { $size: p, children: a ? a(w, m, C) : String(w) }, String(C.field));
    }) }, b)) })
  ] }) });
}, Nc = Cs, Is = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), js = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), Ts = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), kc = ({
  children: e,
  label: r,
  helperText: t,
  required: n = !1,
  error: s,
  className: a,
  id: i,
  ...p
}) => {
  const f = i || `field-${Math.random().toString(36).substr(2, 9)}`, l = Se.Children.map(e, (d) => Se.isValidElement(d) ? Se.cloneElement(d, {
    id: f,
    required: n,
    error: s
  }) : d);
  return /* @__PURE__ */ o.jsxs(Is, { className: a, ...p, children: [
    /* @__PURE__ */ o.jsxs(js, { htmlFor: f, hasError: !!s, children: [
      r,
      n && /* @__PURE__ */ o.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    l,
    (t || s) && /* @__PURE__ */ o.jsx(Ts, { hasError: !!s, children: s || t })
  ] });
}, Es = /* @__PURE__ */ Le(["from{opacity:0;}to{opacity:1;}"]), Ns = /* @__PURE__ */ Le(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), ks = /* @__PURE__ */ c.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, Es), Rs = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, Ns, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), Ls = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), _s = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Ms = /* @__PURE__ */ c.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), Ps = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), Ds = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Rc = ({
  isOpen: e,
  title: r = "",
  children: t,
  onClose: n,
  size: s = "medium",
  closeOnOutsideClick: a = !0,
  showCloseButton: i = !0,
  footer: p,
  hasFooter: f = !0,
  primaryActionText: l = "",
  onPrimaryAction: d,
  primaryActionDisabled: h = !1,
  primaryActionLoading: x = !1,
  secondaryActionText: y = "",
  onSecondaryAction: m,
  secondaryActionDisabled: b = !1,
  className: C = "",
  zIndex: w = 1e3,
  centered: S = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: _ = !0
}) => {
  const z = yt(null);
  le(() => {
    const L = (N) => {
      N.key === "Escape" && e && a && n();
    };
    return document.addEventListener("keydown", L), () => {
      document.removeEventListener("keydown", L);
    };
  }, [e, n, a]);
  const $ = (L) => {
    z.current && !z.current.contains(L.target) && a && n();
  };
  le(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const R = /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    y && /* @__PURE__ */ o.jsx(de, { variant: "outline", onClick: m, disabled: b, children: y }),
    l && /* @__PURE__ */ o.jsx(de, { onClick: d, disabled: h, loading: x, children: l })
  ] });
  return e ? io(/* @__PURE__ */ o.jsx(ks, { onClick: $, zIndex: w, children: /* @__PURE__ */ o.jsxs(Rs, { ref: z, size: s, className: C, centered: S, scrollable: _, onClick: (L) => L.stopPropagation(), children: [
    (r || i) && /* @__PURE__ */ o.jsxs(Ls, { children: [
      r && /* @__PURE__ */ o.jsx(_s, { children: r }),
      i && /* @__PURE__ */ o.jsx(Ms, { onClick: n, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ o.jsx(Ps, { scrollable: _, children: t }),
    f && (p || l || y) && /* @__PURE__ */ o.jsx(Ds, { children: p || R })
  ] }) }), document.body) : null;
}, $s = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), Os = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";border-radius:", ";"], r.colors.border, r.borderRadius.sm), ({
  compact: e,
  theme: r
}) => e ? g(["th,td{padding:", " ", ";}"], r.spacing.xs, r.spacing.sm) : g(["th,td{padding:", " ", ";}"], r.spacing.sm, r.spacing.md)), zs = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), As = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), Fs = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => r.colors.background), ({
  isSorted: e,
  theme: r
}) => e && g(["color:", ";"], r.colors.primary)), qs = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), Bs = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), Hs = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:nth-child(even){background-color:", "50;}"], r.colors.background), ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:hover{background-color:", "aa;}"], r.colors.background), ({
  isSelected: e,
  theme: r
}) => e && g(["background-color:", "15;"], r.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), Us = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), Ys = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), Vs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), Ws = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), Gs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Ks = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), Qs = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), Xs = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function Lc({
  columns: e,
  data: r,
  isLoading: t = !1,
  bordered: n = !0,
  striped: s = !0,
  hoverable: a = !0,
  compact: i = !1,
  stickyHeader: p = !1,
  height: f,
  onRowClick: l,
  isRowSelected: d,
  onSort: h,
  sortColumn: x,
  sortDirection: y,
  pagination: m = !1,
  currentPage: b = 1,
  pageSize: C = 10,
  totalRows: w = 0,
  onPageChange: S,
  onPageSizeChange: _,
  className: z,
  emptyMessage: $ = "No data available",
  scrollable: R = !0
}) {
  const E = Y(() => e.filter((k) => !k.hidden), [e]), L = Y(() => Math.ceil(w / C), [w, C]), N = Y(() => {
    if (!m)
      return r;
    const k = (b - 1) * C, U = k + C;
    return w > 0 && r.length <= C ? r : r.slice(k, U);
  }, [r, m, b, C, w]), W = (k) => {
    if (!h)
      return;
    h(k, x === k && y === "asc" ? "desc" : "asc");
  }, G = (k) => {
    k < 1 || k > L || !S || S(k);
  };
  return /* @__PURE__ */ o.jsxs("div", { style: {
    position: "relative"
  }, children: [
    t && /* @__PURE__ */ o.jsx(Qs, { children: /* @__PURE__ */ o.jsx(Xs, {}) }),
    /* @__PURE__ */ o.jsx($s, { height: f, scrollable: R, children: /* @__PURE__ */ o.jsxs(Os, { bordered: n, striped: s, compact: i, className: z, children: [
      /* @__PURE__ */ o.jsx(zs, { stickyHeader: p, children: /* @__PURE__ */ o.jsx(As, { children: E.map((k) => /* @__PURE__ */ o.jsxs(Fs, { sortable: k.sortable, isSorted: x === k.id, align: k.align, width: k.width, onClick: () => k.sortable && W(k.id), children: [
        k.header,
        k.sortable && /* @__PURE__ */ o.jsx(qs, { direction: x === k.id ? y : void 0 })
      ] }, k.id)) }) }),
      /* @__PURE__ */ o.jsx(Bs, { children: N.length > 0 ? N.map((k, U) => /* @__PURE__ */ o.jsx(Hs, { hoverable: a, striped: s, isSelected: d ? d(k, U) : !1, isClickable: !!l, onClick: () => l && l(k, U), children: E.map((oe) => /* @__PURE__ */ o.jsx(Us, { align: oe.align, children: oe.cell(k, U) }, oe.id)) }, U)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: E.length, children: /* @__PURE__ */ o.jsx(Ys, { children: $ }) }) }) })
    ] }) }),
    m && L > 0 && /* @__PURE__ */ o.jsxs(Vs, { children: [
      /* @__PURE__ */ o.jsxs(Ws, { children: [
        "Showing ",
        Math.min((b - 1) * C + 1, w),
        " to",
        " ",
        Math.min(b * C, w),
        " of ",
        w,
        " entries"
      ] }),
      /* @__PURE__ */ o.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        _ && /* @__PURE__ */ o.jsxs(Ks, { children: [
          /* @__PURE__ */ o.jsx("span", { children: "Show" }),
          /* @__PURE__ */ o.jsx("select", { value: C, onChange: (k) => _(Number(k.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((k) => /* @__PURE__ */ o.jsx("option", { value: k, children: k }, k)) }),
          /* @__PURE__ */ o.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ o.jsxs(Gs, { children: [
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => G(1), disabled: b === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => G(b - 1), disabled: b === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => G(b + 1), disabled: b === L, children: "Next" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => G(L), disabled: b === L, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const pe = {
  [P.MORNING_BREAKOUT]: {
    type: P.MORNING_BREAKOUT,
    name: "9:50-10:10 Macro",
    timeRange: {
      start: "09:50:00",
      end: "10:10:00"
    },
    description: "Morning breakout period - high volatility after market open",
    characteristics: ["High Volume", "Breakout Setups", "Gap Fills", "Opening Range"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.MID_MORNING_REVERSION]: {
    type: P.MID_MORNING_REVERSION,
    name: "10:50-11:10 Macro",
    timeRange: {
      start: "10:50:00",
      end: "11:10:00"
    },
    description: "Mid-morning reversion period - mean reversion opportunities",
    characteristics: ["Mean Reversion", "Pullback Setups", "Support/Resistance Tests"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !0
  },
  [P.PRE_LUNCH]: {
    type: P.PRE_LUNCH,
    name: "11:50-12:10 Macro",
    timeRange: {
      start: "11:50:00",
      end: "12:10:00"
    },
    description: "Pre-lunch macro window - specific high-activity period within lunch session",
    characteristics: ["Consolidation", "Range Trading", "Pre-Lunch Activity"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    parentMacro: P.LUNCH_MACRO_EXTENDED
  },
  [P.LUNCH_MACRO_EXTENDED]: {
    type: P.LUNCH_MACRO_EXTENDED,
    name: "Lunch Macro (11:30-13:30)",
    timeRange: {
      start: "11:30:00",
      end: "13:30:00"
    },
    description: "Extended lunch period spanning late morning through early afternoon",
    characteristics: ["Multi-Session", "Lunch Trading", "Lower Volume", "Transition Period"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    isMultiSession: !0,
    spansSessions: [X.NEW_YORK_AM, X.NEW_YORK_PM],
    subPeriods: []
    // Will be populated with PRE_LUNCH macro
  },
  [P.LUNCH_MACRO]: {
    type: P.LUNCH_MACRO,
    name: "Lunch Macro (12:00-13:30)",
    timeRange: {
      start: "12:00:00",
      end: "13:30:00"
    },
    description: "Traditional lunch time trading - typically lower volume",
    characteristics: ["Low Volume", "Range Bound", "Choppy Price Action"],
    volatilityLevel: 2,
    volumeLevel: 1,
    isHighProbability: !1
  },
  [P.POST_LUNCH]: {
    type: P.POST_LUNCH,
    name: "13:50-14:10 Macro",
    timeRange: {
      start: "13:50:00",
      end: "14:10:00"
    },
    description: "Post-lunch macro window",
    characteristics: ["Volume Pickup", "Trend Resumption"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  },
  [P.PRE_CLOSE]: {
    type: P.PRE_CLOSE,
    name: "14:50-15:10 Macro",
    timeRange: {
      start: "14:50:00",
      end: "15:10:00"
    },
    description: "Pre-close macro window",
    characteristics: ["Institutional Activity", "Position Adjustments"],
    volatilityLevel: 3,
    volumeLevel: 4,
    isHighProbability: !1
  },
  [P.POWER_HOUR]: {
    type: P.POWER_HOUR,
    name: "15:15-15:45 Macro (Power Hour)",
    timeRange: {
      start: "15:15:00",
      end: "15:45:00"
    },
    description: "Last hour macro - high activity before close",
    characteristics: ["High Volume", "Institutional Flows", "EOD Positioning"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.MOC]: {
    type: P.MOC,
    name: "MOC (Market on Close)",
    timeRange: {
      start: "15:45:00",
      end: "16:00:00"
    },
    description: "Market on close period",
    characteristics: ["MOC Orders", "Final Positioning", "High Volume"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !1
  },
  [P.LONDON_OPEN]: {
    type: P.LONDON_OPEN,
    name: "London Open",
    timeRange: {
      start: "08:00:00",
      end: "09:00:00"
    },
    description: "London market opening hour",
    characteristics: ["European Activity", "Currency Moves", "News Reactions"],
    volatilityLevel: 4,
    volumeLevel: 4,
    isHighProbability: !0
  },
  [P.LONDON_NY_OVERLAP]: {
    type: P.LONDON_NY_OVERLAP,
    name: "London/NY Overlap",
    timeRange: {
      start: "14:00:00",
      end: "16:00:00"
    },
    description: "London and New York session overlap",
    characteristics: ["Highest Volume", "Major Moves", "Cross-Market Activity"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.CUSTOM]: {
    type: P.CUSTOM,
    name: "Custom Period",
    timeRange: {
      start: "00:00:00",
      end: "23:59:59"
    },
    description: "User-defined custom time period",
    characteristics: ["Custom"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  }
}, Js = () => {
  const e = Object.values(Zs).map((s) => ({
    id: s.type,
    ...s
  })), t = [{
    ...pe[P.LUNCH_MACRO_EXTENDED],
    id: "lunch-macro-extended",
    subPeriods: [{
      ...pe[P.PRE_LUNCH],
      id: "pre-lunch-sub"
    }]
  }], n = {};
  return e.forEach((s) => {
    s.macroPeriods.forEach((a) => {
      n[a.type] = {
        ...a,
        parentSession: s.type
      };
    });
  }), t.forEach((s) => {
    n[s.type] = {
      ...s,
      spansSessions: s.spansSessions
    };
  }), {
    sessions: e,
    sessionsByType: e.reduce((s, a) => (s[a.type] = a, s), {}),
    macrosByType: n,
    multiSessionMacros: t
  };
}, Zs = {
  [X.NEW_YORK_AM]: {
    type: X.NEW_YORK_AM,
    name: "New York AM Session",
    timeRange: {
      start: "09:30:00",
      end: "12:00:00"
    },
    description: "New York morning session - high activity and volatility",
    timezone: "America/New_York",
    characteristics: ["High Volume", "Trend Development", "Breakout Opportunities"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[P.MORNING_BREAKOUT],
      id: "morning-breakout"
    }, {
      ...pe[P.MID_MORNING_REVERSION],
      id: "mid-morning-reversion"
    }, {
      ...pe[P.PRE_LUNCH],
      id: "pre-lunch"
    }]
  },
  [X.NEW_YORK_PM]: {
    type: X.NEW_YORK_PM,
    name: "New York PM Session",
    timeRange: {
      start: "12:00:00",
      end: "16:00:00"
    },
    description: "New York afternoon session - institutional activity increases toward close",
    timezone: "America/New_York",
    characteristics: ["Institutional Flows", "EOD Positioning", "Power Hour Activity"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[P.LUNCH_MACRO],
      id: "lunch-macro"
    }, {
      ...pe[P.POST_LUNCH],
      id: "post-lunch"
    }, {
      ...pe[P.PRE_CLOSE],
      id: "pre-close"
    }, {
      ...pe[P.POWER_HOUR],
      id: "power-hour"
    }, {
      ...pe[P.MOC],
      id: "moc"
    }]
  },
  [X.LONDON]: {
    type: X.LONDON,
    name: "London Session",
    timeRange: {
      start: "08:00:00",
      end: "16:00:00"
    },
    description: "London trading session - European market activity",
    timezone: "Europe/London",
    characteristics: ["European Activity", "Currency Focus", "News-Driven"],
    color: "#1f2937",
    // Dark Gray
    macroPeriods: [{
      ...pe[P.LONDON_OPEN],
      id: "london-open"
    }, {
      ...pe[P.LONDON_NY_OVERLAP],
      id: "london-ny-overlap"
    }]
  },
  [X.ASIA]: {
    type: X.ASIA,
    name: "Asia Session",
    timeRange: {
      start: "18:00:00",
      end: "03:00:00"
    },
    description: "Asian trading session - typically lower volatility",
    timezone: "Asia/Tokyo",
    characteristics: ["Lower Volume", "Range Trading", "News Reactions"],
    color: "#4b5563",
    // Gray
    macroPeriods: []
  },
  [X.PRE_MARKET]: {
    type: X.PRE_MARKET,
    name: "Pre-Market",
    timeRange: {
      start: "04:00:00",
      end: "09:30:00"
    },
    description: "Pre-market trading hours",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "News Reactions", "Gap Setups"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [X.AFTER_HOURS]: {
    type: X.AFTER_HOURS,
    name: "After Hours",
    timeRange: {
      start: "16:00:00",
      end: "20:00:00"
    },
    description: "After-hours trading",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "Earnings Reactions", "News-Driven"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [X.OVERNIGHT]: {
    type: X.OVERNIGHT,
    name: "Overnight",
    timeRange: {
      start: "20:00:00",
      end: "04:00:00"
    },
    description: "Overnight session",
    timezone: "America/New_York",
    characteristics: ["Very Low Volume", "Futures Activity"],
    color: "#374151",
    // Dark Gray
    macroPeriods: []
  }
};
class ie {
  /**
   * Initialize and get the session hierarchy
   */
  static getSessionHierarchy() {
    return this.hierarchy || (this.hierarchy = this.buildHierarchy()), this.hierarchy;
  }
  /**
   * Build the complete session hierarchy with overlapping macro support
   */
  static buildHierarchy() {
    return Js();
  }
  /**
   * Parse time string to minutes since midnight
   */
  static timeToMinutes(r) {
    const [t, n, s = 0] = r.split(":").map(Number);
    return t * 60 + n + s / 60;
  }
  /**
   * Convert minutes since midnight to time string
   */
  static minutesToTime(r) {
    const t = Math.floor(r / 60), n = Math.floor(r % 60), s = Math.floor(r % 1 * 60);
    return `${t.toString().padStart(2, "0")}:${n.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
  }
  /**
   * Check if a time falls within a time range
   */
  static isTimeInRange(r, t) {
    const n = this.timeToMinutes(r), s = this.timeToMinutes(t.start), a = this.timeToMinutes(t.end);
    return a < s ? n >= s || n <= a : n >= s && n <= a;
  }
  /**
   * Validate a time and suggest appropriate session/macro with overlapping support
   */
  static validateTime(r) {
    var a;
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(r))
      return {
        isValid: !1,
        error: "Invalid time format. Use HH:MM or HH:MM:SS format."
      };
    const n = this.getSessionHierarchy(), s = [];
    for (const [i, p] of Object.entries(n.macrosByType))
      this.isTimeInRange(r, p.timeRange) && s.push({
        type: i,
        macro: p,
        isSubPeriod: !!p.parentMacro
      });
    if (s.length > 0) {
      const p = s.sort((l, d) => {
        if (l.isSubPeriod && !d.isSubPeriod)
          return -1;
        if (!l.isSubPeriod && d.isSubPeriod)
          return 1;
        const h = this.timeToMinutes(l.macro.timeRange.end) - this.timeToMinutes(l.macro.timeRange.start), x = this.timeToMinutes(d.macro.timeRange.end) - this.timeToMinutes(d.macro.timeRange.start);
        return h - x;
      })[0], f = s.length > 1;
      return {
        isValid: !0,
        suggestedMacro: p.type,
        suggestedSession: p.macro.parentSession || ((a = p.macro.spansSessions) == null ? void 0 : a[0]),
        warning: f ? `Time falls within ${s.length} overlapping macro periods. Suggesting most specific: ${p.macro.name}` : void 0
      };
    }
    for (const i of n.sessions)
      if (this.isTimeInRange(r, i.timeRange))
        return {
          isValid: !0,
          suggestedSession: i.type,
          warning: "Time falls within session but not in a specific macro period."
        };
    return {
      isValid: !0,
      warning: "Time does not fall within any defined session or macro period."
    };
  }
  /**
   * Get session by type
   */
  static getSession(r) {
    return this.getSessionHierarchy().sessionsByType[r] || null;
  }
  /**
   * Get macro period by type
   */
  static getMacroPeriod(r) {
    return this.getSessionHierarchy().macrosByType[r] || null;
  }
  /**
   * Get all macro periods for a session
   */
  static getMacroPeriodsForSession(r) {
    const t = this.getSession(r);
    return (t == null ? void 0 : t.macroPeriods) || [];
  }
  /**
   * Create a session selection
   */
  static createSessionSelection(r, t, n) {
    if (t) {
      const s = this.getMacroPeriod(t);
      return {
        session: s == null ? void 0 : s.parentSession,
        macroPeriod: t,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Macro",
        selectionType: "macro"
      };
    }
    if (r) {
      const s = this.getSession(r);
      return {
        session: r,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Session",
        selectionType: "session"
      };
    }
    return n ? {
      customTimeRange: n,
      displayLabel: `${n.start} - ${n.end}`,
      selectionType: "custom"
    } : {
      displayLabel: "No Selection",
      selectionType: "custom"
    };
  }
  /**
   * Filter sessions and macros based on criteria
   */
  static filterSessions(r = {}) {
    var a, i;
    const t = this.getSessionHierarchy();
    let n = [...t.sessions], s = Object.values(t.macrosByType);
    return r.activeOnly && (n = n.filter((p) => p.isActive)), (a = r.sessionTypes) != null && a.length && (n = n.filter((p) => r.sessionTypes.includes(p.type))), (i = r.macroTypes) != null && i.length && (s = s.filter((p) => r.macroTypes.includes(p.type))), r.highProbabilityOnly && (s = s.filter((p) => p.isHighProbability)), r.minVolatility !== void 0 && (s = s.filter((p) => p.volatilityLevel >= r.minVolatility)), r.maxVolatility !== void 0 && (s = s.filter((p) => p.volatilityLevel <= r.maxVolatility)), {
      sessions: n,
      macros: s
    };
  }
  /**
   * Get current active session based on current time
   */
  static getCurrentSession() {
    const r = /* @__PURE__ */ new Date(), t = `${r.getHours().toString().padStart(2, "0")}:${r.getMinutes().toString().padStart(2, "0")}:00`, n = this.validateTime(t);
    return n.suggestedMacro ? this.createSessionSelection(n.suggestedSession, n.suggestedMacro) : n.suggestedSession ? this.createSessionSelection(n.suggestedSession) : null;
  }
  /**
   * Check if two time ranges overlap
   */
  static timeRangesOverlap(r, t) {
    const n = this.timeToMinutes(r.start), s = this.timeToMinutes(r.end), a = this.timeToMinutes(t.start), i = this.timeToMinutes(t.end);
    return Math.max(n, a) < Math.min(s, i);
  }
  /**
   * Get display options for UI dropdowns
   */
  static getDisplayOptions() {
    const r = this.getSessionHierarchy(), t = r.sessions.map((s) => ({
      value: s.type,
      label: s.name,
      group: "Sessions"
    })), n = Object.values(r.macrosByType).map((s) => {
      var a;
      return {
        value: s.type,
        label: s.name,
        group: ((a = r.sessionsByType[s.parentSession]) == null ? void 0 : a.name) || "Other",
        parentSession: s.parentSession
      };
    });
    return {
      sessionOptions: t,
      macroOptions: n
    };
  }
  /**
   * Get all overlapping macro periods for a given time
   */
  static getOverlappingMacros(r) {
    const t = this.getSessionHierarchy(), n = [];
    for (const [s, a] of Object.entries(t.macrosByType))
      this.isTimeInRange(r, a.timeRange) && n.push({
        type: s,
        macro: a,
        isSubPeriod: !!a.parentMacro,
        isMultiSession: !!a.spansSessions
      });
    return n.sort((s, a) => {
      if (s.isSubPeriod && !a.isSubPeriod)
        return -1;
      if (!s.isSubPeriod && a.isSubPeriod)
        return 1;
      const i = this.timeToMinutes(s.macro.timeRange.end) - this.timeToMinutes(s.macro.timeRange.start), p = this.timeToMinutes(a.macro.timeRange.end) - this.timeToMinutes(a.macro.timeRange.start);
      return i - p;
    });
  }
  /**
   * Get multi-session macros
   */
  static getMultiSessionMacros() {
    return this.getSessionHierarchy().multiSessionMacros || [];
  }
  /**
   * Check if a macro period has sub-periods
   */
  static hasSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return !!(t != null && t.subPeriods && t.subPeriods.length > 0);
  }
  /**
   * Get sub-periods for a macro
   */
  static getSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return (t == null ? void 0 : t.subPeriods) || [];
  }
  /**
   * Convert legacy session string to new session selection
   */
  static convertLegacySession(r) {
    const n = {
      "NY Open": {
        session: X.NEW_YORK_AM
      },
      "London Open": {
        session: X.LONDON
      },
      "Lunch Macro": {
        macro: P.LUNCH_MACRO_EXTENDED
      },
      // Updated to use extended lunch macro
      "Lunch Macro (11:30-13:30)": {
        macro: P.LUNCH_MACRO_EXTENDED
      },
      "Lunch Macro (12:00-13:30)": {
        macro: P.LUNCH_MACRO
      },
      MOC: {
        macro: P.MOC
      },
      Overnight: {
        session: X.OVERNIGHT
      },
      "Pre-Market": {
        session: X.PRE_MARKET
      },
      "After Hours": {
        session: X.AFTER_HOURS
      },
      "Power Hour": {
        macro: P.POWER_HOUR
      },
      "10:50-11:10": {
        macro: P.MID_MORNING_REVERSION
      },
      "11:50-12:10": {
        macro: P.PRE_LUNCH
      },
      "15:15-15:45": {
        macro: P.POWER_HOUR
      }
    }[r];
    return n ? this.createSessionSelection(n.session, n.macro) : null;
  }
}
ie.hierarchy = null;
const ei = (e = {}) => {
  const {
    initialSelection: r,
    autoDetectCurrent: t = !1,
    filterOptions: n = {},
    onSelectionChange: s,
    validateTimes: a = !0
  } = e, [i, p] = V(r || {
    displayLabel: "No Selection",
    selectionType: "custom"
  }), f = Y(() => ie.getCurrentSession(), []), l = Y(() => f !== null, [f]), {
    availableSessions: d,
    availableMacros: h
  } = Y(() => {
    const {
      sessions: R,
      macros: E
    } = ie.filterSessions(n), {
      sessionOptions: L,
      macroOptions: N
    } = ie.getDisplayOptions(), W = L.filter((k) => R.some((U) => U.type === k.value)), G = N.filter((k) => E.some((U) => U.type === k.value));
    return {
      availableSessions: W,
      availableMacros: G
    };
  }, [n]), x = Y(() => {
    const R = ie.getSessionHierarchy();
    return d.map((E) => {
      R.sessionsByType[E.value];
      const L = h.filter((N) => N.parentSession === E.value).map((N) => ({
        value: N.value,
        label: N.label
      }));
      return {
        session: E.value,
        sessionLabel: E.label,
        macros: L
      };
    });
  }, [d, h]);
  le(() => {
    t && f && !r && p(f);
  }, [t, f, r]), le(() => {
    s == null || s(i);
  }, [i, s]);
  const y = F((R) => {
    const E = ie.createSessionSelection(R);
    p(E);
  }, []), m = F((R) => {
    const E = ie.createSessionSelection(void 0, R);
    p(E);
  }, []), b = F((R) => {
    const E = ie.createSessionSelection(void 0, void 0, R);
    p(E);
  }, []), C = F(() => {
    p({
      displayLabel: "No Selection",
      selectionType: "custom"
    });
  }, []), w = F((R) => a ? ie.validateTime(R) : {
    isValid: !0
  }, [a]), S = Y(() => {
    if (i.selectionType === "session" && i.session)
      return ie.getSession(i.session) !== null;
    if (i.selectionType === "macro" && i.macroPeriod)
      return ie.getMacroPeriod(i.macroPeriod) !== null;
    if (i.selectionType === "custom" && i.customTimeRange) {
      const R = w(i.customTimeRange.start), E = w(i.customTimeRange.end);
      return R.isValid && E.isValid;
    }
    return i.selectionType === "custom" && !i.customTimeRange;
  }, [i, w]), _ = F((R) => ie.getSession(R), []), z = F((R) => ie.getMacroPeriod(R), []), $ = F((R) => ie.convertLegacySession(R), []);
  return {
    // State
    selection: i,
    // Selection methods
    selectSession: y,
    selectMacro: m,
    selectCustomRange: b,
    clearSelection: C,
    // Validation
    validateTime: w,
    isValidSelection: S,
    // Options
    availableSessions: d,
    availableMacros: h,
    hierarchicalOptions: x,
    // Current session
    currentSession: f,
    isCurrentSessionActive: l,
    // Utilities
    getSessionDetails: _,
    getMacroDetails: z,
    convertLegacySession: $
  };
}, ri = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1reqqnl-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), ti = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectorContainer",
  componentId: "sc-1reqqnl-1"
})(["position:relative;border:1px solid ", ";border-radius:", ";background:", ";transition:all 0.2s ease;opacity:", ";pointer-events:", ";&:hover{border-color:", "40;}&:focus-within{border-color:", ";box-shadow:0 0 0 3px ", "20;}"], ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  disabled: e
}) => e ? 0.6 : 1, ({
  disabled: e
}) => e ? "none" : "auto", ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), oi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectedValue",
  componentId: "sc-1reqqnl-2"
})(["padding:", ";color:", ";font-size:", ";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.md) || "1rem";
}), ni = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownIcon",
  componentId: "sc-1reqqnl-3"
})(["transition:transform 0.2s ease;transform:", ";color:", ";"], ({
  isOpen: e
}) => e ? "rotate(180deg)" : "rotate(0deg)", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), si = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownMenu",
  componentId: "sc-1reqqnl-4"
})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:", ";border:1px solid ", ";border-radius:", ";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  isOpen: e
}) => e ? "block" : "none"), ii = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionGroup",
  componentId: "sc-1reqqnl-5"
})(["border-bottom:1px solid ", ";background:", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), ai = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionHeader",
  componentId: "sc-1reqqnl-6"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ", ";&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.surface) || "#1f2937";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), ci = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionIndicator",
  componentId: "sc-1reqqnl-7"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}), li = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionGroup",
  componentId: "sc-1reqqnl-8"
})(["border-bottom:1px solid ", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), di = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionHeader",
  componentId: "sc-1reqqnl-9"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), ui = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroList",
  componentId: "sc-1reqqnl-10"
})(["background:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), pi = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroItem",
  componentId: "sc-1reqqnl-11"
})(["padding:", " ", ";color:", ";cursor:pointer;font-size:", ";transition:all 0.2s ease;border-left:3px solid ", ";&:hover{background:", "20;color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "24px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), fi = /* @__PURE__ */ c.div.withConfig({
  displayName: "CurrentSessionIndicator",
  componentId: "sc-1reqqnl-12"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.success) || "#10b981";
}), gi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-1reqqnl-13"
})(["color:", ";font-size:", ";margin-top:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#ef4444";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), _c = ({
  value: e,
  onChange: r,
  showMacroPeriods: t = !0,
  showCurrentSession: n = !0,
  allowCustomRange: s = !1,
  placeholder: a = "Select session or macro period",
  disabled: i = !1,
  error: p,
  className: f
}) => {
  const [l, d] = V(!1), {
    hierarchicalOptions: h,
    currentSession: x,
    isCurrentSessionActive: y,
    selectSession: m,
    selectMacro: b
  } = ei({
    onSelectionChange: r
  }), C = Y(() => ie.getMultiSessionMacros(), []), w = Y(() => e != null && e.displayLabel ? e.displayLabel : a, [e, a]), S = (E) => {
    m(E), d(!1);
  }, _ = (E) => {
    b(E), d(!1);
  }, z = (E) => (e == null ? void 0 : e.session) === E && (e == null ? void 0 : e.selectionType) === "session", $ = (E) => (e == null ? void 0 : e.macroPeriod) === E && (e == null ? void 0 : e.selectionType) === "macro", R = (E) => (x == null ? void 0 : x.session) === E;
  return /* @__PURE__ */ o.jsxs(ri, { className: f, hasError: !!p, children: [
    /* @__PURE__ */ o.jsxs(ti, { hasError: !!p, disabled: i, onClick: () => !i && d(!l), children: [
      /* @__PURE__ */ o.jsxs(oi, { children: [
        /* @__PURE__ */ o.jsx("span", { children: w }),
        /* @__PURE__ */ o.jsx(ni, { isOpen: l, children: "▼" })
      ] }),
      /* @__PURE__ */ o.jsxs(si, { isOpen: l, children: [
        t && C.length > 0 && /* @__PURE__ */ o.jsx(ii, { children: C.map((E) => /* @__PURE__ */ o.jsxs(ai, { isSelected: $(E.type), onClick: (L) => {
          L.stopPropagation(), _(E.type);
        }, children: [
          /* @__PURE__ */ o.jsx("span", { children: E.name }),
          /* @__PURE__ */ o.jsx(ci, { children: "🌐 MULTI-SESSION" })
        ] }, E.type)) }),
        h.map(({
          session: E,
          sessionLabel: L,
          macros: N
        }) => /* @__PURE__ */ o.jsxs(li, { children: [
          /* @__PURE__ */ o.jsxs(di, { isSelected: z(E), onClick: (W) => {
            W.stopPropagation(), S(E);
          }, children: [
            /* @__PURE__ */ o.jsx("span", { children: L }),
            n && R(E) && /* @__PURE__ */ o.jsx(fi, { children: "🔴 LIVE" })
          ] }),
          t && N.length > 0 && /* @__PURE__ */ o.jsx(ui, { children: N.map(({
            value: W,
            label: G
          }) => /* @__PURE__ */ o.jsxs(pi, { isSelected: $(W), onClick: (k) => {
            k.stopPropagation(), _(W);
          }, children: [
            G,
            ie.hasSubPeriods(W) && /* @__PURE__ */ o.jsx("span", { style: {
              marginLeft: "8px",
              fontSize: "0.75rem",
              opacity: 0.7
            }, children: "📋 Has sub-periods" })
          ] }, W)) })
        ] }, E))
      ] })
    ] }),
    p && /* @__PURE__ */ o.jsx(gi, { children: p })
  ] });
}, j = {
  DATE: "date",
  SYMBOL: "symbol",
  DIRECTION: "direction",
  MODEL_TYPE: "model_type",
  SESSION: "session",
  ENTRY_PRICE: "entry_price",
  EXIT_PRICE: "exit_price",
  R_MULTIPLE: "r_multiple",
  ACHIEVED_PL: "achieved_pl",
  WIN_LOSS: "win_loss",
  PATTERN_QUALITY: "pattern_quality_rating",
  ENTRY_TIME: "entry_time",
  EXIT_TIME: "exit_time"
}, mr = /* @__PURE__ */ c.span.withConfig({
  displayName: "ProfitLossCell",
  componentId: "sc-14bks31-0"
})(["color:", ";font-weight:", ";"], ({
  isProfit: e,
  theme: r
}) => e ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), jt = /* @__PURE__ */ c(Ze).withConfig({
  displayName: "DirectionBadge",
  componentId: "sc-14bks31-1"
})(["background-color:", ";color:white;"], ({
  direction: e,
  theme: r
}) => e === "Long" ? r.colors.success || "#10b981" : r.colors.error || "#ef4444"), Tt = /* @__PURE__ */ c.span.withConfig({
  displayName: "QualityRating",
  componentId: "sc-14bks31-2"
})(["color:", ";font-weight:", ";"], ({
  rating: e,
  theme: r
}) => e >= 4 ? r.colors.success || "#10b981" : e >= 3 ? r.colors.warning || "#f59e0b" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), hr = /* @__PURE__ */ c.span.withConfig({
  displayName: "RMultipleCell",
  componentId: "sc-14bks31-3"
})(["color:", ";font-weight:", ";"], ({
  rMultiple: e,
  theme: r
}) => e > 0 ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), qe = (e) => e == null ? "-" : new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2
}).format(e), xr = (e) => {
  try {
    return new Date(e).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch {
    return e;
  }
}, ot = (e) => e || "-", mi = () => [{
  id: j.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => xr(e.trade[j.DATE])
}, {
  id: j.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "80px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: j.DIRECTION,
  header: "Direction",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(jt, { direction: e.trade[j.DIRECTION], size: "small", children: e.trade[j.DIRECTION] })
}, {
  id: j.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[j.MODEL_TYPE] || "-"
}, {
  id: j.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[j.SESSION] || "-"
}, {
  id: j.ENTRY_PRICE,
  header: "Entry",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => qe(e.trade[j.ENTRY_PRICE])
}, {
  id: j.EXIT_PRICE,
  header: "Exit",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => qe(e.trade[j.EXIT_PRICE])
}, {
  id: j.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(hr, { rMultiple: e.trade[j.R_MULTIPLE] || 0, children: e.trade[j.R_MULTIPLE] ? `${e.trade[j.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: j.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(mr, { isProfit: (e.trade[j.ACHIEVED_PL] || 0) > 0, children: qe(e.trade[j.ACHIEVED_PL]) })
}, {
  id: j.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Ze, { variant: e.trade[j.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[j.WIN_LOSS] || "-" })
}, {
  id: j.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Tt, { rating: e.trade[j.PATTERN_QUALITY] || 0, children: e.trade[j.PATTERN_QUALITY] ? `${e.trade[j.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: j.ENTRY_TIME,
  header: "Entry Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => ot(e.trade[j.ENTRY_TIME])
}, {
  id: j.EXIT_TIME,
  header: "Exit Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => ot(e.trade[j.EXIT_TIME])
}], hi = () => [{
  id: j.DATE,
  header: "Date",
  sortable: !0,
  width: "90px",
  cell: (e) => xr(e.trade[j.DATE])
}, {
  id: j.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "60px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: j.DIRECTION,
  header: "Dir",
  sortable: !0,
  width: "50px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(jt, { direction: e.trade[j.DIRECTION], size: "small", children: e.trade[j.DIRECTION].charAt(0) })
}, {
  id: j.R_MULTIPLE,
  header: "R",
  sortable: !0,
  width: "60px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(hr, { rMultiple: e.trade[j.R_MULTIPLE] || 0, children: e.trade[j.R_MULTIPLE] ? `${e.trade[j.R_MULTIPLE].toFixed(1)}R` : "-" })
}, {
  id: j.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "80px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(mr, { isProfit: (e.trade[j.ACHIEVED_PL] || 0) > 0, children: qe(e.trade[j.ACHIEVED_PL]) })
}, {
  id: j.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "60px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Ze, { variant: e.trade[j.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[j.WIN_LOSS] === "Win" ? "W" : e.trade[j.WIN_LOSS] === "Loss" ? "L" : "-" })
}], xi = () => [{
  id: j.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => xr(e.trade[j.DATE])
}, {
  id: j.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[j.MODEL_TYPE] || "-"
}, {
  id: j.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[j.SESSION] || "-"
}, {
  id: j.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(hr, { rMultiple: e.trade[j.R_MULTIPLE] || 0, children: e.trade[j.R_MULTIPLE] ? `${e.trade[j.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: j.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(mr, { isProfit: (e.trade[j.ACHIEVED_PL] || 0) > 0, children: qe(e.trade[j.ACHIEVED_PL]) })
}, {
  id: j.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Tt, { rating: e.trade[j.PATTERN_QUALITY] || 0, children: e.trade[j.PATTERN_QUALITY] ? `${e.trade[j.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: j.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Ze, { variant: e.trade[j.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[j.WIN_LOSS] || "-" })
}], bi = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-uyrnn-0"
})(["", " ", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:nth-child(even){background-color:", "50;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:hover{background-color:", "aa;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  isSelected: e,
  theme: r
}) => {
  var t;
  return e && g(["background-color:", "15;"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}, ({
  isClickable: e
}) => e && g(["cursor:pointer;"]), ({
  isExpanded: e,
  theme: r
}) => {
  var t;
  return e && g(["border-bottom:2px solid ", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), nt = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-uyrnn-1"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";padding:", " ", ";vertical-align:middle;"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), yi = /* @__PURE__ */ c.tr.withConfig({
  displayName: "ExpandedRow",
  componentId: "sc-uyrnn-2"
})(["display:", ";"], ({
  isVisible: e
}) => e ? "table-row" : "none"), vi = /* @__PURE__ */ c.td.withConfig({
  displayName: "ExpandedCell",
  componentId: "sc-uyrnn-3"
})(["padding:0;border-bottom:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), wi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ExpandedContent",
  componentId: "sc-uyrnn-4"
})(["padding:", ";background-color:", "30;border-left:3px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Si = /* @__PURE__ */ c.button.withConfig({
  displayName: "ExpandButton",
  componentId: "sc-uyrnn-5"
})(["background:none;border:none;cursor:pointer;padding:", ";color:", ";font-size:", ";display:flex;align-items:center;justify-content:center;border-radius:", ";transition:all 0.2s ease;&:hover{background-color:", ";color:", ";}&:focus{outline:2px solid ", ";outline-offset:2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Ci = /* @__PURE__ */ c.span.withConfig({
  displayName: "ExpandIcon",
  componentId: "sc-uyrnn-6"
})(["display:inline-block;transition:transform 0.2s ease;transform:", ";&::after{content:'▶';}"], ({
  isExpanded: e
}) => e ? "rotate(90deg)" : "rotate(0deg)"), Ii = /* @__PURE__ */ c.div.withConfig({
  displayName: "TradeDetails",
  componentId: "sc-uyrnn-7"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), Oe = /* @__PURE__ */ c.div.withConfig({
  displayName: "DetailGroup",
  componentId: "sc-uyrnn-8"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), ze = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailLabel",
  componentId: "sc-uyrnn-9"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), ce = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailValue",
  componentId: "sc-uyrnn-10"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}), ji = ({
  trade: e
}) => /* @__PURE__ */ o.jsxs(Ii, { children: [
  e.fvg_details && /* @__PURE__ */ o.jsxs(Oe, { children: [
    /* @__PURE__ */ o.jsx(ze, { children: "FVG Details" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Type: ",
      e.fvg_details.rd_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry Version: ",
      e.fvg_details.entry_version || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Draw on Liquidity: ",
      e.fvg_details.draw_on_liquidity || "-"
    ] })
  ] }),
  e.setup && /* @__PURE__ */ o.jsxs(Oe, { children: [
    /* @__PURE__ */ o.jsx(ze, { children: "Setup Classification" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Primary: ",
      e.setup.primary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Secondary: ",
      e.setup.secondary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Liquidity: ",
      e.setup.liquidity_taken || "-"
    ] })
  ] }),
  e.analysis && /* @__PURE__ */ o.jsxs(Oe, { children: [
    /* @__PURE__ */ o.jsx(ze, { children: "Analysis" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "DOL Target: ",
      e.analysis.dol_target_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Path Quality: ",
      e.analysis.path_quality || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Clustering: ",
      e.analysis.clustering || "-"
    ] })
  ] }),
  /* @__PURE__ */ o.jsxs(Oe, { children: [
    /* @__PURE__ */ o.jsx(ze, { children: "Timing" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry: ",
      e.trade.entry_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Exit: ",
      e.trade.exit_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "FVG: ",
      e.trade.fvg_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "RD: ",
      e.trade.rd_time || "-"
    ] })
  ] }),
  e.trade.notes && /* @__PURE__ */ o.jsxs(Oe, { style: {
    gridColumn: "1 / -1"
  }, children: [
    /* @__PURE__ */ o.jsx(ze, { children: "Notes" }),
    /* @__PURE__ */ o.jsx(ce, { children: e.trade.notes })
  ] })
] }), Ti = ({
  trade: e,
  index: r,
  columns: t,
  isSelected: n = !1,
  hoverable: s = !0,
  striped: a = !0,
  expandable: i = !1,
  isExpanded: p = !1,
  onRowClick: f,
  onToggleExpand: l,
  expandedContent: d
}) => {
  const [h, x] = V(!1), y = p !== void 0 ? p : h, m = (w) => {
    w.target.closest("button") || f == null || f(e, r);
  }, b = (w) => {
    w.stopPropagation(), l ? l(e, r) : x(!h);
  }, C = t.filter((w) => !w.hidden);
  return /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    /* @__PURE__ */ o.jsxs(bi, { hoverable: s, striped: a, isSelected: n, isClickable: !!f, isExpanded: y, onClick: m, children: [
      i && /* @__PURE__ */ o.jsx(nt, { align: "center", style: {
        width: "40px",
        padding: "8px"
      }, children: /* @__PURE__ */ o.jsx(Si, { onClick: b, children: /* @__PURE__ */ o.jsx(Ci, { isExpanded: y }) }) }),
      C.map((w) => /* @__PURE__ */ o.jsx(nt, { align: w.align, children: w.cell(e, r) }, w.id))
    ] }),
    i && /* @__PURE__ */ o.jsx(yi, { isVisible: y, children: /* @__PURE__ */ o.jsx(vi, { colSpan: C.length + 1, children: /* @__PURE__ */ o.jsx(wi, { children: d || /* @__PURE__ */ o.jsx(ji, { trade: e }) }) }) })
  ] });
}, ge = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  DATE_FROM: "dateFrom",
  DATE_TO: "dateTo",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  MIN_R_MULTIPLE: "min_r_multiple",
  MAX_R_MULTIPLE: "max_r_multiple",
  MIN_PATTERN_QUALITY: "min_pattern_quality",
  MAX_PATTERN_QUALITY: "max_pattern_quality"
}, Ei = /* @__PURE__ */ c.div.withConfig({
  displayName: "FiltersContainer",
  componentId: "sc-32k3gq-0"
})(["display:flex;flex-direction:column;gap:", ";padding:", ";background-color:", ";border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), st = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterRow",
  componentId: "sc-32k3gq-1"
})(["display:flex;gap:", ";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), be = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterGroup",
  componentId: "sc-32k3gq-2"
})(["display:flex;flex-direction:column;gap:", ";min-width:120px;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), ye = /* @__PURE__ */ c.label.withConfig({
  displayName: "FilterLabel",
  componentId: "sc-32k3gq-3"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Ni = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterActions",
  componentId: "sc-32k3gq-4"
})(["display:flex;gap:", ";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "AdvancedFilters",
  componentId: "sc-32k3gq-5"
})(["display:", ";flex-direction:column;gap:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  isVisible: e
}) => e ? "flex" : "none", ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), it = /* @__PURE__ */ c.div.withConfig({
  displayName: "RangeInputGroup",
  componentId: "sc-32k3gq-6"
})(["display:flex;gap:", ";align-items:center;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), at = /* @__PURE__ */ c.span.withConfig({
  displayName: "RangeLabel",
  componentId: "sc-32k3gq-7"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Ri = ({
  filters: e,
  onFiltersChange: r,
  onReset: t,
  isLoading: n = !1,
  showAdvanced: s = !1,
  onToggleAdvanced: a
}) => {
  const i = (l, d) => {
    r({
      ...e,
      [l]: d
    });
  }, p = () => {
    r({}), t == null || t();
  }, f = Object.values(e).some((l) => l !== void 0 && l !== "" && l !== null);
  return /* @__PURE__ */ o.jsxs(Ei, { children: [
    /* @__PURE__ */ o.jsxs(st, { children: [
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Date From" }),
        /* @__PURE__ */ o.jsx(je, { type: "date", value: e.dateFrom || "", onChange: (l) => i(ge.DATE_FROM, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Date To" }),
        /* @__PURE__ */ o.jsx(je, { type: "date", value: e.dateTo || "", onChange: (l) => i(ge.DATE_TO, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Model Type" }),
        /* @__PURE__ */ o.jsx($e, { options: [{
          value: "",
          label: "All Models"
        }, {
          value: "RD-Cont",
          label: "RD-Cont"
        }, {
          value: "FVG-RD",
          label: "FVG-RD"
        }, {
          value: "True-RD",
          label: "True-RD"
        }, {
          value: "IMM-RD",
          label: "IMM-RD"
        }, {
          value: "Dispersed-RD",
          label: "Dispersed-RD"
        }, {
          value: "Wide-Gap-RD",
          label: "Wide-Gap-RD"
        }], value: e.model_type || "", onChange: (l) => i(ge.MODEL_TYPE, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Session" }),
        /* @__PURE__ */ o.jsx($e, { options: [{
          value: "",
          label: "All Sessions"
        }, {
          value: "Pre-Market",
          label: "Pre-Market"
        }, {
          value: "NY Open",
          label: "NY Open"
        }, {
          value: "10:50-11:10",
          label: "10:50-11:10"
        }, {
          value: "11:50-12:10",
          label: "11:50-12:10"
        }, {
          value: "Lunch Macro",
          label: "Lunch Macro"
        }, {
          value: "13:50-14:10",
          label: "13:50-14:10"
        }, {
          value: "14:50-15:10",
          label: "14:50-15:10"
        }, {
          value: "15:15-15:45",
          label: "15:15-15:45"
        }, {
          value: "MOC",
          label: "MOC"
        }, {
          value: "Post MOC",
          label: "Post MOC"
        }], value: e.session || "", onChange: (l) => i(ge.SESSION, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Direction" }),
        /* @__PURE__ */ o.jsx($e, { options: [{
          value: "",
          label: "All Directions"
        }, {
          value: "Long",
          label: "Long"
        }, {
          value: "Short",
          label: "Short"
        }], value: e.direction || "", onChange: (l) => i(ge.DIRECTION, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Result" }),
        /* @__PURE__ */ o.jsx($e, { options: [{
          value: "",
          label: "All Results"
        }, {
          value: "Win",
          label: "Win"
        }, {
          value: "Loss",
          label: "Loss"
        }], value: e.win_loss || "", onChange: (l) => i(ge.WIN_LOSS, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(Ni, { children: [
        a && /* @__PURE__ */ o.jsxs(de, { variant: "outline", size: "small", onClick: a, disabled: n, children: [
          s ? "Hide" : "Show",
          " Advanced"
        ] }),
        /* @__PURE__ */ o.jsx(de, { variant: "outline", size: "small", onClick: p, disabled: n || !f, children: "Reset" })
      ] })
    ] }),
    /* @__PURE__ */ o.jsx(ki, { isVisible: s, children: /* @__PURE__ */ o.jsxs(st, { children: [
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Market" }),
        /* @__PURE__ */ o.jsx($e, { options: [{
          value: "",
          label: "All Markets"
        }, {
          value: "MNQ",
          label: "MNQ"
        }, {
          value: "NQ",
          label: "NQ"
        }, {
          value: "ES",
          label: "ES"
        }, {
          value: "MES",
          label: "MES"
        }, {
          value: "YM",
          label: "YM"
        }, {
          value: "MYM",
          label: "MYM"
        }], value: e.market || "", onChange: (l) => i(ge.MARKET, l), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "R Multiple Range" }),
        /* @__PURE__ */ o.jsxs(it, { children: [
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Min", step: "0.1", value: e.min_r_multiple || "", onChange: (l) => i(ge.MIN_R_MULTIPLE, l ? Number(l) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(at, { children: "to" }),
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Max", step: "0.1", value: e.max_r_multiple || "", onChange: (l) => i(ge.MAX_R_MULTIPLE, l ? Number(l) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Pattern Quality Range" }),
        /* @__PURE__ */ o.jsxs(it, { children: [
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: e.min_pattern_quality || "", onChange: (l) => i(ge.MIN_PATTERN_QUALITY, l ? Number(l) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(at, { children: "to" }),
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: e.max_pattern_quality || "", onChange: (l) => i(ge.MAX_PATTERN_QUALITY, l ? Number(l) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] })
    ] }) })
  ] });
}, Li = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-13oxwmo-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), _i = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-13oxwmo-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  bordered: e,
  theme: r
}) => {
  var t, n;
  return e && g(["border:1px solid ", ";border-radius:", ";"], ((t = r.colors) == null ? void 0 : t.border) || "#e5e7eb", ((n = r.borderRadius) == null ? void 0 : n.sm) || "4px");
}, ({
  compact: e,
  theme: r
}) => {
  var t, n, s, a;
  return e ? g(["th,td{padding:", " ", ";}"], ((t = r.spacing) == null ? void 0 : t.xs) || "8px", ((n = r.spacing) == null ? void 0 : n.sm) || "12px") : g(["th,td{padding:", " ", ";}"], ((s = r.spacing) == null ? void 0 : s.sm) || "12px", ((a = r.spacing) == null ? void 0 : a.md) || "16px");
}), Mi = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-13oxwmo-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), Pi = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-13oxwmo-3"
})(["background-color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}), ct = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13oxwmo-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.background) || "#f8f9fa";
}), ({
  isSorted: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), Di = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13oxwmo-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), $i = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13oxwmo-6"
})([""]), Oi = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13oxwmo-7"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xl) || "32px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), zi = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-13oxwmo-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return `${((r = e.colors) == null ? void 0 : r.background) || "#ffffff"}80`;
}), Ai = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-13oxwmo-9"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Fi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-13oxwmo-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}), qi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-13oxwmo-11"
})(["color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Bi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-13oxwmo-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Mc = ({
  data: e,
  isLoading: r = !1,
  bordered: t = !0,
  striped: n = !0,
  hoverable: s = !0,
  compact: a = !1,
  stickyHeader: i = !1,
  height: p = "",
  onRowClick: f,
  isRowSelected: l,
  onSort: d,
  sortColumn: h = "",
  sortDirection: x = "asc",
  pagination: y = !1,
  currentPage: m = 1,
  pageSize: b = 10,
  totalRows: C = 0,
  onPageChange: w,
  onPageSizeChange: S,
  className: _ = "",
  emptyMessage: z = "No trades available",
  scrollable: $ = !0,
  showFilters: R = !1,
  filters: E = {},
  onFiltersChange: L,
  columnPreset: N = "default",
  customColumns: W,
  expandableRows: G = !1,
  renderExpandedContent: k
}) => {
  const [U, oe] = V(!1), ne = Y(() => {
    if (W)
      return W;
    switch (N) {
      case "compact":
        return hi();
      case "performance":
        return xi();
      default:
        return mi();
    }
  }, [W, N]), fe = Y(() => ne.filter((H) => !H.hidden), [ne]), se = Y(() => Math.ceil(C / b), [C, b]), D = Y(() => {
    if (!y)
      return e;
    const H = (m - 1) * b, he = H + b;
    return C > 0 && e.length <= b ? e : e.slice(H, he);
  }, [e, y, m, b, C]), J = (H) => {
    if (!d)
      return;
    d(H, h === H && x === "asc" ? "desc" : "asc");
  }, we = (H) => {
    H < 1 || H > se || !w || w(H);
  };
  return /* @__PURE__ */ o.jsxs("div", { children: [
    R && L && /* @__PURE__ */ o.jsx(Ri, { filters: E, onFiltersChange: L, isLoading: r, showAdvanced: U, onToggleAdvanced: () => oe(!U) }),
    /* @__PURE__ */ o.jsxs("div", { style: {
      position: "relative"
    }, children: [
      r && /* @__PURE__ */ o.jsx(zi, { children: /* @__PURE__ */ o.jsx(Ai, {}) }),
      /* @__PURE__ */ o.jsx(Li, { height: p, scrollable: $, children: /* @__PURE__ */ o.jsxs(_i, { bordered: t, striped: n, compact: a, className: _, children: [
        /* @__PURE__ */ o.jsx(Mi, { stickyHeader: i, children: /* @__PURE__ */ o.jsxs(Pi, { children: [
          G && /* @__PURE__ */ o.jsx(ct, { width: "40px", align: "center" }),
          fe.map((H) => /* @__PURE__ */ o.jsxs(ct, { sortable: H.sortable, isSorted: h === H.id, align: H.align, width: H.width, onClick: () => H.sortable && J(H.id), children: [
            H.header,
            H.sortable && /* @__PURE__ */ o.jsx(Di, { direction: h === H.id ? x : void 0 })
          ] }, H.id))
        ] }) }),
        /* @__PURE__ */ o.jsx($i, { children: D.length > 0 ? D.map((H, he) => /* @__PURE__ */ o.jsx(Ti, { trade: H, index: he, columns: fe, isSelected: l ? l(H, he) : !1, hoverable: s, striped: n, expandable: G, onRowClick: f, expandedContent: k == null ? void 0 : k(H) }, H.trade.id || he)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: fe.length + (G ? 1 : 0), children: /* @__PURE__ */ o.jsx(Oi, { children: z }) }) }) })
      ] }) }),
      y && se > 0 && /* @__PURE__ */ o.jsxs(Fi, { children: [
        /* @__PURE__ */ o.jsxs(qi, { children: [
          "Showing ",
          Math.min((m - 1) * b + 1, C),
          " to",
          " ",
          Math.min(m * b, C),
          " of ",
          C,
          " entries"
        ] }),
        /* @__PURE__ */ o.jsxs(Bi, { children: [
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => we(1), disabled: m === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => we(m - 1), disabled: m === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => we(m + 1), disabled: m === se, children: "Next" }),
          /* @__PURE__ */ o.jsx(de, { size: "small", variant: "outline", onClick: () => we(se), disabled: m === se, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}, Hi = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Pc = ({
  title: e,
  children: r,
  isLoading: t = !1,
  hasError: n = !1,
  errorMessage: s = "An error occurred while loading data",
  showRetry: a = !0,
  onRetry: i,
  isEmpty: p = !1,
  emptyMessage: f = "No data available",
  emptyActionText: l,
  onEmptyAction: d,
  actionButton: h,
  className: x,
  ...y
}) => {
  const m = /* @__PURE__ */ o.jsx(Hi, { children: h });
  let b;
  return t ? b = /* @__PURE__ */ o.jsx(Ao, { variant: "card", text: "Loading data..." }) : n ? b = /* @__PURE__ */ o.jsx(Qr, { title: "Error", description: s, variant: "compact", actionText: a ? "Retry" : void 0, onAction: a ? i : void 0 }) : p ? b = /* @__PURE__ */ o.jsx(Qr, { title: "No Data", description: f, variant: "compact", actionText: l, onAction: d }) : b = r, /* @__PURE__ */ o.jsx(Fn, { title: e, actions: m, className: x, ...y, children: b });
}, Ui = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContainer",
  componentId: "sc-14y246p-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.spacing.lg), Yi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionHeader",
  componentId: "sc-14y246p-1"
})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:", ";padding-bottom:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border), Vi = /* @__PURE__ */ c.h2.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-14y246p-2"
})(["color:", ";font-size:", ";font-weight:600;margin:0;"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), Wi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionActions",
  componentId: "sc-14y246p-3"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Gi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContent",
  componentId: "sc-14y246p-4"
})(["min-height:200px;"]), lt = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingState",
  componentId: "sc-14y246p-5"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), Ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorState",
  componentId: "sc-14y246p-6"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";text-align:center;"], ({
  theme: e
}) => e.colors.danger), Qi = ({
  name: e,
  title: r,
  children: t,
  actions: n,
  isLoading: s = !1,
  error: a = null,
  className: i,
  collapsible: p = !1,
  defaultCollapsed: f = !1
}) => {
  const [l, d] = Se.useState(f), h = () => {
    p && d(!l);
  }, x = r || e.charAt(0).toUpperCase() + e.slice(1), y = () => a ? /* @__PURE__ */ o.jsx(Ki, { children: /* @__PURE__ */ o.jsxs("div", { children: [
    /* @__PURE__ */ o.jsxs("div", { children: [
      "Error loading ",
      e
    ] }),
    /* @__PURE__ */ o.jsx("div", { style: {
      fontSize: "0.9em",
      marginTop: "8px"
    }, children: a })
  ] }) }) : s ? /* @__PURE__ */ o.jsxs(lt, { children: [
    "Loading ",
    e,
    "..."
  ] }) : t || /* @__PURE__ */ o.jsxs(lt, { children: [
    "No ",
    e,
    " data available"
  ] });
  return /* @__PURE__ */ o.jsxs(Ui, { className: i, "data-section": e, children: [
    /* @__PURE__ */ o.jsxs(Yi, { children: [
      /* @__PURE__ */ o.jsxs(Vi, { onClick: h, style: {
        cursor: p ? "pointer" : "default"
      }, children: [
        x,
        p && /* @__PURE__ */ o.jsx("span", { style: {
          marginLeft: "8px",
          fontSize: "0.8em"
        }, children: l ? "▶" : "▼" })
      ] }),
      n && /* @__PURE__ */ o.jsx(Wi, { children: n })
    ] }),
    !l && /* @__PURE__ */ o.jsx(Gi, { children: y() })
  ] });
}, Dc = Qi, Xi = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), Ji = /* @__PURE__ */ c.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), Zi = /* @__PURE__ */ c.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), ea = /* @__PURE__ */ c.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), $c = ({
  header: e,
  sidebar: r,
  children: t,
  sidebarCollapsed: n = !1,
  // toggleSidebar, // Unused prop
  className: s
}) => /* @__PURE__ */ o.jsxs(Xi, { sidebarCollapsed: n, className: s, children: [
  /* @__PURE__ */ o.jsx(Ji, { children: e }),
  /* @__PURE__ */ o.jsx(Zi, { collapsed: n, children: r }),
  /* @__PURE__ */ o.jsx(ea, { children: t })
] }), ra = /* @__PURE__ */ c.div.withConfig({
  displayName: "BuilderContainer",
  componentId: "sc-5duzr2-0"
})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]), ta = /* @__PURE__ */ c.h3.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-5duzr2-1"
})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]), oa = /* @__PURE__ */ c.div.withConfig({
  displayName: "MatrixGrid",
  componentId: "sc-5duzr2-2"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]), Ke = /* @__PURE__ */ c.div.withConfig({
  displayName: "ElementSection",
  componentId: "sc-5duzr2-3"
})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]), Ae = /* @__PURE__ */ c.h4.withConfig({
  displayName: "ElementTitle",
  componentId: "sc-5duzr2-4"
})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]), Qe = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-5duzr2-5"
})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]), na = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewContainer",
  componentId: "sc-5duzr2-6"
})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]), sa = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewText",
  componentId: "sc-5duzr2-7"
})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]), dt = /* @__PURE__ */ c.span.withConfig({
  displayName: "RequiredIndicator",
  componentId: "sc-5duzr2-8"
})(["color:#dc2626;margin-left:4px;"]), ut = /* @__PURE__ */ c.span.withConfig({
  displayName: "OptionalIndicator",
  componentId: "sc-5duzr2-9"
})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]), ia = ({
  onSetupChange: e,
  initialComponents: r
}) => {
  const [t, n] = V({
    constant: (r == null ? void 0 : r.constant) || "",
    action: (r == null ? void 0 : r.action) || "None",
    variable: (r == null ? void 0 : r.variable) || "None",
    entry: (r == null ? void 0 : r.entry) || ""
  });
  le(() => {
    t.constant && t.entry && e(t);
  }, [t, e]);
  const s = (i, p) => {
    n((f) => ({
      ...f,
      [i]: p
    }));
  }, a = () => {
    const {
      constant: i,
      action: p,
      variable: f,
      entry: l
    } = t;
    if (!i || !l)
      return "Select required elements to see setup preview...";
    let d = i;
    return p && p !== "None" && (d += ` → ${p}`), f && f !== "None" && (d += ` → ${f}`), d += ` [${l}]`, d;
  };
  return /* @__PURE__ */ o.jsxs(ra, { children: [
    /* @__PURE__ */ o.jsx(ta, { children: "Setup Construction Matrix" }),
    /* @__PURE__ */ o.jsxs(oa, { children: [
      /* @__PURE__ */ o.jsxs(Ke, { children: [
        /* @__PURE__ */ o.jsxs(Ae, { children: [
          "Constant Element",
          /* @__PURE__ */ o.jsx(dt, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(Qe, { value: t.constant, onChange: (i) => s("constant", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Constant" }),
          Me.constant.parentArrays.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i)),
          Me.constant.fvgTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ke, { children: [
        /* @__PURE__ */ o.jsxs(Ae, { children: [
          "Action Element",
          /* @__PURE__ */ o.jsx(ut, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(Qe, { value: t.action, onChange: (i) => s("action", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Me.action.liquidityEvents.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ke, { children: [
        /* @__PURE__ */ o.jsxs(Ae, { children: [
          "Variable Element",
          /* @__PURE__ */ o.jsx(ut, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(Qe, { value: t.variable, onChange: (i) => s("variable", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Me.variable.rdTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ke, { children: [
        /* @__PURE__ */ o.jsxs(Ae, { children: [
          "Entry Method",
          /* @__PURE__ */ o.jsx(dt, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(Qe, { value: t.entry, onChange: (i) => s("entry", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Entry Method" }),
          Me.entry.methods.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] })
    ] }),
    /* @__PURE__ */ o.jsxs(na, { children: [
      /* @__PURE__ */ o.jsx(Ae, { children: "Setup Preview" }),
      /* @__PURE__ */ o.jsx(sa, { children: a() })
    ] })
  ] });
}, Oc = ia, pt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricsContainer",
  componentId: "sc-opkdti-0"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => e.spacing.md), ft = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricCard",
  componentId: "sc-opkdti-1"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.md), gt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricLabel",
  componentId: "sc-opkdti-2"
})(["color:", ";font-size:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xs), mt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricValue",
  componentId: "sc-opkdti-3"
})(["color:", ";font-size:", ";font-weight:600;"], ({
  theme: e,
  positive: r,
  negative: t
}) => r ? e.colors.success : t ? e.colors.danger : e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), aa = ({
  metrics: e,
  isLoading: r
}) => r ? /* @__PURE__ */ o.jsx(pt, { children: Array.from({
  length: 4
}).map((t, n) => /* @__PURE__ */ o.jsxs(ft, { children: [
  /* @__PURE__ */ o.jsx(gt, { children: "Loading..." }),
  /* @__PURE__ */ o.jsx(mt, { children: "--" })
] }, n)) }) : /* @__PURE__ */ o.jsx(pt, { children: e.map((t, n) => /* @__PURE__ */ o.jsxs(ft, { children: [
  /* @__PURE__ */ o.jsx(gt, { children: t.label }),
  /* @__PURE__ */ o.jsx(mt, { positive: t.positive, negative: t.negative, children: t.value })
] }, n)) }), zc = aa, ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContainer",
  componentId: "sc-tp1ymt-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg), la = /* @__PURE__ */ c.h3.withConfig({
  displayName: "AnalysisTitle",
  componentId: "sc-tp1ymt-1"
})(["color:", ";font-size:", ";font-weight:600;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.spacing.md), da = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContent",
  componentId: "sc-tp1ymt-2"
})(["color:", ";line-height:1.6;"], ({
  theme: e
}) => e.colors.textSecondary), ua = ({
  title: e = "Trade Analysis",
  children: r,
  isLoading: t
}) => /* @__PURE__ */ o.jsxs(ca, { children: [
  /* @__PURE__ */ o.jsx(la, { children: e }),
  /* @__PURE__ */ o.jsx(da, { children: t ? /* @__PURE__ */ o.jsx("div", { children: "Loading analysis..." }) : r || /* @__PURE__ */ o.jsx("div", { children: "No analysis data available" }) })
] }), Ac = ua, T = {
  // F1 colors - Updated to match racing specifications
  f1Red: "#dc2626",
  f1RedDark: "#b91c1c",
  f1RedLight: "#ef4444",
  f1Blue: "#1e5bc6",
  f1BlueDark: "#1a4da8",
  f1BlueLight: "#4a7dd8",
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, B = {
  background: "#0f0f0f",
  surface: "#1a1a1a",
  cardBackground: "#1a1a1a",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: T.green,
  warning: T.yellow,
  error: T.red,
  info: T.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: T.f1Red,
  // Trading specific colors
  profit: T.green,
  loss: T.red,
  neutral: T.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, ee = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: T.green,
  warning: T.yellow,
  error: T.red,
  info: T.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: T.f1Red,
  // Trading specific colors
  profit: T.green,
  loss: T.red,
  neutral: T.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, re = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, me = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  xxxl: "2.5rem",
  // Added missing xxxl size
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, br = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, yr = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, vr = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, wr = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, Sr = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, Cr = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, Ir = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, jr = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, pa = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-e71xhh-0"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;border-bottom:2px solid #4b5563;margin-bottom:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["padding:24px 0;margin-bottom:24px;"]);
    case "form":
      return g(["padding:16px 0;margin-bottom:16px;"]);
    default:
      return g(["padding:20px 0;margin-bottom:20px;"]);
  }
}), fa = /* @__PURE__ */ c.div.withConfig({
  displayName: "TitleSection",
  componentId: "sc-e71xhh-1"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), ga = /* @__PURE__ */ c.h1.withConfig({
  displayName: "MainTitle",
  componentId: "sc-e71xhh-2"
})(["font-weight:700;color:", ";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;", " span{color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["font-size:", ";"], me.xxxl);
    case "analysis":
      return g(["font-size:", ";"], me.xxl);
    case "form":
      return g(["font-size:", ";"], me.xl);
    default:
      return g(["font-size:", ";"], me.xxl);
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), ma = /* @__PURE__ */ c.div.withConfig({
  displayName: "Subtitle",
  componentId: "sc-e71xhh-3"
})(["font-size:", ";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"], me.sm), ha = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsSection",
  componentId: "sc-e71xhh-4"
})(["display:flex;align-items:center;gap:", ";flex-wrap:wrap;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), xa = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusIndicator",
  componentId: "sc-e71xhh-5"
})(["display:flex;align-items:center;gap:", ";padding:", " ", ";border-radius:", ";font-size:", ";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  $isLive: e,
  $variant: r
}) => e ? g(["background:rgba(220,38,38,0.1);border:1px solid #dc2626;color:#dc2626;"]) : r === "active" ? g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]) : g(["background:rgba(156,163,175,0.1);border:1px solid #9ca3af;color:#9ca3af;"])), ba = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusDot",
  componentId: "sc-e71xhh-6"
})(["width:6px;height:6px;border-radius:50%;background:", ";", ""], ({
  $isLive: e
}) => e ? "#dc2626" : "#22c55e", ({
  $isLive: e
}) => e && g(["animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"])), ya = /* @__PURE__ */ c.button.withConfig({
  displayName: "RefreshButton",
  componentId: "sc-e71xhh-7"
})(["padding:", " ", ";background:transparent;color:", ";border:1px solid #4b5563;border-radius:", ";cursor:pointer;font-weight:500;font-size:", ";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:", ";border-color:", ";}&:disabled{opacity:0.6;cursor:not-allowed;}", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  $isRefreshing: e
}) => e && g(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])), va = /* @__PURE__ */ c.div.withConfig({
  displayName: "CustomActions",
  componentId: "sc-e71xhh-8"
})(["display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), Fc = (e) => {
  const {
    title: r,
    subtitle: t,
    isLive: n = !1,
    liveText: s = "LIVE SESSION",
    statusText: a,
    onRefresh: i,
    isRefreshing: p = !1,
    actions: f,
    variant: l = "dashboard",
    className: d
  } = e, h = n ? s : a;
  return /* @__PURE__ */ o.jsxs(pa, { $variant: l, className: d, children: [
    /* @__PURE__ */ o.jsxs(fa, { children: [
      /* @__PURE__ */ o.jsx(ga, { $variant: l, children: l === "dashboard" ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
        "🏎️ ",
        r.replace("Trading", "TRADING").replace("Dashboard", "DASHBOARD")
      ] }) : r }),
      t && /* @__PURE__ */ o.jsx(ma, { children: t })
    ] }),
    /* @__PURE__ */ o.jsxs(ha, { children: [
      h && /* @__PURE__ */ o.jsxs(xa, { $isLive: n, $variant: !n && a ? "active" : void 0, children: [
        /* @__PURE__ */ o.jsx(ba, { $isLive: n }),
        h
      ] }),
      i && /* @__PURE__ */ o.jsx(ya, { onClick: i, disabled: p, $isRefreshing: p, children: p ? "Refreshing..." : "Refresh" }),
      f && /* @__PURE__ */ o.jsx(va, { children: f })
    ] })
  ] });
}, ur = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-vuv4tf-0"
})(["display:flex;flex-direction:column;width:100%;max-width:", ";margin:0 auto;min-height:", ";", " ", " ", " ", ""], ({
  $maxWidth: e
}) => typeof e == "number" ? `${e}px` : e, ({
  $variant: e
}) => e === "dashboard" ? "100vh" : "auto", ({
  $padding: e
}) => {
  const r = {
    sm: re.sm,
    md: re.md,
    lg: re.lg,
    xl: re.xl
  };
  return g(["padding:", ";"], r[e || "lg"]);
}, ({
  $background: e,
  theme: r
}) => {
  const t = {
    default: r.colors.background,
    surface: r.colors.surface,
    elevated: r.colors.elevated
  };
  return g(["background:", ";"], t[e || "default"]);
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["gap:24px;padding-top:0;"]);
    case "form":
      return g(["gap:16px;max-width:800px;"]);
    case "analysis":
      return g(["gap:20px;max-width:1400px;"]);
    case "settings":
      return g(["gap:16px;max-width:1000px;"]);
    default:
      return g(["gap:16px;"]);
  }
}, ({
  $animated: e
}) => e && g(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])), wa = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingContainer",
  componentId: "sc-vuv4tf-1"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]), Sa = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-vuv4tf-2"
})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-vuv4tf-3"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]), Ia = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorIcon",
  componentId: "sc-vuv4tf-4"
})(["font-size:48px;opacity:0.8;"]), ja = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-vuv4tf-5"
})(["font-size:16px;font-weight:500;"]), Ta = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-vuv4tf-6"
})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]), ht = () => /* @__PURE__ */ o.jsxs(wa, { children: [
  /* @__PURE__ */ o.jsx(Sa, {}),
  /* @__PURE__ */ o.jsx("div", { children: "Loading..." })
] }), Ea = ({
  error: e,
  onRetry: r
}) => /* @__PURE__ */ o.jsxs(Ca, { children: [
  /* @__PURE__ */ o.jsx(Ia, { children: "⚠️" }),
  /* @__PURE__ */ o.jsx(ja, { children: e }),
  r && /* @__PURE__ */ o.jsx(Ta, { onClick: r, children: "Retry" })
] }), qc = (e) => {
  const {
    children: r,
    variant: t = "dashboard",
    maxWidth: n = "100%",
    padding: s = "lg",
    isLoading: a = !1,
    error: i = null,
    loadingFallback: p,
    errorFallback: f,
    className: l,
    animated: d = !0,
    background: h = "default"
  } = e, x = {
    $variant: t,
    $maxWidth: n,
    $padding: s,
    $animated: d,
    $background: h
  };
  return i ? /* @__PURE__ */ o.jsx(ur, { ...x, className: l, children: f || /* @__PURE__ */ o.jsx(Ea, { error: i }) }) : a ? /* @__PURE__ */ o.jsx(ur, { ...x, className: l, children: p || /* @__PURE__ */ o.jsx(ht, {}) }) : /* @__PURE__ */ o.jsx(ur, { ...x, className: l, children: /* @__PURE__ */ o.jsx(to, { fallback: p || /* @__PURE__ */ o.jsx(ht, {}), children: r }) });
}, Na = /* @__PURE__ */ c.form.withConfig({
  displayName: "FormContainer",
  componentId: "sc-1gwzj6e-0"
})(["display:flex;flex-direction:column;gap:", ";background:", ";border-radius:", ";border:1px solid ", ";position:relative;", " ", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "quick":
      return g(["padding:", ";max-width:600px;"], re.lg);
    case "detailed":
      return g(["padding:", ";max-width:800px;"], re.xl);
    case "modal":
      return g(["padding:", ";max-width:500px;margin:0 auto;"], re.lg);
    case "inline":
      return g(["padding:", ";background:transparent;border:none;"], re.md);
    default:
      return g(["padding:", ";"], re.lg);
  }
}, ({
  $showAccent: e,
  theme: r
}) => {
  var t, n, s, a, i;
  return e && g(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,", ",", ",", " );border-radius:", " ", " 0 0;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primaryDark) || "#b91c1c", ((s = r.colors) == null ? void 0 : s.primary) || "#dc2626", ((a = r.borderRadius) == null ? void 0 : a.lg) || "8px", ((i = r.borderRadius) == null ? void 0 : i.lg) || "8px");
}, ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), ka = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormHeader",
  componentId: "sc-1gwzj6e-1"
})(["display:flex;flex-direction:column;gap:", ";margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), Ra = /* @__PURE__ */ c.h3.withConfig({
  displayName: "FormTitle",
  componentId: "sc-1gwzj6e-2"
})(["font-size:", ";font-weight:700;color:", ";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.lg) || "1.125rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), La = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormSubtitle",
  componentId: "sc-1gwzj6e-3"
})(["font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), _a = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormContent",
  componentId: "sc-1gwzj6e-4"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), xt = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormMessage",
  componentId: "sc-1gwzj6e-5"
})(["padding:", " ", ";border-radius:", ";font-size:", ";font-weight:500;display:flex;align-items:center;gap:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $type: e
}) => {
  switch (e) {
    case "error":
      return g(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);
    case "success":
      return g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);
    case "info":
      return g(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"]);
  }
}), Ma = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-1gwzj6e-6"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:", ";z-index:10;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}), Pa = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1gwzj6e-7"
})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Da = /* @__PURE__ */ c.div.withConfig({
  displayName: "AutoSaveIndicator",
  componentId: "sc-1gwzj6e-8"
})(["position:absolute;top:8px;right:8px;font-size:", ";color:", ";opacity:", ";transition:opacity 0.3s ease;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  $visible: e
}) => e ? 1 : 0), Bc = (e) => {
  const {
    children: r,
    onSubmit: t,
    title: n,
    subtitle: s,
    isSubmitting: a = !1,
    error: i = null,
    success: p = null,
    variant: f = "quick",
    showAccent: l = !0,
    className: d,
    disabled: h = !1,
    autoSave: x = !1,
    autoSaveInterval: y = 3e4
  } = e, m = async (b) => {
    b.preventDefault(), t && !a && !h && await t(b);
  };
  return /* @__PURE__ */ o.jsxs(Na, { $variant: f, $showAccent: l, $disabled: h, className: d, onSubmit: m, noValidate: !0, children: [
    a && /* @__PURE__ */ o.jsx(Ma, { children: /* @__PURE__ */ o.jsx(Pa, {}) }),
    x && /* @__PURE__ */ o.jsx(Da, { $visible: !a, children: "Auto-save enabled" }),
    (n || s) && /* @__PURE__ */ o.jsxs(ka, { children: [
      n && /* @__PURE__ */ o.jsx(Ra, { children: n }),
      s && /* @__PURE__ */ o.jsx(La, { children: s })
    ] }),
    i && /* @__PURE__ */ o.jsxs(xt, { $type: "error", children: [
      "⚠️ ",
      i
    ] }),
    p && /* @__PURE__ */ o.jsxs(xt, { $type: "success", children: [
      "✅ ",
      p
    ] }),
    /* @__PURE__ */ o.jsx(_a, { children: r })
  ] });
}, $a = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-sq94oz-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  $size: e
}) => ({
  sm: re.xs,
  md: re.sm,
  lg: re.md
})[e || "md"]), Oa = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-sq94oz-1"
})(["font-size:", ";font-weight:600;color:", ";display:flex;align-items:center;gap:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "trading":
      return g(["text-transform:uppercase;letter-spacing:0.025em;"]);
    case "analysis":
      return g(["font-weight:500;"]);
    default:
      return g([""]);
  }
}, ({
  $required: e,
  theme: r
}) => {
  var t;
  return e && g(["&::after{content:'*';color:", ";margin-left:2px;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}), za = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-sq94oz-2"
})(["position:relative;display:flex;align-items:center;", ""], ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), Tr = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background:", ";color:", ";font-family:inherit;transition:all 0.2s ease;", " &:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"], ({
  $hasError: e,
  theme: r
}) => {
  var t;
  return e ? ((t = r.colors) == null ? void 0 : t.error) || "#f44336" : "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $size: e
}) => ({
  sm: g(["padding:", " ", ";font-size:", ";"], re.xs, re.sm, me.sm),
  md: g(["padding:", " ", ";font-size:", ";"], re.sm, re.md, me.md),
  lg: g(["padding:", " ", ";font-size:", ";"], re.md, re.lg, me.lg)
})[e || "md"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), Aa = /* @__PURE__ */ c.input.withConfig({
  displayName: "Input",
  componentId: "sc-sq94oz-3"
})(["", ""], Tr), Fa = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-sq94oz-4"
})(["", " cursor:pointer;option{background:", ";color:", ";}"], Tr, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), qa = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "TextArea",
  componentId: "sc-sq94oz-5"
})(["", " resize:vertical;min-height:80px;font-family:inherit;"], Tr), Ba = /* @__PURE__ */ c.div.withConfig({
  displayName: "PrefixContainer",
  componentId: "sc-sq94oz-6"
})(["position:absolute;left:12px;display:flex;align-items:center;color:", ";pointer-events:none;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Ha = /* @__PURE__ */ c.div.withConfig({
  displayName: "SuffixContainer",
  componentId: "sc-sq94oz-7"
})(["position:absolute;right:12px;display:flex;align-items:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Ua = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-sq94oz-8"
})(["font-size:", ";color:", ";font-weight:500;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#f44336";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), Ya = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-sq94oz-9"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Va = /* @__PURE__ */ c.div.withConfig({
  displayName: "ValidationIndicator",
  componentId: "sc-sq94oz-10"
})(["position:absolute;right:8px;display:flex;align-items:center;", " ", ""], ({
  $validating: e
}) => e && g(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), ({
  $valid: e,
  $validating: r
}) => !r && g(["color:", ";&::after{content:'", "';}"], e ? "#22c55e" : "#f44336", e ? "✓" : "✗")), Hc = (e) => {
  const {
    label: r,
    field: t,
    type: n = "text",
    placeholder: s,
    required: a = !1,
    disabled: i = !1,
    helpText: p,
    options: f = [],
    inputProps: l = {},
    className: d,
    size: h = "md",
    variant: x = "default",
    prefix: y,
    suffix: m
  } = e, b = !!(t.error && t.touched), C = t.touched && !t.validating, w = () => {
    const S = {
      id: l.id || r.toLowerCase().replace(/\s+/g, "-"),
      value: t.value,
      onChange: t.setValue,
      onBlur: () => t.setTouched(!0),
      disabled: i,
      placeholder: s,
      $hasError: b,
      $size: h,
      ...l
    };
    switch (n) {
      case "select":
        return /* @__PURE__ */ o.jsxs(Fa, { ...S, children: [
          s && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: s }),
          f.map((_) => /* @__PURE__ */ o.jsx("option", { value: _.value, children: _.label }, _.value))
        ] });
      case "textarea":
        return /* @__PURE__ */ o.jsx(qa, { ...S });
      default:
        return /* @__PURE__ */ o.jsx(Aa, { ...S, type: n });
    }
  };
  return /* @__PURE__ */ o.jsxs($a, { $size: h, className: d, children: [
    /* @__PURE__ */ o.jsx(Oa, { $required: a, $variant: x, htmlFor: l.id || r.toLowerCase().replace(/\s+/g, "-"), children: r }),
    /* @__PURE__ */ o.jsxs(za, { $hasError: b, $disabled: i, children: [
      y && /* @__PURE__ */ o.jsx(Ba, { children: y }),
      w(),
      m && /* @__PURE__ */ o.jsx(Ha, { children: m }),
      C && /* @__PURE__ */ o.jsx(Va, { $valid: t.valid, $validating: t.validating })
    ] }),
    b && /* @__PURE__ */ o.jsxs(Ua, { children: [
      "⚠️ ",
      t.error
    ] }),
    p && !b && /* @__PURE__ */ o.jsx(Ya, { children: p })
  ] });
}, Wa = (e = !1) => {
  const [r, t] = V(e), [n, s] = V(null), [a, i] = V(!1), p = F((y) => {
    t(y), y && (s(null), i(!1));
  }, []), f = F((y) => {
    s(y), t(!1), i(!1);
  }, []), l = F(() => {
    s(null);
  }, []), d = F(() => {
    t(!1), s(null), i(!1);
  }, []), h = F(async (y) => {
    p(!0);
    try {
      const m = await y();
      return i(!0), t(!1), m;
    } catch (m) {
      const b = m instanceof Error ? m.message : "An unexpected error occurred";
      throw f(b), m;
    }
  }, [p, f]), x = F((y) => async (...m) => {
    try {
      await h(() => y(...m));
    } catch (b) {
      console.error("Operation failed:", b);
    }
  }, [h]);
  return {
    // State
    isLoading: r,
    error: n,
    isSuccess: a,
    isError: n !== null,
    // Actions
    setLoading: p,
    setError: f,
    clearError: l,
    reset: d,
    withLoading: h,
    withLoadingCallback: x
  };
};
function Uc(e, r = {}) {
  const {
    fetchOnMount: t = !0,
    dependencies: n = []
  } = r, [s, a] = V({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), i = F(async (...p) => {
    a((f) => ({
      ...f,
      isLoading: !0,
      error: null
    }));
    try {
      const f = await e(...p);
      return a({
        data: f,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), f;
    } catch (f) {
      const l = f instanceof Error ? f : new Error(String(f));
      throw a((d) => ({
        ...d,
        isLoading: !1,
        error: l,
        isInitialized: !0
      })), l;
    }
  }, [e]);
  return le(() => {
    t && i();
  }, [t, i, ...n]), {
    ...s,
    fetchData: i,
    refetch: () => i()
  };
}
function Yc(e, r) {
  const [t, n] = V(e);
  return le(() => {
    const s = setTimeout(() => {
      n(e);
    }, r);
    return () => {
      clearTimeout(s);
    };
  }, [e, r]), t;
}
function Vc(e = {}) {
  const {
    componentName: r,
    logToConsole: t = !0,
    reportToMonitoring: n = !0,
    onError: s
  } = e, [a, i] = V(null), [p, f] = V(!1), l = F((x) => {
    if (i(x), f(!0), t) {
      const y = r ? `[${r}]` : "";
      console.error(`Error caught by useErrorHandler${y}:`, x);
    }
    s && s(x);
  }, [r, t, n, s]), d = F(() => {
    i(null), f(!1);
  }, []), h = F(async (x) => {
    try {
      return await x();
    } catch (y) {
      l(y);
      return;
    }
  }, [l]);
  return le(() => () => {
    i(null), f(!1);
  }, []), {
    error: a,
    hasError: p,
    handleError: l,
    resetError: d,
    tryExecute: h
  };
}
function bt(e, r) {
  const t = () => {
    if (typeof window > "u")
      return r;
    try {
      const i = window.localStorage.getItem(e);
      return i ? JSON.parse(i) : r;
    } catch (i) {
      return console.warn(`Error reading localStorage key "${e}":`, i), r;
    }
  }, [n, s] = V(t), a = (i) => {
    try {
      const p = i instanceof Function ? i(n) : i;
      s(p), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(p));
    } catch (p) {
      console.warn(`Error setting localStorage key "${e}":`, p);
    }
  };
  return le(() => {
    const i = (p) => {
      p.key === e && p.newValue && s(JSON.parse(p.newValue));
    };
    return window.addEventListener("storage", i), () => window.removeEventListener("storage", i);
  }, [e]), [n, a];
}
function Wc(e) {
  const {
    totalItems: r,
    itemsPerPage: t = 10,
    initialPage: n = 1,
    persistKey: s
  } = e, [a, i] = s ? bt(`${s}_page`, n) : V(n), [p, f] = s ? bt(`${s}_itemsPerPage`, t) : V(t), l = Y(() => Math.max(1, Math.ceil(r / p)), [r, p]), d = Y(() => Math.min(Math.max(1, a), l), [a, l]);
  d !== a && i(d);
  const h = (d - 1) * p, x = Math.min(h + p - 1, r - 1), y = d > 1, m = d < l, b = Y(() => {
    const $ = [];
    if (l <= 5)
      for (let R = 1; R <= l; R++)
        $.push(R);
    else {
      let R = Math.max(1, d - Math.floor(2.5));
      const E = Math.min(l, R + 5 - 1);
      E === l && (R = Math.max(1, E - 5 + 1));
      for (let L = R; L <= E; L++)
        $.push(L);
    }
    return $;
  }, [d, l]), C = F(() => {
    m && i(d + 1);
  }, [m, d, i]), w = F(() => {
    y && i(d - 1);
  }, [y, d, i]), S = F((z) => {
    const $ = Math.min(Math.max(1, z), l);
    i($);
  }, [l, i]), _ = F((z) => {
    f(z), i(1);
  }, [f, i]);
  return {
    currentPage: d,
    itemsPerPage: p,
    totalPages: l,
    hasPreviousPage: y,
    hasNextPage: m,
    startIndex: h,
    endIndex: x,
    pageRange: b,
    nextPage: C,
    previousPage: w,
    goToPage: S,
    setItemsPerPage: _
  };
}
const Ga = (e, r = "$", t = !1) => {
  const s = Math.abs(e).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  return e > 0 ? t ? `+${r}${s}` : `${r}${s}` : e < 0 ? `-${r}${s}` : `${r}${s}`;
}, Gc = (e, r = {}) => {
  const {
    currency: t = "$",
    showPositiveSign: n = !1,
    customAriaLabel: s
  } = r;
  return Y(() => {
    if (e == null)
      return {
        formattedAmount: "",
        isProfit: !1,
        isLoss: !1,
        isNeutral: !1,
        isEmpty: !0,
        ariaLabel: s || "No profit/loss data available"
      };
    const a = e > 0, i = e < 0, p = e === 0, f = Ga(e, t, n), l = `${a ? "Profit" : i ? "Loss" : "Breakeven"} of ${f}`;
    return {
      formattedAmount: f,
      isProfit: a,
      isLoss: i,
      isNeutral: p,
      isEmpty: !1,
      ariaLabel: s || l
    };
  }, [e, t, n, s]);
}, Ka = (e) => e == null ? !0 : Array.isArray(e) ? e.length === 0 : typeof e == "object" ? Object.keys(e).length === 0 : typeof e == "string" ? e.trim().length === 0 : !1, Kc = (e) => {
  const {
    fetchData: r,
    initialData: t = null,
    fetchOnMount: n = !0,
    refreshInterval: s,
    isEmpty: a = Ka,
    transformError: i,
    dependencies: p = []
  } = e, [f, l] = V(t), [d, h] = V(null), x = Wa(), y = Y(() => f === null || a(f), [f, a]), m = F(async () => {
    try {
      const S = await x.withLoading(r);
      l(S), h(/* @__PURE__ */ new Date());
    } catch (S) {
      const _ = i && S instanceof Error ? i(S) : S instanceof Error ? S.message : "Failed to fetch data";
      x.setError(_), console.error("Data fetch failed:", S);
    }
  }, [r, x, i]), b = F(async () => {
    await m();
  }, [m]), C = F(() => {
    l(t), h(null), x.reset();
  }, [t, x]), w = F((S) => {
    l(S), h(/* @__PURE__ */ new Date()), x.clearError();
  }, [x]);
  return le(() => {
    n && m();
  }, [n, m]), le(() => {
    p.length > 0 && d !== null && m();
  }, p), le(() => {
    if (!s || s <= 0)
      return;
    const S = setInterval(() => {
      !x.isLoading && !x.error && m();
    }, s);
    return () => clearInterval(S);
  }, [s, x.isLoading, x.error, m]), {
    // State
    data: f,
    isLoading: x.isLoading,
    error: x.error,
    isEmpty: y,
    isSuccess: x.isSuccess,
    isError: x.isError,
    lastFetched: d,
    // Actions
    refresh: b,
    clearError: x.clearError,
    reset: C,
    setData: w
  };
}, Qc = (e = "en-US") => Y(() => ({
  formatCurrency: (f, l = {}) => {
    const {
      currency: d = "USD",
      locale: h = e,
      minimumFractionDigits: x = 2,
      maximumFractionDigits: y = 2,
      showPositiveSign: m = !1
    } = l, C = new Intl.NumberFormat(h, {
      style: "currency",
      currency: d,
      minimumFractionDigits: x,
      maximumFractionDigits: y
    }).format(Math.abs(f));
    return f > 0 && m ? `+${C}` : f < 0 ? `-${C}` : C;
  },
  formatPercent: (f, l = {}) => {
    const {
      locale: d = e,
      minimumFractionDigits: h = 2,
      maximumFractionDigits: x = 2,
      showPositiveSign: y = !1
    } = l, m = new Intl.NumberFormat(d, {
      style: "percent",
      minimumFractionDigits: h,
      maximumFractionDigits: x
    }), b = f > 1 ? f / 100 : f, C = m.format(Math.abs(b));
    return b > 0 && y ? `+${C}` : b < 0 ? `-${C}` : C;
  },
  formatNumber: (f, l = {}) => {
    const {
      locale: d = e,
      minimumFractionDigits: h = 0,
      maximumFractionDigits: x = 2,
      useGrouping: y = !0
    } = l;
    return new Intl.NumberFormat(d, {
      minimumFractionDigits: h,
      maximumFractionDigits: x,
      useGrouping: y
    }).format(f);
  },
  formatDate: (f, l = "medium") => {
    const d = typeof f == "string" ? new Date(f) : f;
    return new Intl.DateTimeFormat(e, {
      dateStyle: l
    }).format(d);
  },
  formatTime: (f, l = "short") => {
    const d = typeof f == "string" ? new Date(f) : f;
    return new Intl.DateTimeFormat(e, {
      timeStyle: l
    }).format(d);
  },
  formatRelativeTime: (f) => {
    const l = typeof f == "string" ? new Date(f) : f, h = Math.floor(((/* @__PURE__ */ new Date()).getTime() - l.getTime()) / 1e3);
    if (typeof Intl.RelativeTimeFormat < "u") {
      const b = new Intl.RelativeTimeFormat(e, {
        numeric: "auto"
      }), C = [{
        unit: "year",
        seconds: 31536e3
      }, {
        unit: "month",
        seconds: 2592e3
      }, {
        unit: "day",
        seconds: 86400
      }, {
        unit: "hour",
        seconds: 3600
      }, {
        unit: "minute",
        seconds: 60
      }, {
        unit: "second",
        seconds: 1
      }];
      for (const w of C) {
        const S = Math.floor(Math.abs(h) / w.seconds);
        if (S >= 1)
          return b.format(h > 0 ? -S : S, w.unit);
      }
      return b.format(0, "second");
    }
    const x = Math.abs(h), y = h < 0;
    if (x < 60)
      return y ? "in a few seconds" : "a few seconds ago";
    if (x < 3600) {
      const b = Math.floor(x / 60);
      return y ? `in ${b} minute${b > 1 ? "s" : ""}` : `${b} minute${b > 1 ? "s" : ""} ago`;
    }
    if (x < 86400) {
      const b = Math.floor(x / 3600);
      return y ? `in ${b} hour${b > 1 ? "s" : ""}` : `${b} hour${b > 1 ? "s" : ""} ago`;
    }
    const m = Math.floor(x / 86400);
    return y ? `in ${m} day${m > 1 ? "s" : ""}` : `${m} day${m > 1 ? "s" : ""} ago`;
  }
}), [e]), Qa = {
  small: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xxs) || "2px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }),
  medium: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }),
  large: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.lg) || "18px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
  })
}, Xa = {
  profit: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.profit) || ((t = e.colors) == null ? void 0 : t.success) || "#4caf50";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}15` : "rgba(76, 175, 80, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}30` : "rgba(76, 175, 80, 0.2)";
  }),
  loss: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.loss) || ((t = e.colors) == null ? void 0 : t.error) || "#f44336";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}15` : "rgba(244, 67, 54, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}30` : "rgba(244, 67, 54, 0.2)";
  }),
  neutral: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.neutral) || ((t = e.colors) == null ? void 0 : t.textSecondary) || "#757575";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}15` : "rgba(117, 117, 117, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}30` : "rgba(117, 117, 117, 0.2)";
  }),
  default: g(["color:", ";background-color:transparent;border:1px solid transparent;"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
  })
}, Xc = /* @__PURE__ */ g(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:", ";font-family:", ";transition:", ";border-radius:", ";&:hover{transform:translateY(-1px);box-shadow:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontFamilies) == null ? void 0 : r.mono) || "monospace";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.shadows) == null ? void 0 : r.sm) || "0 2px 4px rgba(0, 0, 0, 0.1)";
}), Jc = /* @__PURE__ */ g(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]), Zc = (e) => Qa[e], el = (e, r, t) => e ? "profit" : r ? "loss" : t ? "neutral" : "default", rl = (e) => Xa[e], Et = {
  name: "f1",
  colors: {
    // Primary colors
    primary: T.f1Red,
    primaryDark: T.f1RedDark,
    primaryLight: T.f1RedLight,
    // Secondary colors
    secondary: T.f1Blue,
    secondaryDark: T.f1BlueDark,
    secondaryLight: T.f1BlueLight,
    // Accent colors
    accent: T.purple,
    accentDark: T.purpleDark,
    accentLight: T.purpleLight,
    // Status colors
    success: B.success,
    warning: B.warning,
    error: B.error,
    danger: B.error,
    // Use error color for danger
    info: B.info,
    // Neutral colors
    background: B.background,
    surface: B.surface,
    elevated: T.gray700,
    // Added elevated color for F1 theme
    cardBackground: B.surface,
    border: B.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: B.textPrimary,
    textSecondary: B.textSecondary,
    textDisabled: B.textDisabled,
    textInverse: B.textInverse,
    // Chart colors
    chartGrid: B.chartGrid,
    chartLine: B.chartLine,
    chartAxis: T.gray400,
    chartTooltip: B.tooltipBackground,
    // Trading specific colors
    profit: B.profit,
    loss: B.loss,
    neutral: B.neutral,
    // Tab colors
    tabActive: T.f1Red,
    tabInactive: T.gray600,
    // Component specific colors
    tooltipBackground: B.tooltipBackground,
    modalBackground: B.modalBackground,
    sidebarBackground: T.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)"
  },
  spacing: re,
  breakpoints: wr,
  fontSizes: me,
  fontWeights: br,
  lineHeights: yr,
  fontFamilies: vr,
  borderRadius: Sr,
  shadows: Cr,
  transitions: Ir,
  zIndex: jr
}, Ja = {
  name: "light",
  colors: {
    // Primary colors
    primary: T.f1Red,
    primaryDark: T.f1RedDark,
    primaryLight: T.f1RedLight,
    // Secondary colors
    secondary: T.f1Blue,
    secondaryDark: T.f1BlueDark,
    secondaryLight: T.f1BlueLight,
    // Accent colors
    accent: T.purple,
    accentDark: T.purpleDark,
    accentLight: T.purpleLight,
    // Status colors
    success: ee.success,
    warning: ee.warning,
    error: ee.error,
    danger: ee.error,
    // Use error color for danger
    info: ee.info,
    // Neutral colors
    background: ee.background,
    surface: ee.surface,
    elevated: T.gray100,
    // Added elevated color for light theme
    cardBackground: ee.surface,
    border: ee.border,
    divider: T.blackTransparent10,
    // Text colors
    textPrimary: ee.textPrimary,
    textSecondary: ee.textSecondary,
    textDisabled: ee.textDisabled,
    textInverse: ee.textInverse,
    // Chart colors
    chartGrid: ee.chartGrid,
    chartLine: ee.chartLine,
    chartAxis: T.gray600,
    chartTooltip: ee.tooltipBackground,
    // Trading specific colors
    profit: ee.profit,
    loss: ee.loss,
    neutral: ee.neutral,
    // Tab colors
    tabActive: T.f1Red,
    tabInactive: T.gray400,
    // Component specific colors
    tooltipBackground: ee.tooltipBackground,
    modalBackground: ee.modalBackground,
    sidebarBackground: T.white,
    headerBackground: "rgba(0, 0, 0, 0.05)"
  },
  spacing: re,
  breakpoints: wr,
  fontSizes: me,
  fontWeights: br,
  lineHeights: yr,
  fontFamilies: vr,
  borderRadius: Sr,
  shadows: Cr,
  transitions: Ir,
  zIndex: jr
}, Za = {
  name: "dark",
  colors: {
    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)
    primary: T.f1Blue,
    primaryDark: T.f1BlueDark,
    primaryLight: T.f1BlueLight,
    // Secondary colors
    secondary: T.f1Blue,
    secondaryDark: T.f1BlueDark,
    secondaryLight: T.f1BlueLight,
    // Accent colors
    accent: T.purple,
    accentDark: T.purpleDark,
    accentLight: T.purpleLight,
    // Status colors
    success: B.success,
    warning: B.warning,
    error: B.error,
    danger: B.error,
    // Use error color for danger
    info: B.info,
    // Neutral colors
    background: T.gray900,
    // Slightly different from F1 theme
    surface: T.gray800,
    elevated: T.gray700,
    // Added elevated color for dark theme
    cardBackground: T.gray800,
    border: T.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: T.white,
    textSecondary: T.gray300,
    textDisabled: T.gray500,
    textInverse: T.gray900,
    // Chart colors
    chartGrid: B.chartGrid,
    chartLine: T.f1Blue,
    // Using blue instead of red
    chartAxis: T.gray400,
    chartTooltip: B.tooltipBackground,
    // Trading specific colors
    profit: B.profit,
    loss: B.loss,
    neutral: B.neutral,
    // Tab colors
    tabActive: T.f1Blue,
    // Using blue instead of red
    tabInactive: T.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: T.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)"
  },
  spacing: re,
  breakpoints: wr,
  fontSizes: me,
  fontWeights: br,
  lineHeights: yr,
  fontFamilies: vr,
  borderRadius: Sr,
  shadows: Cr,
  transitions: Ir,
  zIndex: jr
}, ec = /* @__PURE__ */ no(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), rc = ec, tc = {
  f1: Et,
  light: Ja,
  dark: Za
}, Er = Et, pr = (e) => tc[e] || Er, Nt = vt({
  theme: Er,
  setTheme: () => {
  }
}), tl = () => wt(Nt), ol = ({
  initialTheme: e = Er,
  persistTheme: r = !0,
  storageKey: t = "adhd-dashboard-theme",
  children: n
}) => {
  const [s, a] = V(() => {
    if (r && typeof window < "u") {
      const l = window.localStorage.getItem(t);
      if (l)
        try {
          const d = pr(l);
          return d || JSON.parse(l);
        } catch (d) {
          console.error("Failed to parse stored theme:", d);
        }
    }
    return typeof e == "string" ? pr(e) : e;
  }), i = (f) => {
    const l = typeof f == "string" ? pr(f) : f;
    a(l), r && typeof window < "u" && window.localStorage.setItem(t, l.name || JSON.stringify(l));
  }, p = ({
    children: f
  }) => /* @__PURE__ */ o.jsxs(so, { theme: s, children: [
    /* @__PURE__ */ o.jsx(rc, {}),
    f
  ] });
  return /* @__PURE__ */ o.jsx(Nt.Provider, { value: {
    theme: s,
    setTheme: i
  }, children: /* @__PURE__ */ o.jsx(p, { children: n }) });
};
function nl(e, r, t = "StoreContext") {
  const n = vt(void 0);
  n.displayName = t;
  const s = ({
    children: l,
    initialState: d
  }) => {
    const [h, x] = oo(e, d || r), y = Y(() => ({
      state: h,
      dispatch: x
    }), [h]);
    return /* @__PURE__ */ o.jsx(n.Provider, { value: y, children: l });
  };
  function a() {
    const l = wt(n);
    if (l === void 0)
      throw new Error(`use${t} must be used within a ${t}Provider`);
    return l;
  }
  function i(l) {
    const {
      state: d
    } = a();
    return l(d);
  }
  function p(l) {
    const {
      dispatch: d
    } = a();
    return Y(() => (...h) => {
      d(l(...h));
    }, [d, l]);
  }
  function f(l) {
    const {
      dispatch: d
    } = a();
    return Y(() => {
      const h = {};
      for (const x in l)
        h[x] = (...y) => {
          d(l[x](...y));
        };
      return h;
    }, [d, l]);
  }
  return {
    Context: n,
    Provider: s,
    useStore: a,
    useSelector: i,
    useAction: p,
    useActions: f
  };
}
function sl(...e) {
  const r = e.pop(), t = e;
  let n = null, s = null;
  return (a) => {
    const i = t.map((p) => p(a));
    return (n === null || i.length !== n.length || i.some((p, f) => p !== n[f])) && (s = r(...i), n = i), s;
  };
}
function il(e, r) {
  const {
    key: t,
    initialState: n,
    version: s = 1,
    migrate: a,
    serialize: i = JSON.stringify,
    deserialize: p = JSON.parse,
    filter: f = (w) => w,
    merge: l = (w, S) => ({
      ...S,
      ...w
    }),
    debug: d = !1
  } = r, h = () => {
    try {
      const w = localStorage.getItem(t);
      if (w === null)
        return null;
      const {
        state: S,
        version: _
      } = p(w);
      return _ !== s && a ? (d && console.log(`Migrating state from version ${_} to ${s}`), a(S, _)) : S;
    } catch (w) {
      return d && console.error("Error loading state from local storage:", w), null;
    }
  }, x = (w) => {
    try {
      const S = f(w), _ = i({
        state: S,
        version: s
      });
      localStorage.setItem(t, _);
    } catch (S) {
      d && console.error("Error saving state to local storage:", S);
    }
  }, y = () => {
    try {
      localStorage.removeItem(t);
    } catch (w) {
      d && console.error("Error clearing state from local storage:", w);
    }
  }, m = h(), b = m ? l(m, n) : n;
  return d && m && (console.log("Loaded persisted state:", m), console.log("Merged initial state:", b)), {
    reducer: (w, S) => {
      const _ = e(w, S);
      return x(_), _;
    },
    initialState: b,
    clear: y
  };
}
function al(e, r = "$") {
  return `${r}${e.toFixed(2)}`;
}
function cl(e, r = 1) {
  return `${(e * 100).toFixed(r)}%`;
}
function ll(e, r = "short") {
  const t = typeof e == "string" ? new Date(e) : e;
  switch (r) {
    case "medium":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function dl(e, r = 50) {
  return e.length <= r ? e : `${e.substring(0, r - 3)}...`;
}
function ul() {
  return Math.random().toString(36).substring(2, 9);
}
function pl(e, r) {
  let t = null;
  return function(...n) {
    const s = () => {
      t = null, e(...n);
    };
    t && clearTimeout(t), t = setTimeout(s, r);
  };
}
function fl(e, r) {
  let t = !1;
  return function(...n) {
    t || (e(...n), t = !0, setTimeout(() => {
      t = !1;
    }, r));
  };
}
function gl(e = {}) {
  console.log("Monitoring service initialized", e);
}
function ml(e, r) {
  console.error("Error captured by monitoring service:", e, r);
}
function hl(e) {
  console.log("User set for monitoring service:", e);
}
function xl(e, r) {
  const t = performance.now();
  return {
    name: e,
    startTime: t,
    finish: () => {
      const s = performance.now() - t;
      console.log(`Transaction "${e}" finished in ${s.toFixed(2)}ms`, r);
    }
  };
}
const Q = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  R_MULTIPLE: "r_multiple",
  DATE: "date",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  ACHIEVED_PL: "achieved_pl",
  PATTERN_QUALITY_RATING: "pattern_quality_rating"
};
class oc {
  constructor() {
    this.dbName = "adhd-trading-dashboard", this.version = 2, this.db = null, this.stores = {
      trades: "trades",
      fvg_details: "trade_fvg_details",
      setups: "trade_setups",
      analysis: "trade_analysis",
      sessions: "trading_sessions"
    };
  }
  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  async initDB() {
    return this.db ? this.db : new Promise((r, t) => {
      const n = indexedDB.open(this.dbName, this.version);
      n.onupgradeneeded = (s) => {
        var i;
        const a = s.target.result;
        if (!a.objectStoreNames.contains(this.stores.trades)) {
          const p = a.createObjectStore(this.stores.trades, {
            keyPath: "id",
            autoIncrement: !0
          });
          p.createIndex(Q.DATE, Q.DATE, {
            unique: !1
          }), p.createIndex(Q.MODEL_TYPE, Q.MODEL_TYPE, {
            unique: !1
          }), p.createIndex(Q.SESSION, Q.SESSION, {
            unique: !1
          }), p.createIndex(Q.WIN_LOSS, Q.WIN_LOSS, {
            unique: !1
          }), p.createIndex(Q.R_MULTIPLE, Q.R_MULTIPLE, {
            unique: !1
          });
        }
        if (a.objectStoreNames.contains(this.stores.fvg_details) || a.createObjectStore(this.stores.fvg_details, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.setups) || a.createObjectStore(this.stores.setups, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.analysis) || a.createObjectStore(this.stores.analysis, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), !a.objectStoreNames.contains(this.stores.sessions)) {
          a.createObjectStore(this.stores.sessions, {
            keyPath: "id",
            autoIncrement: !0
          }).createIndex("name", "name", {
            unique: !0
          });
          const f = [{
            name: "Pre-Market",
            start_time: "04:00:00",
            end_time: "09:30:00",
            description: "Pre-market trading hours"
          }, {
            name: "NY Open",
            start_time: "09:30:00",
            end_time: "10:30:00",
            description: "New York opening hour"
          }, {
            name: "10:50-11:10",
            start_time: "10:50:00",
            end_time: "11:10:00",
            description: "Mid-morning macro window"
          }, {
            name: "11:50-12:10",
            start_time: "11:50:00",
            end_time: "12:10:00",
            description: "Pre-lunch macro window"
          }, {
            name: "Lunch Macro",
            start_time: "12:00:00",
            end_time: "13:30:00",
            description: "Lunch time trading"
          }, {
            name: "13:50-14:10",
            start_time: "13:50:00",
            end_time: "14:10:00",
            description: "Post-lunch macro window"
          }, {
            name: "14:50-15:10",
            start_time: "14:50:00",
            end_time: "15:10:00",
            description: "Pre-close macro window"
          }, {
            name: "15:15-15:45",
            start_time: "15:15:00",
            end_time: "15:45:00",
            description: "Late afternoon window"
          }, {
            name: "MOC",
            start_time: "15:45:00",
            end_time: "16:00:00",
            description: "Market on close"
          }, {
            name: "Post MOC",
            start_time: "16:00:00",
            end_time: "20:00:00",
            description: "After hours trading"
          }];
          (i = n.transaction) == null || i.addEventListener("complete", () => {
            const d = a.transaction([this.stores.sessions], "readwrite").objectStore(this.stores.sessions);
            f.forEach((h) => d.add(h));
          });
        }
      }, n.onsuccess = (s) => {
        this.db = s.target.result, r(this.db);
      }, n.onerror = (s) => {
        console.error("Error opening IndexedDB:", s), t(new Error("Failed to open IndexedDB"));
      };
    });
  }
  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (l) => {
          console.error("Transaction error:", l), s(new Error("Failed to save trade with details"));
        };
        const i = a.objectStore(this.stores.trades), p = {
          ...r.trade,
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, f = i.add(p);
        f.onsuccess = () => {
          const l = f.result, d = [];
          if (r.fvg_details) {
            const h = a.objectStore(this.stores.fvg_details), x = {
              ...r.fvg_details,
              trade_id: l
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save FVG details"));
            }));
          }
          if (r.setup) {
            const h = a.objectStore(this.stores.setups), x = {
              ...r.setup,
              trade_id: l
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save setup data"));
            }));
          }
          if (r.analysis) {
            const h = a.objectStore(this.stores.analysis), x = {
              ...r.analysis,
              trade_id: l
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save analysis data"));
            }));
          }
          a.oncomplete = () => {
            n(l);
          };
        }, f.onerror = (l) => {
          console.error("Error saving trade:", l), s(new Error("Failed to save trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in saveTradeWithDetails:", t), new Error("Failed to save trade with details");
    }
  }
  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), p = a.objectStore(this.stores.trades).get(r);
        p.onsuccess = () => {
          const f = p.result;
          if (!f) {
            n(null);
            return;
          }
          const l = {
            trade: f
          }, x = a.objectStore(this.stores.fvg_details).index("trade_id").get(r);
          x.onsuccess = () => {
            x.result && (l.fvg_details = x.result);
            const b = a.objectStore(this.stores.setups).index("trade_id").get(r);
            b.onsuccess = () => {
              b.result && (l.setup = b.result);
              const S = a.objectStore(this.stores.analysis).index("trade_id").get(r);
              S.onsuccess = () => {
                S.result && (l.analysis = S.result), n(l);
              }, S.onerror = (_) => {
                console.error("Error getting analysis data:", _), n(l);
              };
            }, b.onerror = (C) => {
              console.error("Error getting setup data:", C), n(l);
            };
          }, x.onerror = (y) => {
            console.error("Error getting FVG details:", y), n(l);
          };
        }, p.onerror = (f) => {
          console.error("Error getting trade:", f), s(new Error("Failed to get trade"));
        };
      });
    } catch (t) {
      return console.error("Error in getTradeById:", t), null;
    }
  }
  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const r = await this.initDB();
      return new Promise((t, n) => {
        const i = r.transaction([this.stores.trades], "readonly").objectStore(this.stores.trades).getAll();
        i.onsuccess = () => {
          const p = i.result;
          if (p.length === 0) {
            t({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: "all",
              startDate: "",
              endDate: ""
            });
            return;
          }
          const f = p.length, l = p.filter((D) => D[Q.WIN_LOSS] === "Win").length, d = p.filter((D) => D[Q.WIN_LOSS] === "Loss").length, h = f > 0 ? l / f * 100 : 0, x = p.filter((D) => D.achieved_pl !== void 0).map((D) => D.achieved_pl), y = x.reduce((D, J) => D + J, 0), m = x.filter((D) => D > 0), b = x.filter((D) => D < 0), C = m.length > 0 ? m.reduce((D, J) => D + J, 0) / m.length : 0, w = b.length > 0 ? Math.abs(b.reduce((D, J) => D + J, 0) / b.length) : 0, S = m.length > 0 ? Math.max(...m) : 0, _ = b.length > 0 ? Math.abs(Math.min(...b)) : 0, z = m.reduce((D, J) => D + J, 0), $ = Math.abs(b.reduce((D, J) => D + J, 0)), R = $ > 0 ? z / $ : 0, E = p.filter((D) => D[Q.R_MULTIPLE] !== void 0).map((D) => D[Q.R_MULTIPLE]), L = E.length > 0 ? E.reduce((D, J) => D + J, 0) / E.length : 0, N = L * (h / 100);
          let W = 0, G = 0, k = 0;
          for (const D of p)
            if (D.achieved_pl !== void 0) {
              W += D.achieved_pl, W > G && (G = W);
              const J = G - W;
              J > k && (k = J);
            }
          const U = G > 0 ? k / G * 100 : 0, oe = E.length > 0 ? Math.sqrt(E.length) * L / Math.sqrt(E.reduce((D, J) => D + Math.pow(J - L, 2), 0) / E.length) : 0, ne = p.map((D) => D.date).sort(), fe = ne.length > 0 ? ne[0] : "", se = ne.length > 0 ? ne[ne.length - 1] : "";
          t({
            totalTrades: f,
            winningTrades: l,
            losingTrades: d,
            winRate: h,
            profitFactor: R,
            averageWin: C,
            averageLoss: w,
            largestWin: S,
            largestLoss: _,
            totalPnl: y,
            maxDrawdown: k,
            maxDrawdownPercent: U,
            sharpeRatio: 0,
            // Would need daily returns to calculate
            sortinoRatio: 0,
            // Would need daily returns to calculate
            calmarRatio: 0,
            // Would need daily returns to calculate
            averageRMultiple: L,
            expectancy: N,
            sqn: oe,
            period: "all",
            startDate: fe,
            endDate: se
          });
        }, i.onerror = (p) => {
          console.error("Error getting performance metrics:", p), n(new Error("Failed to get performance metrics"));
        };
      });
    } catch (r) {
      throw console.error("Error in getPerformanceMetrics:", r), new Error("Failed to get performance metrics");
    }
  }
  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), p = a.objectStore(this.stores.trades).getAll();
        p.onsuccess = async () => {
          let f = p.result;
          r.dateFrom && (f = f.filter((d) => d.date >= r.dateFrom)), r.dateTo && (f = f.filter((d) => d.date <= r.dateTo)), r.model_type && (f = f.filter((d) => d[Q.MODEL_TYPE] === r.model_type)), r.session && (f = f.filter((d) => d[Q.SESSION] === r.session)), r.direction && (f = f.filter((d) => d[Q.DIRECTION] === r.direction)), r.win_loss && (f = f.filter((d) => d[Q.WIN_LOSS] === r.win_loss)), r.market && (f = f.filter((d) => d[Q.MARKET] === r.market)), r.min_r_multiple !== void 0 && (f = f.filter((d) => d[Q.R_MULTIPLE] !== void 0 && d[Q.R_MULTIPLE] >= r.min_r_multiple)), r.max_r_multiple !== void 0 && (f = f.filter((d) => d[Q.R_MULTIPLE] !== void 0 && d[Q.R_MULTIPLE] <= r.max_r_multiple)), r.min_pattern_quality !== void 0 && (f = f.filter((d) => d[Q.PATTERN_QUALITY_RATING] !== void 0 && d[Q.PATTERN_QUALITY_RATING] >= r.min_pattern_quality)), r.max_pattern_quality !== void 0 && (f = f.filter((d) => d[Q.PATTERN_QUALITY_RATING] !== void 0 && d[Q.PATTERN_QUALITY_RATING] <= r.max_pattern_quality));
          const l = [];
          for (const d of f) {
            const h = {
              trade: d
            }, m = a.objectStore(this.stores.fvg_details).index("trade_id").get(d.id);
            await new Promise(($) => {
              m.onsuccess = () => {
                m.result && (h.fvg_details = m.result), $();
              }, m.onerror = () => $();
            });
            const w = a.objectStore(this.stores.setups).index("trade_id").get(d.id);
            await new Promise(($) => {
              w.onsuccess = () => {
                w.result && (h.setup = w.result), $();
              }, w.onerror = () => $();
            });
            const z = a.objectStore(this.stores.analysis).index("trade_id").get(d.id);
            await new Promise(($) => {
              z.onsuccess = () => {
                z.result && (h.analysis = z.result), $();
              }, z.onerror = () => $();
            }), l.push(h);
          }
          n(l);
        }, p.onerror = (f) => {
          console.error("Error filtering trades:", f), s(new Error("Failed to filter trades"));
        };
      });
    } catch (t) {
      throw console.error("Error in filterTrades:", t), new Error("Failed to filter trades");
    }
  }
  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades() {
    try {
      return await this.filterTrades({});
    } catch (r) {
      return console.error("Error in getAllTrades:", r), [];
    }
  }
  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (w) => {
          console.error("Transaction error:", w), s(new Error("Failed to delete trade"));
        };
        const f = a.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));
        f.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const h = a.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));
        h.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const m = a.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));
        m.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const C = a.objectStore(this.stores.trades).delete(r);
        a.oncomplete = () => {
          n();
        }, C.onerror = (w) => {
          console.error("Error deleting trade:", w), s(new Error("Failed to delete trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in deleteTrade:", t), new Error("Failed to delete trade");
    }
  }
  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(r, t) {
    try {
      const n = await this.initDB();
      return new Promise((s, a) => {
        const i = n.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        i.onerror = (d) => {
          console.error("Transaction error:", d), a(new Error("Failed to update trade"));
        };
        const p = i.objectStore(this.stores.trades), f = {
          ...t.trade,
          id: r,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, l = p.put(f);
        l.onsuccess = () => {
          if (t.fvg_details) {
            const d = i.objectStore(this.stores.fvg_details), h = {
              ...t.fvg_details,
              trade_id: r
            };
            d.put(h);
          }
          if (t.setup) {
            const d = i.objectStore(this.stores.setups), h = {
              ...t.setup,
              trade_id: r
            };
            d.put(h);
          }
          if (t.analysis) {
            const d = i.objectStore(this.stores.analysis), h = {
              ...t.analysis,
              trade_id: r
            };
            d.put(h);
          }
        }, i.oncomplete = () => {
          s();
        }, l.onerror = (d) => {
          console.error("Error updating trade:", d), a(new Error("Failed to update trade"));
        };
      });
    } catch (n) {
      throw console.error("Error in updateTradeWithDetails:", n), new Error("Failed to update trade");
    }
  }
}
const kt = new oc(), bl = kt, yl = kt;
export {
  Cc as AppErrorBoundary,
  Ze as Badge,
  de as Button,
  Fn as Card,
  Dc as DashboardSection,
  $c as DashboardTemplate,
  Pc as DataCard,
  yc as DualTimeDisplay,
  Qr as EmptyState,
  Tc as EnhancedFormField,
  Zn as ErrorBoundary,
  qc as F1Container,
  Bc as F1Form,
  Hc as F1FormField,
  Fc as F1Header,
  Ic as FeatureErrorBoundary,
  kc as FormField,
  _c as HierarchicalSessionSelector,
  je as Input,
  wc as LoadingCell,
  Ao as LoadingPlaceholder,
  Sc as LoadingSpinner,
  P as MacroPeriodType,
  Rc as Modal,
  uo as OrderSide,
  po as OrderStatus,
  lo as OrderType,
  Me as SETUP_ELEMENTS,
  $e as Select,
  vc as SelectDropdown,
  X as SessionType,
  ie as SessionUtils,
  Oc as SetupBuilder,
  Nc as SortableTable,
  cc as StatusIndicator,
  j as TRADE_COLUMN_IDS,
  jc as TabPanel,
  Lc as Table,
  lc as Tag,
  Nt as ThemeContext,
  ol as ThemeProvider,
  fo as TimeInForce,
  dc as TimePicker,
  Ac as TradeAnalysis,
  ao as TradeDirection,
  zc as TradeMetrics,
  co as TradeStatus,
  Mc as TradeTable,
  Ri as TradeTableFilters,
  Ti as TradeTableRow,
  It as UnifiedErrorBoundary,
  ac as VALID_TRADING_MODELS,
  T as baseColors,
  Sr as borderRadius,
  wr as breakpoints,
  ml as captureError,
  uc as convertLocalToNY,
  Fe as convertNYToLocal,
  Be as convertSessionToDualTime,
  sl as createSelector,
  nl as createStoreContext,
  B as darkModeColors,
  Za as darkTheme,
  pl as debounce,
  Et as f1Theme,
  vr as fontFamilies,
  me as fontSizes,
  br as fontWeights,
  al as formatCurrency,
  ll as formatDate,
  cl as formatPercentage,
  ot as formatTime,
  hc as formatTimeForDesktop,
  dn as formatTimeForMobile,
  an as formatTimeInterval,
  ul as generateId,
  hi as getCompactTradeTableColumns,
  Xe as getCurrentDualTime,
  pc as getCurrentNYMinutes,
  fc as getCurrentNYTime,
  mc as getNextSessionInfo,
  xi as getPerformanceTradeTableColumns,
  rl as getProfitLossColors,
  Zc as getProfitLossSize,
  el as getProfitLossVariant,
  xc as getSessionStatus,
  cn as getTimeRemainingInWindow,
  Ee as getTimeUntilNYTime,
  fr as getTimezoneAbbreviation,
  mi as getTradeTableColumns,
  er as getUserTimezone,
  gl as initMonitoring,
  ln as isCurrentTimeInNYWindow,
  ee as lightModeColors,
  Ja as lightTheme,
  yr as lineHeights,
  gc as minutesToTime,
  il as persistState,
  Xc as profitLossBaseStyles,
  Xa as profitLossColors,
  Jc as profitLossLoadingStyles,
  Qa as profitLossSizes,
  hl as setUser,
  Cr as shadows,
  Ec as sortFunctions,
  re as spacing,
  xl as startTransaction,
  bc as testTimezoneConversion,
  fl as throttle,
  Te as timeToMinutes,
  yl as tradeStorage,
  bl as tradeStorageService,
  Ir as transitions,
  dl as truncateText,
  Uc as useAsyncData,
  Qc as useDataFormatting,
  Kc as useDataSection,
  Yc as useDebounce,
  Vc as useErrorHandler,
  is as useFormField,
  Wa as useLoadingState,
  bt as useLocalStorage,
  Wc as usePagination,
  Gc as useProfitLossFormatting,
  ei as useSessionSelection,
  ms as useSortableTable,
  tl as useTheme,
  ss as validationRules,
  jr as zIndex
};
