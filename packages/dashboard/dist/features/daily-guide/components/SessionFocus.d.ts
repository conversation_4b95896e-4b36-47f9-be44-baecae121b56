/**
 * Enhanced Session Focus Component
 *
 * Advanced ICT session intelligence using real trading data from spreadsheet import.
 * Provides sophisticated PD Array and model-specific analysis with ADHD-optimized format.
 */
import React from 'react';
export interface SessionFocusProps {
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * Session Focus Component
 */
export declare const SessionFocus: React.FC<SessionFocusProps>;
//# sourceMappingURL=SessionFocus.d.ts.map