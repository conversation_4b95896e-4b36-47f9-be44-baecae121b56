/**
 * PD Array Intelligence Hook
 *
 * Analyzes real trading data to track active FVGs, RD levels, liquidity targets,
 * and parent PD arrays with performance statistics and recommendations.
 */
export interface PDArrayLevel {
    type: 'FVG' | 'NWOG' | 'NDOG' | 'RD' | 'Liquidity';
    level: string;
    timeframe: string;
    age: string;
    isActive: boolean;
    performance: {
        totalTrades: number;
        winRate: number;
        avgRMultiple: number;
        successRate: number;
    };
    recommendation: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
}
export interface PDArraySummary {
    totalActiveLevels: number;
    bestPerformingType: string;
    overallSuccessRate: number;
    avgRMultiple: number;
    keyInsights: string[];
}
export interface PDArrayIntelligence {
    activePDArrays: PDArrayLevel[];
    summary: PDArraySummary;
    lastUpdated: string;
}
/**
 * PD Array Intelligence Hook
 */
export declare const usePDArrayIntelligence: () => {
    pdArrayIntelligence: PDArrayIntelligence;
    isLoading: boolean;
    error: string;
    refresh: () => void;
};
//# sourceMappingURL=usePDArrayIntelligence.d.ts.map