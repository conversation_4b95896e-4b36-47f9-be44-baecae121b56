import { DailyGuideState } from '../types';
export declare const selectDailyGuideData: (state: DailyGuideState) => import("../types").DailyGuideData;
export declare const selectMarketOverview: (state: DailyGuideState) => import("../types").MarketOverview;
export declare const selectTradingPlan: (state: DailyGuideState) => import("../types").TradingPlan;
export declare const selectKeyPriceLevels: (state: DailyGuideState) => import("../types").KeyPriceLevel[];
export declare const selectWatchlist: (state: DailyGuideState) => import("../types").WatchlistItem[];
export declare const selectMarketNews: (state: DailyGuideState) => import("../types").MarketNewsItem[];
export declare const selectIsLoading: (state: DailyGuideState) => boolean;
export declare const selectError: (state: DailyGuideState) => string;
export declare const selectSelectedDate: (state: DailyGuideState) => string;
export declare const selectTradingPlanItems: any;
export declare const selectCompletedTradingPlanItems: any;
export declare const selectIncompleteTradingPlanItems: any;
export declare const selectTradingPlanCompletion: any;
export declare const selectTradingPlanItemsByPriority: any;
export declare const selectMarketIndices: any;
export declare const selectPositiveIndices: any;
export declare const selectNegativeIndices: any;
export declare const selectMarketSentiment: any;
export declare const selectMarketSummary: any;
export declare const selectEconomicEvents: any;
export declare const selectHighImpactEconomicEvents: any;
export declare const selectKeyPriceLevelsBySymbol: any;
export declare const selectWatchlistBySymbol: any;
export declare const selectHighImpactMarketNews: any;
export declare const selectMarketNewsByDate: any;
export declare const selectHasData: any;
export declare const selectLastUpdated: any;
export declare const dailyGuideSelectors: {
    selectDailyGuideData: (state: DailyGuideState) => import("../types").DailyGuideData;
    selectMarketOverview: (state: DailyGuideState) => import("../types").MarketOverview;
    selectTradingPlan: (state: DailyGuideState) => import("../types").TradingPlan;
    selectKeyPriceLevels: (state: DailyGuideState) => import("../types").KeyPriceLevel[];
    selectWatchlist: (state: DailyGuideState) => import("../types").WatchlistItem[];
    selectMarketNews: (state: DailyGuideState) => import("../types").MarketNewsItem[];
    selectIsLoading: (state: DailyGuideState) => boolean;
    selectError: (state: DailyGuideState) => string;
    selectSelectedDate: (state: DailyGuideState) => string;
    selectTradingPlanItems: any;
    selectCompletedTradingPlanItems: any;
    selectIncompleteTradingPlanItems: any;
    selectTradingPlanCompletion: any;
    selectTradingPlanItemsByPriority: any;
    selectMarketIndices: any;
    selectPositiveIndices: any;
    selectNegativeIndices: any;
    selectMarketSentiment: any;
    selectMarketSummary: any;
    selectEconomicEvents: any;
    selectHighImpactEconomicEvents: any;
    selectKeyPriceLevelsBySymbol: any;
    selectWatchlistBySymbol: any;
    selectHighImpactMarketNews: any;
    selectMarketNewsByDate: any;
    selectHasData: any;
    selectLastUpdated: any;
};
//# sourceMappingURL=dailyGuideSelectors.d.ts.map