{"version": 3, "file": "useSessionAnalytics.js", "sourceRoot": "", "sources": ["../../../../src/features/daily-guide/hooks/useSessionAnalytics.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAqB,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAgCxF;;GAEG;AACH,MAAM,iBAAiB,GAAG,GAAW,EAAE;IACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,KAAK,CAAC,CAAC;IAC9D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe;IAC3D,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACxB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,eAAe,GAAG,CAAC,IAAY,EAAU,EAAE;IAC/C,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC;QAAE,OAAO,YAAY,CAAC;IAC/C,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;QAAE,OAAO,SAAS,CAAC;IAC7C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;QAAE,OAAO,SAAS,CAAC;IAC9C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;QAAE,OAAO,UAAU,CAAC;IAC/C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;QAAE,OAAO,aAAa,CAAC;IAClD,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,mBAAmB,GAAG,CAAC,OAAe,EAAE,UAAkB,EAAqC,EAAE;IACrG,IAAI,UAAU,GAAG,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,kBAAkB;IACxD,IAAI,OAAO,IAAI,EAAE;QAAE,OAAO,WAAW,CAAC;IACtC,IAAI,OAAO,IAAI,EAAE;QAAE,OAAO,MAAM,CAAC;IACjC,IAAI,OAAO,IAAI,EAAE;QAAE,OAAO,SAAS,CAAC;IACpC,IAAI,OAAO,IAAI,EAAE;QAAE,OAAO,MAAM,CAAC;IACjC,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG,CAAC,WAA8C,EAAkD,EAAE;IAChI,QAAQ,WAAW,EAAE,CAAC;QACpB,KAAK,WAAW,CAAC,CAAC,OAAO,MAAM,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC;QAC7B,KAAK,SAAS,CAAC,CAAC,OAAO,QAAQ,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC;QAC1B,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;QAC7B,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC3B,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAAG,EAAE;IACtC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAC3D,SAAS,CAAC,SAAS,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,GAAG,CAAC,CAAC;gBACnE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YACxC,CAAC;oBAAS,CAAC;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,8BAA8B;IAC9B,MAAM,SAAS,GAAqB,OAAO,CAAC,GAAG,EAAE;QAC/C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO;gBACL,kBAAkB,EAAE,EAAE;gBACtB,qBAAqB,EAAE;oBACrB,WAAW,EAAE,iBAAiB,EAAE;oBAChC,YAAY,EAAE,eAAe,CAAC,iBAAiB,EAAE,CAAC;oBAClD,cAAc,EAAE,QAAQ;oBACxB,OAAO,EAAE,CAAC;oBACV,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,CAAC,uCAAuC,CAAC;iBACvD;gBACD,mBAAmB,EAAE,EAAE;gBACvB,oBAAoB,EAAE,EAAE;gBACxB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,MAAM,YAAY,GAA4C,EAAE,CAAC;QAEjE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC1B,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,kBAAkB,GAAyB,EAAE,CAAC;QAEpD,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC;YACrC,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5C,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC;gBACxC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM;gBACvF,CAAC,CAAC,CAAC,CAAC;YACN,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEpF,gCAAgC;YAChC,MAAM,WAAW,GAAgC,EAAE,CAAC;YACpD,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,SAAS,CAAC;gBAClD,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACzD,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAEnD,kBAAkB,CAAC,IAAI,CAAC;gBACtB,IAAI;gBACJ,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC;gBAC5B,OAAO;gBACP,WAAW,EAAE,UAAU,CAAC,MAAM;gBAC9B,YAAY;gBACZ,QAAQ;gBACR,SAAS;gBACT,WAAW,EAAE,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC;aAC7D,CAAC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QACxC,MAAM,cAAc,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAEvD,uCAAuC;QACvC,MAAM,iBAAiB,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAC1D,MAAM,aAAa,GAAyD,EAAE,CAAC;QAE/E,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAChC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,SAAS,CAAC;YAClD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YAC/C,CAAC;YACD,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;gBACnC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;aAC7C,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,oBAAoB;aAC7D,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;aAClE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAE3B,wBAAwB;QACxB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,cAAc,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;YAC/C,WAAW,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC3E,WAAW,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;YACjD,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YAC3F,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACjD,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,qBAAqB,GAAiC;YAC1D,WAAW;YACX,YAAY,EAAE,eAAe,CAAC,WAAW,CAAC;YAC1C,cAAc,EAAE,sBAAsB,CAAC,cAAc,CAAC,WAAW,CAAC;YAClE,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,UAAU;YACV,SAAS,EAAE,cAAc,CAAC,WAAW,KAAK,WAAW,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC7F,cAAc,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;YACvE,WAAW;SACZ,CAAC;QAEF,uCAAuC;QACvC,MAAM,mBAAmB,GAAG,kBAAkB;aAC3C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,kCAAkC;aAClE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7E,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE5E,OAAO;YACL,kBAAkB;YAClB,qBAAqB;YACrB,mBAAmB;YACnB,oBAAoB;YACpB,mBAAmB,EAAE,MAAM,CAAC,MAAM;YAClC,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,OAAO;QACL,SAAS;QACT,SAAS;QACT,KAAK;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,mBAAmB;YACnB,SAAS,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}