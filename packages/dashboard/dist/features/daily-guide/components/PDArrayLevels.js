import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayIntelligence } from '../hooks/usePDArrayIntelligence';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: 24px;
`;
const SectionTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const PDArrayCard = styled.div `
  background: ${({ arrayType, isActive }) => {
    if (!isActive)
        return 'rgba(255, 255, 255, 0.03)';
    switch (arrayType) {
        case 'FVG': return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
        case 'NWOG': return 'linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1))';
        case 'NDOG': return 'linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1))';
        case 'RD': return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
        case 'Liquidity': return 'linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.1))';
        default: return 'rgba(255, 255, 255, 0.05)';
    }
}};
  border: 1px solid ${({ arrayType, isActive }) => {
    if (!isActive)
        return 'rgba(255, 255, 255, 0.1)';
    switch (arrayType) {
        case 'FVG': return 'rgba(59, 130, 246, 0.4)';
        case 'NWOG': return 'rgba(16, 185, 129, 0.4)';
        case 'NDOG': return 'rgba(245, 158, 11, 0.4)';
        case 'RD': return 'rgba(220, 38, 38, 0.4)';
        case 'Liquidity': return 'rgba(168, 85, 247, 0.4)';
        default: return 'rgba(255, 255, 255, 0.2)';
    }
}};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
`;
const ArrayHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;
const ArrayType = styled.div `
  font-size: 16px;
  font-weight: 700;
  color: ${({ arrayType }) => {
    switch (arrayType) {
        case 'FVG': return '#3b82f6';
        case 'NWOG': return '#10b981';
        case 'NDOG': return '#f59e0b';
        case 'RD': return '#dc2626';
        case 'Liquidity': return '#a855f7';
        default: return '#ffffff';
    }
}};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ArrayStatus = styled.div `
  background: ${({ isActive }) => isActive ? '#10b981' : '#6b7280'};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;
const LevelInfo = styled.div `
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 12px 0;
`;
const LevelDetail = styled.div `
  text-align: center;
`;
const LevelValue = styled.div `
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
`;
const LevelLabel = styled.div `
  font-size: 10px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
`;
const PerformanceStats = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
`;
const StatsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
`;
const StatItem = styled.div `
  text-align: center;
`;
const StatValue = styled.div `
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
`;
const StatLabel = styled.div `
  font-size: 9px;
  color: #9ca3af;
  text-transform: uppercase;
`;
const PriorityBadge = styled.div `
  background: ${({ priority }) => {
    switch (priority) {
        case 'HIGH': return '#dc2626';
        case 'MEDIUM': return '#f59e0b';
        default: return '#6b7280';
    }
}};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
`;
const RecommendationText = styled.div `
  color: #d1d5db;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;
const EmptyState = styled.div `
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
`;
const EmptyIcon = styled.div `
  font-size: 48px;
  margin-bottom: 16px;
`;
const EmptyTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 8px;
`;
const EmptyMessage = styled.p `
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
`;
/**
 * PD Array Levels Component
 */
export const PDArrayLevels = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { pdArrayIntelligence, isLoading: intelligenceLoading, error: intelligenceError } = usePDArrayIntelligence();
    const loading = isLoading || intelligenceLoading;
    const displayError = error || intelligenceError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: "\uD83C\uDFAF PD Array Intelligence", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Analyzing PD Array levels and liquidity targets..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: "\uD83C\uDFAF PD Array Intelligence", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    // Empty state
    if (!pdArrayIntelligence || pdArrayIntelligence.activePDArrays.length === 0) {
        return (_jsx(Card, { title: "\uD83C\uDFAF PD Array Intelligence", children: _jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\uD83D\uDCCA" }), _jsx(EmptyTitle, { children: "No PD Array Data Available" }), _jsx(EmptyMessage, { children: "Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and liquidity targets. The system will analyze your historical performance with each PD Array type." })] }) }));
    }
    return (_jsx(Card, { title: "\uD83C\uDFAF PD Array Intelligence", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsxs("div", { children: [_jsxs(SectionTitle, { children: ["\uD83D\uDD25 Active PD Arrays", _jsx(PriorityBadge, { priority: "HIGH", children: "PRIORITY TARGETS" })] }), pdArrayIntelligence.activePDArrays.map((array, index) => (_jsxs(PDArrayCard, { arrayType: array.type, isActive: array.isActive, children: [_jsxs(ArrayHeader, { children: [_jsx(ArrayType, { arrayType: array.type, children: array.type }), _jsx(ArrayStatus, { isActive: array.isActive, children: array.isActive ? 'ACTIVE' : 'INACTIVE' })] }), _jsxs(LevelInfo, { children: [_jsxs(LevelDetail, { children: [_jsx(LevelValue, { children: array.level }), _jsx(LevelLabel, { children: "Level" })] }), _jsxs(LevelDetail, { children: [_jsx(LevelValue, { children: array.timeframe }), _jsx(LevelLabel, { children: "Timeframe" })] }), _jsxs(LevelDetail, { children: [_jsx(LevelValue, { children: array.age }), _jsx(LevelLabel, { children: "Age" })] })] }), _jsx(PerformanceStats, { children: _jsxs(StatsGrid, { children: [_jsxs(StatItem, { children: [_jsx(StatValue, { children: array.performance.totalTrades }), _jsx(StatLabel, { children: "Trades" })] }), _jsxs(StatItem, { children: [_jsxs(StatValue, { children: [array.performance.winRate.toFixed(0), "%"] }), _jsx(StatLabel, { children: "Win Rate" })] }), _jsxs(StatItem, { children: [_jsxs(StatValue, { children: [array.performance.avgRMultiple.toFixed(1), "R"] }), _jsx(StatLabel, { children: "Avg R" })] }), _jsxs(StatItem, { children: [_jsxs(StatValue, { children: [array.performance.successRate.toFixed(0), "%"] }), _jsx(StatLabel, { children: "Success Rate" })] })] }) }), _jsxs(RecommendationText, { children: [_jsx("strong", { children: "Strategy:" }), " ", array.recommendation] })] }, index)))] }), _jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA PD Array Performance Summary" }), _jsxs(PDArrayCard, { arrayType: "summary", isActive: true, children: [_jsxs(StatsGrid, { children: [_jsxs(StatItem, { children: [_jsx(StatValue, { children: pdArrayIntelligence.summary.totalActiveLevels }), _jsx(StatLabel, { children: "Active Levels" })] }), _jsxs(StatItem, { children: [_jsx(StatValue, { children: pdArrayIntelligence.summary.bestPerformingType }), _jsx(StatLabel, { children: "Best Type" })] }), _jsxs(StatItem, { children: [_jsxs(StatValue, { children: [pdArrayIntelligence.summary.overallSuccessRate.toFixed(0), "%"] }), _jsx(StatLabel, { children: "Overall Success" })] }), _jsxs(StatItem, { children: [_jsxs(StatValue, { children: [pdArrayIntelligence.summary.avgRMultiple.toFixed(1), "R"] }), _jsx(StatLabel, { children: "Avg R-Multiple" })] })] }), _jsxs(RecommendationText, { children: [_jsx("strong", { children: "Key Insights:" }), " ", pdArrayIntelligence.summary.keyInsights.join(' • ')] })] })] })] }) }));
};
//# sourceMappingURL=PDArrayLevels.js.map