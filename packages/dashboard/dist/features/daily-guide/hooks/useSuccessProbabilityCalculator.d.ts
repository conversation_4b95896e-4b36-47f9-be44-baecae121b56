/**
 * Success Probability Calculator Hook
 *
 * Real-time probability assessment based on current conditions:
 * - Multi-factor analysis combining model, session, quality, and market conditions
 * - Dynamic risk management recommendations
 * - Position sizing based on confluence factors
 */
import { ModelRecommendation } from './useModelSelectionEngine';
import { PatternQualityScore } from './usePatternQualityScoring';
export interface SuccessProbability {
    finalProbability: number;
    confidence: 'LOW' | 'MEDIUM' | 'HIGH';
    recommendation: 'AVOID' | 'REDUCE_SIZE' | 'STANDARD' | 'INCREASE_SIZE' | 'PRIORITIZE';
    expectedRMultiple: {
        min: number;
        max: number;
        average: number;
    };
    breakdown: {
        baseModelWinRate: number;
        sessionBonus: number;
        qualityBonus: number;
        newsImpact: number;
        volumeBonus: number;
        confluenceBonus: number;
    };
    riskManagement: {
        positionSizing: 'CONSERVATIVE' | 'STANDARD' | 'AGGRESSIVE';
        maxRiskPercent: number;
        stopLossMultiplier: number;
        takeProfitTargets: number[];
    };
}
export interface MarketContext {
    isNewsDay: boolean;
    newsImpact: 'LOW' | 'MEDIUM' | 'HIGH';
    volumeProfile: 'LOW' | 'NORMAL' | 'HIGH';
    marketHours: 'PRE_MARKET' | 'REGULAR' | 'AFTER_HOURS';
    dayOfWeek: 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY';
}
/**
 * Success Probability Calculator Hook
 */
export declare const useSuccessProbabilityCalculator: (modelRecommendation: ModelRecommendation, patternQuality: PatternQualityScore) => {
    successProbability: SuccessProbability;
    isLoading: boolean;
    error: string;
    refresh: () => void;
};
//# sourceMappingURL=useSuccessProbabilityCalculator.d.ts.map