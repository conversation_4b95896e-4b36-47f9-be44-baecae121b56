import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import MetricsPanel from '../components/MetricsPanel';
import PerformanceChart from '../components/PerformanceChart';
import RecentTradesTable from '../components/RecentTradesTable';
import SetupAnalysis from '../components/SetupAnalysis';
import TradeFormBasicFields from '../../trade-journal/components/trade-form/TradeFormBasicFields';
import styled from 'styled-components';
// Styled components for analytics layout
const AnalyticsContainer = styled.div `
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};

  @media (max-width: ${({ theme }) => theme.breakpoints?.lg || '1024px'}) {
    grid-template-columns: 1fr;
  }
`;
const ChartsSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const TradeFormSection = styled.div `
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  height: fit-content;
`;
const FormTitle = styled.h3 `
  margin: 0 0 ${({ theme }) => theme.spacing?.lg || '24px'} 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
`;
/**
 * Summary Tab Content
 */
const SummaryTabContent = ({ data, isLoading }) => {
    // Use completeTradeData for RecentTradesTable (correct nested format)
    // Sort by date descending and take first 5
    const recentTrades = data.completeTradeData
        ?.sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime())
        ?.slice(0, 5) || [];
    return (_jsxs(_Fragment, { children: [_jsx(MetricsPanel, { metrics: data.performanceMetrics, isLoading: isLoading }), _jsx(PerformanceChart, { data: data.chartData, isLoading: isLoading }), _jsx(RecentTradesTable, { trades: recentTrades, isLoading: isLoading })] }));
};
/**
 * Trades Tab Content
 */
const TradesTabContent = ({ data, isLoading }) => {
    // Use completeTradeData for RecentTradesTable (correct nested format)
    // Sort by date descending for full trade list
    const sortedTrades = data.completeTradeData?.sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()) || [];
    return _jsx(RecentTradesTable, { trades: sortedTrades, isLoading: isLoading });
};
/**
 * Setups Tab Content
 */
const SetupsTabContent = ({ data, isLoading }) => {
    return (_jsx(SetupAnalysis, { setupPerformance: data.setupPerformance, sessionPerformance: data.sessionPerformance, isLoading: isLoading }));
};
/**
 * Analytics Tab Content
 */
const AnalyticsTabContent = ({ data, isLoading, tradeFormValues, handleTradeFormChange, }) => {
    return (_jsxs(AnalyticsContainer, { children: [_jsxs(ChartsSection, { children: [_jsx(MetricsPanel, { metrics: data.performanceMetrics, isLoading: isLoading }), _jsx(PerformanceChart, { data: data.chartData, isLoading: isLoading }), _jsx(SetupAnalysis, { setupPerformance: data.setupPerformance, sessionPerformance: data.sessionPerformance, isLoading: isLoading })] }), _jsxs(TradeFormSection, { children: [_jsx(FormTitle, { children: "\uD83C\uDFCE\uFE0F Quick Trade Entry" }), tradeFormValues && handleTradeFormChange && (_jsx(TradeFormBasicFields, { formValues: tradeFormValues, handleChange: handleTradeFormChange, validationErrors: {} }))] })] }));
};
/**
 * Tab configuration with components and metadata
 */
export const DASHBOARD_TAB_CONFIG = {
    summary: {
        id: 'summary',
        title: 'Performance Summary',
        description: 'Overview of trading performance and key metrics',
        icon: '📊',
        component: SummaryTabContent,
        showInMobile: true,
        requiresData: true,
    },
    trades: {
        id: 'trades',
        title: 'Recent Trades',
        description: 'Complete list of recent trading activity',
        icon: '📋',
        component: TradesTabContent,
        showInMobile: true,
        requiresData: true,
    },
    setups: {
        id: 'setups',
        title: 'Setup Analysis',
        description: 'Performance breakdown by trading setups and sessions',
        icon: '🎯',
        component: SetupsTabContent,
        showInMobile: true,
        requiresData: true,
    },
    analytics: {
        id: 'analytics',
        title: 'Advanced Analytics',
        description: 'Comprehensive analytics with quick trade entry',
        icon: '📈',
        component: AnalyticsTabContent,
        showInMobile: false,
        requiresData: true,
    },
};
/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId) => {
    return DASHBOARD_TAB_CONFIG[tabId];
};
/**
 * Get all tab configurations
 */
export const getAllTabConfigs = () => {
    return Object.values(DASHBOARD_TAB_CONFIG);
};
/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = () => {
    return getAllTabConfigs().filter((config) => config.showInMobile);
};
/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = () => {
    return getAllTabConfigs().filter((config) => config.requiresData);
};
/**
 * Tab Content Renderer Component
 */
export const DashboardTabContentRenderer = (props) => {
    const { activeTab } = props;
    const config = getTabConfig(activeTab);
    if (!config) {
        return (_jsxs("div", { style: {
                padding: '48px',
                textAlign: 'center',
                color: '#ef4444',
            }, children: ["\u274C Unknown tab: ", activeTab] }));
    }
    const TabComponent = config.component;
    return (_jsx("div", { id: `dashboard-panel-${activeTab}`, role: "tabpanel", "aria-labelledby": `dashboard-tab-${activeTab}`, children: _jsx(TabComponent, { ...props }) }));
};
export default DashboardTabContentRenderer;
//# sourceMappingURL=dashboardTabConfig.js.map