{"version": 3, "file": "usePDArrayIntelligence.js", "sourceRoot": "", "sources": ["../../../../src/features/daily-guide/hooks/usePDArrayIntelligence.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAiCrE;;GAEG;AACH,MAAM,kBAAkB,GAAG,CAAC,KAAwB,EAAqD,EAAE;IACzG,MAAM,MAAM,GAAsD,EAAE,CAAC;IACrE,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IAC9D,MAAM,QAAQ,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;IAElD,gBAAgB;IAChB,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACtG,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,iBAAiB;IACjB,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QAC1G,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChF,CAAC;IAED,iBAAiB;IACjB,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACxG,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChF,CAAC;IAED,eAAe;IACf,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QAClG,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED,sBAAsB;IACtB,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3H,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,yBAAyB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,SAAiB,EAAU,EAAE;IACjD,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAE7D,IAAI,QAAQ,KAAK,CAAC;QAAE,OAAO,OAAO,CAAC;IACnC,IAAI,QAAQ,GAAG,CAAC;QAAE,OAAO,GAAG,QAAQ,OAAO,CAAC;IAC5C,IAAI,QAAQ,GAAG,EAAE;QAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;IAC9D,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC,SAAS,CAAC;AAC/C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,kBAAkB,GAAG,CAAC,KAAwB,EAAU,EAAE;IAC9D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,QAAQ,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC;IAErC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;QAAE,OAAO,OAAO,CAAC;IAC1E,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IACrE,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IACrE,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;QAAE,OAAO,KAAK,CAAC;IACzE,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QAAE,OAAO,IAAI,CAAC;IACtE,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QAAE,OAAO,IAAI,CAAC;IAEtE,2BAA2B;IAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;IACpC,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,aAAa;QAAE,OAAO,KAAK,CAAC;IACrE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,mBAAmB,GAAG,CAAC,KAAwB,EAAE,SAA+B,EAAU,EAAE;IAChG,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;IAC3C,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC;IAEzC,IAAI,CAAC,UAAU;QAAE,OAAO,KAAK,CAAC;IAE9B,qDAAqD;IACrD,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,KAAK;YACR,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;YAClE,OAAO,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3F,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,IAAI,CAAC;QACV,KAAK,WAAW;YACd,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/B;YACE,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,yBAAyB,GAAG,CAChC,SAA+B,EAC/B,MAA2B,EACE,EAAE;IAC/B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QACxC,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;IACvC,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;IACjF,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1E,MAAM,UAAU,GAAG,WAAW;SAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;SAC5B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;IAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1C,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpE,4DAA4D;IAC5D,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAC7E,CAAC,MAAM,CAAC;IACT,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjF,OAAO;QACL,WAAW;QACX,OAAO;QACP,YAAY;QACZ,WAAW;KACZ,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG,CAC7B,SAA+B,EAC/B,WAAwC,EACxC,GAAW,EACqD,EAAE;IAClE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;IAE3D,qCAAqC;IACrC,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,KAAK;YACR,OAAO,GAAG,kCAAkC,CAAC;YAC7C,MAAM;QACR,KAAK,MAAM;YACT,OAAO,GAAG,4CAA4C,CAAC;YACvD,MAAM;QACR,KAAK,MAAM;YACT,OAAO,GAAG,4CAA4C,CAAC;YACvD,MAAM;QACR,KAAK,IAAI;YACP,OAAO,GAAG,uCAAuC,CAAC;YAClD,MAAM;QACR,KAAK,WAAW;YACd,OAAO,GAAG,4CAA4C,CAAC;YACvD,MAAM;IACV,CAAC;IAED,8BAA8B;IAC9B,IAAI,QAAQ,GAA6B,QAAQ,CAAC;IAClD,IAAI,eAAe,GAAG,EAAE,CAAC;IAEzB,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;QACrB,IAAI,OAAO,IAAI,EAAE,IAAI,YAAY,IAAI,GAAG,EAAE,CAAC;YACzC,QAAQ,GAAG,MAAM,CAAC;YAClB,eAAe,GAAG,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1F,CAAC;aAAM,IAAI,OAAO,IAAI,EAAE,IAAI,YAAY,IAAI,GAAG,EAAE,CAAC;YAChD,QAAQ,GAAG,QAAQ,CAAC;YACpB,eAAe,GAAG,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,KAAK,CAAC;YACjB,eAAe,GAAG,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B,CAAC;QACvE,CAAC;IACH,CAAC;SAAM,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QAC3B,eAAe,GAAG,KAAK,WAAW,SAAS,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC;IAC1F,CAAC;SAAM,CAAC;QACN,eAAe,GAAG,uBAAuB,CAAC;QAC1C,QAAQ,GAAG,KAAK,CAAC;IACnB,CAAC;IAED,oBAAoB;IACpB,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACjD,eAAe,IAAI,gBAAgB,CAAC;IACtC,CAAC;SAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAChC,eAAe,IAAI,sBAAsB,CAAC;IAC5C,CAAC;IAED,OAAO;QACL,cAAc,EAAE,OAAO,GAAG,eAAe;QACzC,QAAQ;KACT,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,GAAG,EAAE;IACzC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAC3D,SAAS,CAAC,SAAS,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;gBACvE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YACxC,CAAC;oBAAS,CAAC;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,kCAAkC;IAClC,MAAM,mBAAmB,GAA+B,OAAO,CAAC,GAAG,EAAE;QACnE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qDAAqD;QACrD,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAEpD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,OAAO,SAAS,IAAI,aAAa,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,UAAU,GAAG,IAAI,GAAG,EAAoE,CAAC;QAE/F,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC3B,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrB,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC3E,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzB,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,MAAM,cAAc,GAAmB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YAC7F,MAAM,WAAW,GAAG,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC/C,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YAEpF,iEAAiE;YACjE,MAAM,QAAQ,GAAG,CACf,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;gBAC3D,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CACrD,CAAC;YAEF,OAAO;gBACL,IAAI;gBACJ,KAAK;gBACL,SAAS;gBACT,GAAG;gBACH,QAAQ;gBACR,WAAW;gBACX,cAAc;gBACd,QAAQ;aACT,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,MAAM,aAAa,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE5C,IAAI,SAAS,KAAK,SAAS;gBAAE,OAAO,SAAS,GAAG,SAAS,CAAC;YAC1D,OAAO,CAAC,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QAE5E,4BAA4B;QAC5B,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5E,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YACnE,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACxD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAC1E,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QAE5C,MAAM,kBAAkB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpD,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAExG,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC9C,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzG,wBAAwB;QACxB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAC5B,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,kCAAkC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACtH,CAAC;QAED,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QACvF,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;YAC1B,WAAW,CAAC,IAAI,CAAC,GAAG,iBAAiB,8BAA8B,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,kBAAkB,IAAI,EAAE,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC1D,CAAC;aAAM,IAAI,kBAAkB,GAAG,EAAE,EAAE,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,OAAO,GAAmB;YAC9B,iBAAiB;YACjB,kBAAkB,EAAE,QAAQ,CAAC,IAAI;YACjC,kBAAkB;YAClB,YAAY;YACZ,WAAW;SACZ,CAAC;QAEF,OAAO;YACL,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,kBAAkB;YAC/D,OAAO;YACP,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,OAAO;QACL,mBAAmB;QACnB,SAAS;QACT,KAAK;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,SAAS,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}