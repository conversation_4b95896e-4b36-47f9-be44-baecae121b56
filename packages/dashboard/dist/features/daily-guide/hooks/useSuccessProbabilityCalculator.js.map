{"version": 3, "file": "useSuccessProbabilityCalculator.js", "sourceRoot": "", "sources": ["../../../../src/features/daily-guide/hooks/useSuccessProbabilityCalculator.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAsCrE;;GAEG;AACH,MAAM,uBAAuB,GAAG,GAAkB,EAAE;IAClD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC5B,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAA+B,CAAC;IAE7I,yBAAyB;IACzB,IAAI,WAAyC,CAAC;IAC9C,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QACtD,WAAW,GAAG,YAAY,CAAC;IAC7B,CAAC;SAAM,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;QACtB,WAAW,GAAG,aAAa,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,WAAW,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,sFAAsF;IACtF,MAAM,SAAS,GAAG,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,WAAW,CAAC,CAAC,aAAa;IACpF,MAAM,UAAU,GAAgC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAE7E,wFAAwF;IACxF,MAAM,aAAa,GACjB,WAAW,KAAK,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;IAE7E,OAAO;QACL,SAAS;QACT,UAAU;QACV,aAAa;QACb,WAAW;QACX,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,qBAAqB,GAAG,CAC5B,WAAmB,EACnB,kBAA4E,EACpE,EAAE;IACV,MAAM,cAAc,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;IAC5E,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,WAAW,GAAG,CAAC;QAAE,OAAO,CAAC,CAAC;IAEhE,gDAAgD;IAChD,IAAI,cAAc,CAAC,OAAO,IAAI,EAAE;QAAE,OAAO,EAAE,CAAC;IAC5C,IAAI,cAAc,CAAC,OAAO,IAAI,EAAE;QAAE,OAAO,EAAE,CAAC;IAC5C,IAAI,cAAc,CAAC,OAAO,IAAI,EAAE;QAAE,OAAO,CAAC,CAAC;IAC3C,IAAI,cAAc,CAAC,OAAO,GAAG,EAAE;QAAE,OAAO,CAAC,EAAE,CAAC;IAC5C,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,qBAAqB,GAAG,CAAC,YAAoB,EAAU,EAAE;IAC7D,IAAI,YAAY,IAAI,GAAG;QAAE,OAAO,EAAE,CAAC;IACnC,IAAI,YAAY,IAAI,GAAG;QAAE,OAAO,EAAE,CAAC;IACnC,IAAI,YAAY,IAAI,GAAG;QAAE,OAAO,EAAE,CAAC;IACnC,IAAI,YAAY,IAAI,GAAG;QAAE,OAAO,CAAC,CAAC;IAClC,IAAI,YAAY,GAAG,GAAG;QAAE,OAAO,CAAC,EAAE,CAAC;IACnC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,mBAAmB,GAAG,CAAC,aAA4B,EAAE,KAAgB,EAAU,EAAE;IACrF,IAAI,aAAa,CAAC,UAAU,KAAK,KAAK;QAAE,OAAO,CAAC,CAAC;IAEjD,0EAA0E;IAC1E,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;QACxC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvC,CAAC;IAED,qBAAqB;IACrB,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,oBAAoB,GAAG,CAAC,aAA4B,EAAU,EAAE;IACpE,QAAQ,aAAa,CAAC,aAAa,EAAE,CAAC;QACpC,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACtB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,wBAAwB,GAAG,CAC/B,eAA0C,EAC1C,aAAqB,EACrB,YAAoB,EACZ,EAAE;IACV,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAE1B,IAAI,eAAe,KAAK,MAAM;QAAE,iBAAiB,EAAE,CAAC;IACpD,IAAI,aAAa,KAAK,WAAW,IAAI,aAAa,KAAK,aAAa;QAAE,iBAAiB,EAAE,CAAC;IAC1F,IAAI,YAAY,IAAI,EAAE;QAAE,iBAAiB,EAAE,CAAC;IAE5C,wCAAwC;IACxC,IAAI,iBAAiB,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IACtC,IAAI,iBAAiB,IAAI,CAAC;QAAE,OAAO,CAAC,CAAC;IACrC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG,CAC7B,WAAmB,EACnB,UAAqC,EACrC,YAAoB,EACkB,EAAE;IACxC,IAAI,cAAsE,CAAC;IAC3E,IAAI,cAAsB,CAAC;IAC3B,IAAI,kBAA0B,CAAC;IAC/B,IAAI,iBAA2B,CAAC;IAEhC,IAAI,WAAW,IAAI,EAAE,IAAI,UAAU,KAAK,MAAM,IAAI,YAAY,IAAI,GAAG,EAAE,CAAC;QACtE,cAAc,GAAG,YAAY,CAAC;QAC9B,cAAc,GAAG,GAAG,CAAC;QACrB,kBAAkB,GAAG,GAAG,CAAC;QACzB,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;SAAM,IAAI,WAAW,IAAI,EAAE,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;QACrD,cAAc,GAAG,UAAU,CAAC;QAC5B,cAAc,GAAG,GAAG,CAAC;QACrB,kBAAkB,GAAG,GAAG,CAAC;QACzB,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,cAAc,GAAG,cAAc,CAAC;QAChC,cAAc,GAAG,GAAG,CAAC;QACrB,kBAAkB,GAAG,GAAG,CAAC;QACzB,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,OAAO;QACL,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,iBAAiB;KAClB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAAG,CAC7C,mBAAwC,EACxC,cAAmC,EACnC,EAAE;IACF,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAC3D,SAAS,CAAC,SAAS,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;gBACzE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YACxC,CAAC;oBAAS,CAAC;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,gCAAgC;IAChC,MAAM,kBAAkB,GAAuB,OAAO,CAAC,GAAG,EAAE;QAC1D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO;gBACL,gBAAgB,EAAE,EAAE;gBACpB,UAAU,EAAE,KAAK;gBACjB,cAAc,EAAE,UAAU;gBAC1B,iBAAiB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;gBACvD,SAAS,EAAE;oBACT,gBAAgB,EAAE,EAAE;oBACpB,YAAY,EAAE,CAAC;oBACf,YAAY,EAAE,CAAC;oBACf,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;iBACnB;gBACD,cAAc,EAAE;oBACd,cAAc,EAAE,cAAc;oBAC9B,cAAc,EAAE,GAAG;oBACnB,kBAAkB,EAAE,GAAG;oBACvB,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;iBACnC;aACF,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,uBAAuB,EAAE,CAAC;QAChD,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QAE1C,gCAAgC;QAChC,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACpG,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC/C,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACzF,mBAAmB,CAAC,WAAW,CAAC;QAElC,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;YAChE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACnC,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;gBACrC,IAAI,CAAC,SAAS;oBAAE,OAAO,KAAK,CAAC;gBAC7B,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,OAAO,SAAS,KAAK,IAAI,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI;gBACJ,OAAO,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC9B,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3F,WAAW,EAAE,UAAU,CAAC,MAAM;aAC/B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,MAAM,YAAY,GAAG,qBAAqB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,qBAAqB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACtE,MAAM,UAAU,GAAG,mBAAmB,CAAC,aAAa,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC5F,MAAM,WAAW,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACxD,MAAM,eAAe,GAAG,wBAAwB,CAC9C,mBAAmB,CAAC,UAAU,EAC9B,cAAc,CAAC,MAAM,EACrB,YAAY,CACb,CAAC;QAEF,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CACxC,gBAAgB,GAAG,YAAY,GAAG,YAAY,GAAG,UAAU,GAAG,WAAW,GAAG,eAAe,EAC3F,EAAE,CACH,EAAE,EAAE,CAAC,CAAC;QAEP,6BAA6B;QAC7B,IAAI,UAAqC,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,YAAY,GAAG,eAAe,CAAC,CAAC;QAC3E,IAAI,UAAU,IAAI,EAAE,IAAI,mBAAmB,CAAC,UAAU,KAAK,MAAM;YAAE,UAAU,GAAG,MAAM,CAAC;aAClF,IAAI,UAAU,IAAI,EAAE;YAAE,UAAU,GAAG,QAAQ,CAAC;;YAC5C,UAAU,GAAG,KAAK,CAAC;QAExB,0BAA0B;QAC1B,IAAI,cAAoD,CAAC;QACzD,IAAI,gBAAgB,IAAI,EAAE,IAAI,UAAU,KAAK,MAAM;YAAE,cAAc,GAAG,YAAY,CAAC;aAC9E,IAAI,gBAAgB,IAAI,EAAE;YAAE,cAAc,GAAG,eAAe,CAAC;aAC7D,IAAI,gBAAgB,IAAI,EAAE;YAAE,cAAc,GAAG,UAAU,CAAC;aACxD,IAAI,gBAAgB,IAAI,EAAE;YAAE,cAAc,GAAG,aAAa,CAAC;;YAC3D,cAAc,GAAG,OAAO,CAAC;QAE9B,sCAAsC;QACtC,MAAM,eAAe,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;aAC5B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAE7D,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;QAEhF,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC;YAC9B,GAAG,EAAE,IAAI,GAAG,GAAG;YACf,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,2BAA2B;QAC3B,MAAM,cAAc,GAAG,sBAAsB,CAAC,gBAAgB,EAAE,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAEvG,OAAO;YACL,gBAAgB;YAChB,UAAU;YACV,cAAc;YACd,iBAAiB;YACjB,SAAS,EAAE;gBACT,gBAAgB;gBAChB,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,eAAe;aAChB;YACD,cAAc;SACf,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAE,cAAc,CAAC,CAAC,CAAC;IAElD,OAAO;QACL,kBAAkB;QAClB,SAAS;QACT,KAAK;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,SAAS,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}