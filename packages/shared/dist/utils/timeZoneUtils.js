/**
 * Time Zone Utilities
 *
 * Comprehensive time zone handling for international traders working with NY sessions.
 * Provides dual-timezone display, proper time formatting, and session time conversion.
 */
/**
 * Get user's local timezone
 */
export const getUserTimezone = () => {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
};
/**
 * Get timezone abbreviation
 */
export const getTimezoneAbbreviation = (timezone, date = new Date()) => {
    const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timezone,
        timeZoneName: 'short',
    });
    const parts = formatter.formatToParts(date);
    const timeZonePart = parts.find((part) => part.type === 'timeZoneName');
    return timeZonePart?.value || timezone;
};
/**
 * Convert NY time to user's local time
 */
export const convertNYToLocal = (nyTime, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    // Parse the NY time
    const [hours, minutes] = nyTime.split(':').map(Number);
    // Get today's date
    const today = new Date();
    // Create a date for today at the specified time
    const baseDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, 0);
    // The key insight: we need to create a date that when interpreted in NY timezone gives us our desired time
    // We'll use the inverse approach - create what would be the UTC time for this NY time
    // First, let's see what time it would be in NY if we interpret our base date as local time
    const baseInNY = new Date(baseDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const baseInUTC = new Date(baseDate.toLocaleString('en-US', { timeZone: 'UTC' }));
    // Calculate the difference
    const offsetMs = baseInUTC.getTime() - baseInNY.getTime();
    // Apply this offset to get the correct UTC time
    const correctUTCTime = new Date(baseDate.getTime() + offsetMs);
    // Now convert this UTC time to the user's timezone
    return correctUTCTime.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
};
/**
 * Convert local time to NY time
 */
export const convertLocalToNY = (localTime, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    // Create a date object for today with the local time
    const today = new Date();
    const localDateTime = new Date(`${today.toDateString()} ${localTime}:00`);
    // Convert to NY timezone
    return localDateTime.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
};
/**
 * Get current time in both NY and local timezone
 */
export const getCurrentDualTime = (userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    const now = new Date();
    const nyTime = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const localTime = now.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const nyTimezone = getTimezoneAbbreviation('America/New_York', now);
    const localTimezone = getTimezoneAbbreviation(timezone, now);
    return {
        nyTime,
        localTime,
        nyTimezone,
        localTimezone,
        formatted: `${nyTime} ${nyTimezone} | ${localTime} ${localTimezone}`,
    };
};
/**
 * Get current NY time as minutes since midnight (CRITICAL FOR SESSION CALCULATIONS)
 */
export const getCurrentNYMinutes = () => {
    const now = new Date();
    const nyTime = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    return timeToMinutes(nyTime);
};
/**
 * Get current NY time as Date object
 */
export const getCurrentNYTime = () => {
    const now = new Date();
    return new Date(now.toLocaleString('en-US', { timeZone: 'America/New_York' }));
};
/**
 * Format time interval in user-friendly format
 */
export const formatTimeInterval = (totalMinutes) => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    let formatted = '';
    if (hours > 0) {
        formatted = minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    else {
        formatted = `${minutes}m`;
    }
    return {
        totalMinutes,
        hours,
        minutes,
        formatted,
    };
};
/**
 * Calculate time until a specific NY time
 */
export const getTimeUntilNYTime = (targetNYTime) => {
    const now = new Date();
    const nyNow = new Date(now.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    // Create target time for today
    const [hours, minutes] = targetNYTime.split(':').map(Number);
    const targetTime = new Date(nyNow);
    targetTime.setHours(hours, minutes, 0, 0);
    // If target time has passed today, set it for tomorrow
    if (targetTime <= nyNow) {
        targetTime.setDate(targetTime.getDate() + 1);
    }
    const diffMs = targetTime.getTime() - nyNow.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return formatTimeInterval(diffMinutes);
};
/**
 * Calculate time remaining in a session window
 */
export const getTimeRemainingInWindow = (windowEndNY) => {
    return getTimeUntilNYTime(windowEndNY);
};
/**
 * Convert session time range to dual timezone
 */
export const convertSessionToDualTime = (nyStart, nyEnd, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    const localStart = convertNYToLocal(nyStart, timezone);
    const localEnd = convertNYToLocal(nyEnd, timezone);
    const localTimezoneAbbr = getTimezoneAbbreviation(timezone);
    return {
        nyStart,
        nyEnd,
        localStart,
        localEnd,
        formatted: `${nyStart}-${nyEnd} NY | ${localStart}-${localEnd} ${localTimezoneAbbr}`,
    };
};
/**
 * Check if current time is within a NY session window
 */
export const isCurrentTimeInNYWindow = (nyStart, nyEnd) => {
    const now = new Date();
    const nyNow = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const currentMinutes = timeToMinutes(nyNow);
    const startMinutes = timeToMinutes(nyStart);
    const endMinutes = timeToMinutes(nyEnd);
    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
};
/**
 * Convert time string to minutes since midnight
 */
export const timeToMinutes = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
};
/**
 * Convert minutes since midnight to time string
 */
export const minutesToTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};
/**
 * Get next session information
 */
export const getNextSessionInfo = (sessions) => {
    const now = new Date();
    const nyNow = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const currentMinutes = timeToMinutes(nyNow);
    // Find next session
    for (const session of sessions) {
        const sessionStartMinutes = timeToMinutes(session.nyStart);
        if (sessionStartMinutes > currentMinutes) {
            return {
                nextSession: session.name,
                timeUntil: getTimeUntilNYTime(session.nyStart),
                sessionTime: convertSessionToDualTime(session.nyStart, session.nyEnd),
            };
        }
    }
    // If no session today, return first session tomorrow
    if (sessions.length > 0) {
        const firstSession = sessions[0];
        const timeUntil = getTimeUntilNYTime(firstSession.nyStart);
        return {
            nextSession: firstSession.name,
            timeUntil,
            sessionTime: convertSessionToDualTime(firstSession.nyStart, firstSession.nyEnd),
        };
    }
    return null;
};
/**
 * Format time for mobile display (compact)
 */
export const formatTimeForMobile = (dualTime) => {
    const localFlag = dualTime.localTimezone.includes('GMT') ? '🇮🇪' : '🌍';
    return `${dualTime.localTime} ${localFlag} | ${dualTime.nyTime} 🇺🇸`;
};
/**
 * Format time for desktop display (full)
 */
export const formatTimeForDesktop = (dualTime) => {
    return `${dualTime.localTime} Local (${dualTime.localTimezone}) | ${dualTime.nyTime} NY (${dualTime.nyTimezone})`;
};
/**
 * Get session status with time context
 */
export const getSessionStatus = (sessionName, nyStart, nyEnd, userTimezone) => {
    const isActive = isCurrentTimeInNYWindow(nyStart, nyEnd);
    const sessionTime = convertSessionToDualTime(nyStart, nyEnd, userTimezone);
    if (isActive) {
        return {
            isActive: true,
            timeRemaining: getTimeRemainingInWindow(nyEnd),
            sessionTime,
            status: 'active',
        };
    }
    const timeUntilStart = getTimeUntilNYTime(nyStart);
    return {
        isActive: false,
        timeUntilStart,
        sessionTime,
        status: timeUntilStart.totalMinutes < 24 * 60 ? 'upcoming' : 'ended',
    };
};
/**
 * Test timezone conversion (for debugging)
 */
export const testTimezoneConversion = () => {
    console.log('🕐 TIMEZONE CONVERSION TEST:');
    console.log('09:00 NY →', convertNYToLocal('09:00', 'Europe/Dublin'));
    console.log('11:50 NY →', convertNYToLocal('11:50', 'Europe/Dublin'));
    console.log('15:15 NY →', convertNYToLocal('15:15', 'Europe/Dublin'));
    const dualTime = getCurrentDualTime('Europe/Dublin');
    console.log('Current dual time:', dualTime.formatted);
    // Expected results (summer time):
    // 09:00 NY → 14:00 Irish (5 hour difference)
    // 11:50 NY → 16:50 Irish (5 hour difference)
    // 15:15 NY → 20:15 Irish (5 hour difference)
};
//# sourceMappingURL=timeZoneUtils.js.map