/**
 * Dashboard Tab Configuration
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Centralized configuration for dashboard tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different dashboard views
 * - Clear content mapping
 */
import React from 'react';
import { DashboardTab } from './F1DashboardTabs';
export interface DashboardTabConfig {
    id: DashboardTab;
    title: string;
    description: string;
    icon: string;
    component: React.ComponentType<any>;
    showInMobile: boolean;
    requiresData: boolean;
}
export interface DashboardTabContentProps {
    /** Current active tab */
    activeTab: DashboardTab;
    /** Dashboard data */
    data: {
        trades: any[];
        performanceMetrics: any[];
        chartData: any[];
        setupPerformance: any[];
        sessionPerformance: any[];
        completeTradeData: any[];
    };
    /** Loading state */
    isLoading: boolean;
    /** Error state */
    error: string | null;
    /** Trade form values for analytics tab */
    tradeFormValues?: any;
    /** Trade form change handler */
    handleTradeFormChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
}
/**
 * Tab configuration with components and metadata
 */
export declare const DASHBOARD_TAB_CONFIG: Record<DashboardTab, DashboardTabConfig>;
/**
 * Get tab configuration by ID
 */
export declare const getTabConfig: (tabId: DashboardTab) => DashboardTabConfig;
/**
 * Get all tab configurations
 */
export declare const getAllTabConfigs: () => DashboardTabConfig[];
/**
 * Get mobile-friendly tabs
 */
export declare const getMobileTabConfigs: () => DashboardTabConfig[];
/**
 * Get tabs that require data
 */
export declare const getDataRequiredTabConfigs: () => DashboardTabConfig[];
/**
 * Tab Content Renderer Component
 */
export declare const DashboardTabContentRenderer: React.FC<DashboardTabContentProps>;
export default DashboardTabContentRenderer;
//# sourceMappingURL=dashboardTabConfig.d.ts.map