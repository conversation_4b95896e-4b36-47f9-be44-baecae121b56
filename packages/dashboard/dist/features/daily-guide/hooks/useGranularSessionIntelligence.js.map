{"version": 3, "file": "useGranularSessionIntelligence.js", "sourceRoot": "", "sources": ["../../../../src/features/daily-guide/hooks/useGranularSessionIntelligence.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAuDrE;;GAEG;AACH,MAAM,eAAe,GAAG;IACtB,OAAO,EAAE;QACP,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,+BAA+B,EAAE;QACpG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,WAAW,EAAE,4BAA4B,EAAE;QACpG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,4BAA4B,EAAE;QACtG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,WAAW,EAAE,yBAAyB,EAAE;KACrG;IACD,WAAW,EAAE;QACX,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,WAAW,EAAE,yBAAyB,EAAE;QACjG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,qBAAqB,EAAE,WAAW,EAAE,8BAA8B,EAAE;QAC3G,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,sBAAsB,EAAE,WAAW,EAAE,sBAAsB,EAAE;QACpG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,sBAAsB,EAAE,WAAW,EAAE,8BAA8B,EAAE;KAC7G;IACD,GAAG,EAAE;QACH,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,WAAW,EAAE,wBAAwB,EAAE;QACjG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,sBAAsB,EAAE;KAC7F;IACD,UAAU,EAAE;QACV,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,wBAAwB,EAAE;QAClG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,mBAAmB,EAAE;QAC7F,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,mBAAmB,EAAE;KACzF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAG,CAAC,OAAe,EAAU,EAAE;IAChD,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACxD,OAAO,KAAK,GAAG,EAAE,GAAG,OAAO,CAAC;AAC9B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,GAAW,EAAE;IAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,OAAO,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;AACzG,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,WAAmB,EAAE,MAAkB,EAAW,EAAE;IAC1E,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;IAC3C,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAEtC,OAAO,OAAO,IAAI,KAAK,IAAI,OAAO,IAAI,GAAG,CAAC;AAC5C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAG,CAAC,WAAmB,EAAE,MAAkB,EAAU,EAAE;IAC3E,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAEtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,KAAwB,EAAiB,EAAE;IACjE,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;IACpC,IAAI,OAAO;QAAE,OAAO,OAAO,CAAC;IAE5B,iCAAiC;IACjC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC;IACzC,IAAI,CAAC,SAAS;QAAE,OAAO,IAAI,CAAC;IAE5B,MAAM,WAAW,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;IAE7C,sBAAsB;IACtB,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QACnF,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QAC1F,OAAO,aAAa,CAAC;IACvB,CAAC;SAAM,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QAC1F,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QAC1F,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CACrB,WAA2C,EAC3C,WAAmB,EACnB,MAA2B,EACV,EAAE;IACnB,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;IAC7C,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAC1C,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QACtC,OAAO,OAAO,KAAK,WAAW,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,cAAc,GAAoB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QAC3D,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAChD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC;YACzC,IAAI,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAC7B,OAAO,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC;QACxC,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1E,MAAM,UAAU,GAAG,YAAY;aAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;aAC5B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1C,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpE,MAAM,QAAQ,GAAG,YAAY;aAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;aAClC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAEpC,6BAA6B;QAC7B,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;QAChF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC;QAE9E,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC7C,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChG,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3C,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,IAAI,eAAe,GAAqB,IAAI,CAAC;QAC7C,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACxD,eAAe,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;QACxE,CAAC;aAAM,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACpC,eAAe,GAAG,SAAS,CAAC;QAC9B,CAAC;aAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,eAAe,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,yCAAyC;QACzC,MAAM,SAAS,GAAG,OAAO,IAAI,EAAE,IAAI,WAAW,IAAI,CAAC,CAAC;QACpD,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAEvF,0BAA0B;QAC1B,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,SAAS,EAAE,CAAC;YACd,cAAc,GAAG,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;QACpG,CAAC;aAAM,IAAI,OAAO,IAAI,EAAE,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YAC7C,cAAc,GAAG,sBAAsB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC;QACxE,CAAC;aAAM,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAC3B,cAAc,GAAG,+CAA+C,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,aAAa,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC,CAAC;QACtF,CAAC;QAED,OAAO;YACL,MAAM;YACN,WAAW,EAAE;gBACX,WAAW;gBACX,aAAa;gBACb,OAAO;gBACP,YAAY;gBACZ,QAAQ;aACT;YACD,eAAe;YACf,SAAS;YACT,SAAS;YACT,cAAc;SACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,wCAAwC;IACxC,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC;IACzC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;IACnF,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1E,MAAM,UAAU,GAAG,aAAa;SAC7B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;SAC5B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;IAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1C,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpE,8BAA8B;IAC9B,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAClF,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACvC,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACxE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IAElB,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CACxC,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAC1E,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IAElB,iBAAiB;IACjB,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAChF,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvF,mBAAmB;IACnB,MAAM,cAAc,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;IAClD,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,IAAI,IAAI,CAAC;IAEtF,MAAM,QAAQ,GAAG,aAAa,KAAK,IAAI,CAAC;IAExC,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,QAAQ,IAAI,aAAa,EAAE,CAAC;QAC9B,MAAM,qBAAqB,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;QACnF,cAAc,GAAG,qBAAqB,EAAE,cAAc,IAAI,2BAA2B,CAAC;IACxF,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;QACpE,cAAc,GAAG,gBAAgB,UAAU,CAAC,KAAK,OAAO,UAAU,UAAU,CAAC;IAC/E,CAAC;SAAM,CAAC;QACN,cAAc,GAAG,oBAAoB,CAAC;IACxC,CAAC;IAED,OAAO;QACL,WAAW;QACX,WAAW;QACX,OAAO,EAAE,cAAc;QACvB,kBAAkB,EAAE;YAClB,WAAW;YACX,OAAO;YACP,YAAY;YACZ,UAAU;YACV,WAAW;SACZ;QACD,aAAa,EAAE;YACb,QAAQ;YACR,aAAa;YACb,aAAa;YACb,UAAU;YACV,cAAc;SACf;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,GAAG,EAAE;IACjD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAC3D,SAAS,CAAC,SAAS,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,GAAG,CAAC,CAAC;gBACtE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YACxC,CAAC;oBAAS,CAAC;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,uBAAuB;IACvB,MAAM,eAAe,GAAsB,OAAO,CAAC,GAAG,EAAE;QACtD,OAAO;YACL,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;YAC5C,cAAc,CAAC,aAAa,EAAE,aAAa,EAAE,MAAM,CAAC;YACpD,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;YACpC,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC;SACnD,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,iCAAiC;IACjC,MAAM,YAAY,GAAwB,OAAO,CAAC,GAAG,EAAE;QACrD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;QAElF,oBAAoB;QACpB,MAAM,cAAc,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,eAAe;aAChC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC;aACtC,IAAI,CAAC,CAAC,CAAC,EAAE;YACR,MAAM,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,OAAO,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;QAClE,CAAC,CAAC,IAAI,IAAI,CAAC;QAEb,mCAAmC;QACnC,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,YAAY,GAAwC,KAAK,CAAC;QAE9D,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;YACrF,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC;YAEhE,IAAI,aAAa,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,EAAE,CAAC;gBAC1E,iBAAiB,GAAG,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC;gBAC9D,YAAY,GAAG,iBAAiB,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC7D,CAAC;QACH,CAAC;aAAM,IAAI,WAAW,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;YAC9E,IAAI,WAAW,EAAE,CAAC;gBAChB,iBAAiB,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;gBAC7E,YAAY,GAAG,iBAAiB,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,qBAAqB,GAAG,EAAE,CAAC;QAC/B,IAAI,aAAa,EAAE,CAAC;YAClB,qBAAqB,GAAG,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC;QACrE,CAAC;aAAM,IAAI,WAAW,IAAI,iBAAiB,IAAI,EAAE,EAAE,CAAC;YAClD,qBAAqB,GAAG,eAAe,WAAW,CAAC,WAAW,OAAO,iBAAiB,UAAU,CAAC;QACnG,CAAC;aAAM,CAAC;YACN,qBAAqB,GAAG,iDAAiD,CAAC;QAC5E,CAAC;QAED,OAAO;YACL,WAAW;YACX,aAAa;YACb,WAAW;YACX,iBAAiB;YACjB,qBAAqB;YACrB,YAAY;SACb,CAAC;IACJ,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,OAAO;QACL,eAAe;QACf,YAAY;QACZ,SAAS;QACT,KAAK;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,SAAS,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}