/**
 * Trade Form Data Hook
 *
 * Custom hook for managing trade form state
 */
/**
 * Hook for managing trade form data
 * @param tradeId The ID of the trade to load (optional)
 * @param isEditMode Whether the form is in edit mode
 * @param isNewTrade Whether the form is for a new trade
 */
export declare function useTradeFormData(tradeId: string | undefined, isEditMode: boolean, isNewTrade: boolean): {
    formValues: TradeFormValues;
    setFormValues: import("react").Dispatch<any>;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    isLoading: boolean;
    setIsLoading: import("react").Dispatch<import("react").SetStateAction<boolean>>;
    error: string;
    setError: import("react").Dispatch<import("react").SetStateAction<string>>;
    success: string;
    setSuccess: import("react").Dispatch<import("react").SetStateAction<string>>;
    tradeData: any;
};
export type TradeFormDataHook = ReturnType<typeof useTradeFormData>;
//# sourceMappingURL=useTradeFormData.d.ts.map