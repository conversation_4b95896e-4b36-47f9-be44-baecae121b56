import { jsx as _jsx } from "react/jsx-runtime";
import { F1GuideContainer } from './F1GuideContainer';
import { DailyGuideProvider } from '../state/dailyGuideState';
/**
 * Daily Guide Component
 *
 * Simple wrapper that renders the container with context provider.
 * Follows the proven architecture pattern.
 */
export const DailyGuide = ({ className, initialTab, title }) => {
    return (_jsx(DailyGuideProvider, { children: _jsx(F1GuideContainer, { className: className, initialTab: initialTab, title: title }) }));
};
export default DailyGuide;
//# sourceMappingURL=DailyGuide.js.map