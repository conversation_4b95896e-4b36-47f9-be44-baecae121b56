/**
 * PD Array Intelligence Hook
 *
 * Analyzes real trading data to track active FVGs, RD levels, liquidity targets,
 * and parent PD arrays with performance statistics and recommendations.
 */
import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
/**
 * Extract PD Array information from trade data
 */
const extractPDArrayInfo = (trade) => {
    const arrays = [];
    const notes = trade.trade.notes?.toLowerCase() || '';
    const setup = trade.trade.setup?.toLowerCase() || '';
    const modelType = trade.trade.model_type?.toLowerCase() || '';
    const combined = `${notes} ${setup} ${modelType}`;
    // FVG detection
    if (combined.includes('fvg') || combined.includes('fair value gap') || combined.includes('imbalance')) {
        arrays.push({ type: 'FVG', details: `FVG from ${trade.trade.date}` });
    }
    // NWOG detection
    if (combined.includes('nwog') || combined.includes('new week opening') || combined.includes('weekly gap')) {
        arrays.push({ type: 'NWOG', details: `NWOG level from ${trade.trade.date}` });
    }
    // NDOG detection
    if (combined.includes('ndog') || combined.includes('new day opening') || combined.includes('daily gap')) {
        arrays.push({ type: 'NDOG', details: `NDOG level from ${trade.trade.date}` });
    }
    // RD detection
    if (combined.includes('rd') || combined.includes('reaction') || combined.includes('displacement')) {
        arrays.push({ type: 'RD', details: `RD level from ${trade.trade.date}` });
    }
    // Liquidity detection
    if (combined.includes('liquidity') || combined.includes('sweep') || combined.includes('raid') || combined.includes('hunt')) {
        arrays.push({ type: 'Liquidity', details: `Liquidity target from ${trade.trade.date}` });
    }
    return arrays;
};
/**
 * Calculate age of PD Array based on trade date
 */
const calculateAge = (tradeDate) => {
    const trade = new Date(tradeDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - trade.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 1)
        return '1 day';
    if (diffDays < 7)
        return `${diffDays} days`;
    if (diffDays < 30)
        return `${Math.floor(diffDays / 7)} weeks`;
    return `${Math.floor(diffDays / 30)} months`;
};
/**
 * Determine timeframe from trade data
 */
const determineTimeframe = (trade) => {
    const notes = trade.trade.notes?.toLowerCase() || '';
    const setup = trade.trade.setup?.toLowerCase() || '';
    const combined = `${notes} ${setup}`;
    if (combined.includes('daily') || combined.includes('1d'))
        return 'Daily';
    if (combined.includes('4h') || combined.includes('4hr'))
        return '4H';
    if (combined.includes('1h') || combined.includes('1hr'))
        return '1H';
    if (combined.includes('15m') || combined.includes('15min'))
        return '15M';
    if (combined.includes('5m') || combined.includes('5min'))
        return '5M';
    if (combined.includes('1m') || combined.includes('1min'))
        return '1M';
    // Default based on session
    const session = trade.trade.session;
    if (session === 'NY Open' || session === 'Lunch Macro')
        return '15M';
    return '5M';
};
/**
 * Generate level string from trade data
 */
const generateLevelString = (trade, arrayType) => {
    const entryPrice = trade.trade.entry_price;
    const exitPrice = trade.trade.exit_price;
    if (!entryPrice)
        return 'N/A';
    // Estimate level based on entry price and array type
    switch (arrayType) {
        case 'FVG':
            // FVG typically has a range
            const fvgRange = Math.abs((exitPrice || entryPrice) - entryPrice);
            return `${(entryPrice - fvgRange / 2).toFixed(0)}-${(entryPrice + fvgRange / 2).toFixed(0)}`;
        case 'NWOG':
        case 'NDOG':
        case 'RD':
        case 'Liquidity':
            return entryPrice.toFixed(0);
        default:
            return entryPrice.toFixed(0);
    }
};
/**
 * Analyze PD Array performance
 */
const analyzePDArrayPerformance = (arrayType, trades) => {
    const arrayTrades = trades.filter(trade => {
        const arrays = extractPDArrayInfo(trade);
        return arrays.some(arr => arr.type === arrayType);
    });
    const totalTrades = arrayTrades.length;
    const winningTrades = arrayTrades.filter(t => t.trade.win_loss === 'Win').length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const rMultiples = arrayTrades
        .map(t => t.trade.r_multiple)
        .filter((r) => r !== undefined && r !== null);
    const avgRMultiple = rMultiples.length > 0 ?
        rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
    // Success rate considers both wins and positive R-multiples
    const successfulTrades = arrayTrades.filter(t => t.trade.win_loss === 'Win' || (t.trade.r_multiple && t.trade.r_multiple > 0)).length;
    const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;
    return {
        totalTrades,
        winRate,
        avgRMultiple,
        successRate
    };
};
/**
 * Generate recommendations for PD Array
 */
const generateRecommendation = (arrayType, performance, age) => {
    const { winRate, avgRMultiple, totalTrades } = performance;
    // Base recommendations by array type
    let baseRec = '';
    switch (arrayType) {
        case 'FVG':
            baseRec = 'Target FVG fills with confluence';
            break;
        case 'NWOG':
            baseRec = 'High-probability parent PD array reactions';
            break;
        case 'NDOG':
            baseRec = 'Daily opening gap redelivery opportunities';
            break;
        case 'RD':
            baseRec = 'Reaction delivery continuation setups';
            break;
        case 'Liquidity':
            baseRec = 'Liquidity sweep and reversal opportunities';
            break;
    }
    // Adjust based on performance
    let priority = 'MEDIUM';
    let performanceNote = '';
    if (totalTrades >= 3) {
        if (winRate >= 70 && avgRMultiple >= 1.5) {
            priority = 'HIGH';
            performanceNote = ` (${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)}R avg)`;
        }
        else if (winRate >= 60 || avgRMultiple >= 1.2) {
            priority = 'MEDIUM';
            performanceNote = ` (${winRate.toFixed(0)}% win rate)`;
        }
        else {
            priority = 'LOW';
            performanceNote = ` (${winRate.toFixed(0)}% win rate - use caution)`;
        }
    }
    else if (totalTrades > 0) {
        performanceNote = ` (${totalTrades} trade${totalTrades > 1 ? 's' : ''} - limited data)`;
    }
    else {
        performanceNote = ' (no historical data)';
        priority = 'LOW';
    }
    // Age consideration
    if (age.includes('day') && !age.includes('days')) {
        performanceNote += ' - Fresh level';
    }
    else if (age.includes('week')) {
        performanceNote += ' - Established level';
    }
    return {
        recommendation: baseRec + performanceNote,
        priority
    };
};
/**
 * PD Array Intelligence Hook
 */
export const usePDArrayIntelligence = () => {
    const [trades, setTrades] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch trade data
    useEffect(() => {
        const fetchTrades = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const tradeData = await tradeStorageService.getAllTrades();
                setTrades(tradeData);
            }
            catch (err) {
                console.error('Error fetching trades for PD Array intelligence:', err);
                setError('Failed to load trade data');
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTrades();
    }, []);
    // Calculate PD Array intelligence
    const pdArrayIntelligence = useMemo(() => {
        if (trades.length === 0) {
            return null;
        }
        // Get recent trades (last 30 days) for active levels
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentTrades = trades.filter(trade => {
            const tradeDate = new Date(trade.trade.date);
            return tradeDate >= thirtyDaysAgo;
        });
        // Extract all unique PD Arrays from recent trades
        const pdArrayMap = new Map();
        recentTrades.forEach(trade => {
            const arrays = extractPDArrayInfo(trade);
            arrays.forEach(array => {
                const key = `${array.type}-${trade.trade.date}-${trade.trade.entry_price}`;
                if (!pdArrayMap.has(key)) {
                    pdArrayMap.set(key, { type: array.type, trade });
                }
            });
        });
        // Create PD Array levels
        const activePDArrays = Array.from(pdArrayMap.values()).map(({ type, trade }) => {
            const performance = analyzePDArrayPerformance(type, trades);
            const age = calculateAge(trade.trade.date);
            const timeframe = determineTimeframe(trade);
            const level = generateLevelString(trade, type);
            const { recommendation, priority } = generateRecommendation(type, performance, age);
            // Consider active if recent and has good performance or is fresh
            const isActive = ((performance.totalTrades >= 2 && performance.winRate >= 50) ||
                (performance.totalTrades < 2 && age.includes('day')));
            return {
                type,
                level,
                timeframe,
                age,
                isActive,
                performance,
                recommendation,
                priority
            };
        });
        // Sort by priority and performance
        activePDArrays.sort((a, b) => {
            const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
            const aPriority = priorityOrder[a.priority];
            const bPriority = priorityOrder[b.priority];
            if (aPriority !== bPriority)
                return bPriority - aPriority;
            return b.performance.winRate - a.performance.winRate;
        });
        // Calculate summary statistics
        const totalActiveLevels = activePDArrays.filter(arr => arr.isActive).length;
        // Find best performing type
        const typePerformance = ['FVG', 'NWOG', 'NDOG', 'RD', 'Liquidity'].map(type => {
            const typeArrays = activePDArrays.filter(arr => arr.type === type);
            const avgWinRate = typeArrays.length > 0 ?
                typeArrays.reduce((sum, arr) => sum + arr.performance.winRate, 0) / typeArrays.length : 0;
            return { type, avgWinRate, count: typeArrays.length };
        });
        const bestType = typePerformance.reduce((best, current) => current.avgWinRate > best.avgWinRate && current.count > 0 ? current : best, { type: 'N/A', avgWinRate: 0, count: 0 });
        const overallSuccessRate = activePDArrays.length > 0 ?
            activePDArrays.reduce((sum, arr) => sum + arr.performance.successRate, 0) / activePDArrays.length : 0;
        const avgRMultiple = activePDArrays.length > 0 ?
            activePDArrays.reduce((sum, arr) => sum + arr.performance.avgRMultiple, 0) / activePDArrays.length : 0;
        // Generate key insights
        const keyInsights = [];
        if (bestType.type !== 'N/A') {
            keyInsights.push(`${bestType.type} arrays show best performance (${bestType.avgWinRate.toFixed(0)}% avg win rate)`);
        }
        const highPriorityCount = activePDArrays.filter(arr => arr.priority === 'HIGH').length;
        if (highPriorityCount > 0) {
            keyInsights.push(`${highPriorityCount} high-priority levels active`);
        }
        if (overallSuccessRate >= 70) {
            keyInsights.push('Strong PD Array performance overall');
        }
        else if (overallSuccessRate < 50) {
            keyInsights.push('Focus on quality over quantity');
        }
        const summary = {
            totalActiveLevels,
            bestPerformingType: bestType.type,
            overallSuccessRate,
            avgRMultiple,
            keyInsights
        };
        return {
            activePDArrays: activePDArrays.slice(0, 10), // Limit to top 10
            summary,
            lastUpdated: new Date().toISOString()
        };
    }, [trades]);
    return {
        pdArrayIntelligence,
        isLoading,
        error,
        refresh: () => {
            setTrades([]);
        }
    };
};
//# sourceMappingURL=usePDArrayIntelligence.js.map