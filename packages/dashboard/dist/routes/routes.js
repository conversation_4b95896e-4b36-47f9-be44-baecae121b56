import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Application Routes
 *
 * This file defines the application routes using React Router.
 */
import { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import LoadingScreen from '../components/molecules/LoadingScreen';
// Lazy-loaded feature components for code splitting
const Dashboard = lazy(() => import('../features/trading-dashboard/TradingDashboard'));
const DailyGuide = lazy(() => import('../features/daily-guide/components/DailyGuide'));
const TradeJournal = lazy(() => import('../features/trade-journal/TradeJournal'));
const TradeAnalysis = lazy(() => import('../features/trade-analysis/TradeAnalysis'));
const TradeForm = lazy(() => import('../features/trade-journal/TradeForm'));
const Settings = lazy(() => import('../features/settings/Settings'));
const NotFound = lazy(() => import('../components/NotFound'));
/**
 * AppRoutes Component
 *
 * Defines the application routes using React Router.
 */
const AppRoutes = () => {
    return (_jsx(Suspense, { fallback: _jsx(LoadingScreen, {}), children: _jsxs(Routes, { children: [_jsxs(Route, { path: "/", element: _jsx(MainLayout, {}), children: [_jsx(Route, { index: true, element: _jsx(Dashboard, {}) }), _jsx(Route, { path: "daily-guide", element: _jsx(DailyGuide, {}) }), _jsx(Route, { path: "journal", element: _jsx(TradeJournal, {}) }), _jsx(Route, { path: "analysis", element: _jsx(TradeAnalysis, {}) }), _jsx(Route, { path: "trade/new", element: _jsx(TradeForm, {}) }), _jsx(Route, { path: "trade/:id", element: _jsx(TradeForm, {}) }), _jsx(Route, { path: "settings", element: _jsx(Settings, {}) }), _jsx(Route, { path: "*", element: _jsx(NotFound, {}) })] }), _jsx(Route, { path: "/dashboard", element: _jsx(Navigate, { to: "/", replace: true }) })] }) }));
};
export default AppRoutes;
//# sourceMappingURL=routes.js.map