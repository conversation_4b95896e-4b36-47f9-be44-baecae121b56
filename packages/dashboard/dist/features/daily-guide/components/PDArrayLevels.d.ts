/**
 * PD Array Levels Component
 *
 * Replaces generic Key Levels with sophisticated ICT PD Array intelligence.
 * Tracks active FVGs, RD levels, liquidity targets, and parent PD arrays using real trading data.
 */
import React from 'react';
export interface PDArrayLevelsProps {
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * PD Array Levels Component
 */
export declare const PDArrayLevels: React.FC<PDArrayLevelsProps>;
//# sourceMappingURL=PDArrayLevels.d.ts.map