/**
 * Enhanced Session Intelligence Hook
 *
 * Advanced ICT session analysis using real trading data from spreadsheet import.
 * Provides sophisticated timing, model selection, and PD Array insights.
 */
export interface ICTSessionPerformance {
    sessionName: string;
    sessionType: 'Pre-Market' | 'NY Open' | 'Lunch Macro' | 'MOC';
    timeRange: string;
    performance: {
        totalTrades: number;
        winningTrades: number;
        winRate: number;
        avgRMultiple: number;
        totalPnL: number;
        avgRisk: number;
    };
    modelPreference: {
        rdCont: {
            trades: number;
            winRate: number;
            avgR: number;
        };
        fvgRd: {
            trades: number;
            winRate: number;
            avgR: number;
        };
        recommendation: 'RD-Cont' | 'FVG-RD' | 'Either';
    };
    optimalWindows: {
        start: string;
        end: string;
        description: string;
        winRate: number;
        trades: number;
    }[];
    qualityThreshold: number;
    recommendations: string[];
}
export interface CurrentSessionStatus {
    currentTime: string;
    currentTimeFormatted: string;
    activeSession: ICTSessionPerformance | null;
    nextSession: ICTSessionPerformance | null;
    timeToNext: number;
    timeToNextFormatted: string;
    isOptimalWindow: boolean;
    currentRecommendation: string;
    urgency: 'LOW' | 'MEDIUM' | 'HIGH';
}
export interface EnhancedSessionIntelligence {
    sessions: ICTSessionPerformance[];
    currentStatus: CurrentSessionStatus;
    weeklyInsights: {
        bestSession: string;
        bestModel: 'RD-Cont' | 'FVG-RD';
        avgQuality: number;
        qualityThreshold: number;
        recommendations: string[];
    };
}
/**
 * Enhanced Session Intelligence Hook
 */
export declare const useEnhancedSessionIntelligence: () => {
    intelligence: EnhancedSessionIntelligence;
    isLoading: boolean;
    error: string;
    refresh: () => void;
};
//# sourceMappingURL=useEnhancedSessionIntelligence.d.ts.map