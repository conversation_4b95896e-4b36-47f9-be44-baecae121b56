/**
 * Trade Journal API Implementation
 *
 * Concrete implementation of the TradeJournalApi contract.
 * This provides the actual functionality for trade journal operations.
 */
import { TradeJournalApi, TradeJournalEvents, TradeJournalState, Trade, SetupComponents } from '@adhd-trading-dashboard/shared';
export declare class TradeJournalApiImpl implements TradeJournalApi {
    private eventListeners;
    private state;
    /**
     * Get trade data by ID
     */
    getTradeData(id: number): Promise<Trade | null>;
    /**
     * Convert CompleteTradeData to Trade format
     */
    private convertToTradeFormat;
    /**
     * Validate setup components
     */
    validateSetup(setup: SetupComponents): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Get recent trades
     */
    getRecentTrades(limit?: number): Promise<Trade[]>;
    /**
     * Calculate trade metrics
     */
    calculateMetrics(trades: Trade[]): {
        totalTrades: number;
        winRate: number;
        avgPnL: number;
        totalPnL: Trade;
    };
    /**
     * Export trades data
     */
    exportTrades(trades: Trade[], format: 'csv' | 'json'): Promise<string>;
    /**
     * Event management
     */
    addEventListener<K extends keyof TradeJournalEvents>(event: K, listener: TradeJournalEvents[K]): void;
    removeEventListener<K extends keyof TradeJournalEvents>(event: K): void;
    /**
     * State management
     */
    getState(): TradeJournalState;
    private setState;
    /**
     * Emit events
     */
    private emit;
    /**
     * Public methods to emit events (called by other parts of the app)
     */
    emitTradeCreated(trade: Trade): void;
    emitTradeUpdated(trade: Trade): void;
    emitTradeDeleted(tradeId: number): void;
    emitTradesFiltered(trades: Trade[]): void;
}
export declare const tradeJournalApi: TradeJournalApiImpl;
//# sourceMappingURL=TradeJournalApiImpl.d.ts.map