/**
 * Session Analytics Hook
 *
 * Analyzes user's trading performance by session timing using real trade data.
 * Provides personalized insights based on actual trading patterns.
 */
import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
/**
 * Get current EST hour (trading timezone)
 */
const getCurrentESTHour = () => {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const est = new Date(utc + (-5 * 3600000)); // EST is UTC-5
    return est.getHours();
};
/**
 * Get session label for hour
 */
const getSessionLabel = (hour) => {
    if (hour >= 4 && hour < 9)
        return 'Pre-Market';
    if (hour >= 9 && hour < 11)
        return 'NY Open';
    if (hour >= 11 && hour < 14)
        return 'Mid-Day';
    if (hour >= 14 && hour < 16)
        return 'NY Close';
    if (hour >= 16 && hour < 20)
        return 'After Hours';
    return 'Overnight';
};
/**
 * Determine performance level based on win rate and trade count
 */
const getPerformanceLevel = (winRate, tradeCount) => {
    if (tradeCount < 3)
        return 'average'; // Not enough data
    if (winRate >= 70)
        return 'excellent';
    if (winRate >= 60)
        return 'good';
    if (winRate >= 45)
        return 'average';
    if (winRate >= 30)
        return 'poor';
    return 'avoid';
};
/**
 * Get recommendation level based on performance
 */
const getRecommendationLevel = (performance) => {
    switch (performance) {
        case 'excellent': return 'high';
        case 'good': return 'medium';
        case 'average': return 'medium';
        case 'poor': return 'low';
        case 'avoid': return 'avoid';
        default: return 'medium';
    }
};
/**
 * Session Analytics Hook
 */
export const useSessionAnalytics = () => {
    const [trades, setTrades] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch trade data
    useEffect(() => {
        const fetchTrades = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const tradeData = await tradeStorageService.getAllTrades();
                setTrades(tradeData);
            }
            catch (err) {
                console.error('Error fetching trades for session analytics:', err);
                setError('Failed to load trade data');
            }
            finally {
                setIsLoading(false);
            }
        };
        fetchTrades();
    }, []);
    // Calculate session analytics
    const analytics = useMemo(() => {
        if (trades.length === 0) {
            return {
                sessionPerformance: [],
                currentRecommendation: {
                    currentHour: getCurrentESTHour(),
                    sessionLabel: getSessionLabel(getCurrentESTHour()),
                    recommendation: 'medium',
                    winRate: 0,
                    bestSetups: [],
                    riskLevel: 'medium',
                    actionItems: ['Insufficient data for recommendations'],
                },
                bestPerformingHours: [],
                worstPerformingHours: [],
                totalAnalyzedTrades: 0,
                lastUpdated: new Date(),
            };
        }
        // Group trades by hour
        const tradesByHour = {};
        trades.forEach(trade => {
            if (trade.trade.entry_time) {
                const hour = parseInt(trade.trade.entry_time.split(':')[0]);
                if (!tradesByHour[hour]) {
                    tradesByHour[hour] = [];
                }
                tradesByHour[hour].push(trade);
            }
        });
        // Calculate performance for each hour
        const sessionPerformance = [];
        for (let hour = 0; hour < 24; hour++) {
            const hourTrades = tradesByHour[hour] || [];
            const winningTrades = hourTrades.filter(t => t.trade.win_loss === 'Win');
            const winRate = hourTrades.length > 0 ? (winningTrades.length / hourTrades.length) * 100 : 0;
            const avgRMultiple = hourTrades.length > 0
                ? hourTrades.reduce((sum, t) => sum + (t.trade.r_multiple || 0), 0) / hourTrades.length
                : 0;
            const totalPnL = hourTrades.reduce((sum, t) => sum + (t.trade.achieved_pl || 0), 0);
            // Find best setup for this hour
            const setupCounts = {};
            hourTrades.forEach(trade => {
                const setup = trade.trade.model_type || 'Unknown';
                setupCounts[setup] = (setupCounts[setup] || 0) + 1;
            });
            const bestSetup = Object.keys(setupCounts).reduce((a, b) => setupCounts[a] > setupCounts[b] ? a : b, 'None');
            sessionPerformance.push({
                hour,
                label: getSessionLabel(hour),
                winRate,
                totalTrades: hourTrades.length,
                avgRMultiple,
                totalPnL,
                bestSetup,
                performance: getPerformanceLevel(winRate, hourTrades.length),
            });
        }
        // Get current hour recommendation
        const currentHour = getCurrentESTHour();
        const currentSession = sessionPerformance[currentHour];
        // Find best setups for current session
        const currentHourTrades = tradesByHour[currentHour] || [];
        const setupWinRates = {};
        currentHourTrades.forEach(trade => {
            const setup = trade.trade.model_type || 'Unknown';
            if (!setupWinRates[setup]) {
                setupWinRates[setup] = { wins: 0, total: 0 };
            }
            setupWinRates[setup].total++;
            if (trade.trade.win_loss === 'Win') {
                setupWinRates[setup].wins++;
            }
        });
        const bestSetups = Object.entries(setupWinRates)
            .filter(([_, stats]) => stats.total >= 2) // At least 2 trades
            .sort(([_, a], [__, b]) => (b.wins / b.total) - (a.wins / a.total))
            .slice(0, 3)
            .map(([setup]) => setup);
        // Generate action items
        const actionItems = [];
        if (currentSession.performance === 'excellent') {
            actionItems.push('🎯 Prime trading time - consider larger position sizes');
            actionItems.push('📈 Focus on your best setups');
        }
        else if (currentSession.performance === 'good') {
            actionItems.push('✅ Good performance window - trade with confidence');
            actionItems.push('🎯 Stick to proven setups');
        }
        else if (currentSession.performance === 'poor' || currentSession.performance === 'avoid') {
            actionItems.push('⚠️ Low performance period - reduce position sizes');
            actionItems.push('🛡️ Focus on risk management');
            actionItems.push('📚 Consider paper trading or review');
        }
        else {
            actionItems.push('📊 Average performance - trade with standard risk');
            actionItems.push('🎯 Focus on high-probability setups');
        }
        const currentRecommendation = {
            currentHour,
            sessionLabel: getSessionLabel(currentHour),
            recommendation: getRecommendationLevel(currentSession.performance),
            winRate: currentSession.winRate,
            bestSetups,
            riskLevel: currentSession.performance === 'excellent' || currentSession.performance === 'good' ? 'low' :
                currentSession.performance === 'average' ? 'medium' : 'high',
            actionItems,
        };
        // Find best and worst performing hours
        const sortedByPerformance = sessionPerformance
            .filter(s => s.totalTrades >= 3) // Only hours with sufficient data
            .sort((a, b) => b.winRate - a.winRate);
        const bestPerformingHours = sortedByPerformance.slice(0, 3).map(s => s.hour);
        const worstPerformingHours = sortedByPerformance.slice(-3).map(s => s.hour);
        return {
            sessionPerformance,
            currentRecommendation,
            bestPerformingHours,
            worstPerformingHours,
            totalAnalyzedTrades: trades.length,
            lastUpdated: new Date(),
        };
    }, [trades]);
    return {
        analytics,
        isLoading,
        error,
        refresh: () => {
            // Trigger re-fetch
            setTrades([]);
        },
    };
};
//# sourceMappingURL=useSessionAnalytics.js.map