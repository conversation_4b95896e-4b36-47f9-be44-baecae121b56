import { TradingPlanItem } from '../types';
/**
 * Use Daily Guide Hook
 *
 * A custom hook for the daily guide feature.
 *
 * @returns The daily guide state and actions
 */
export declare function useDailyGuide(): {
    selectedDate: any;
    marketOverview: any;
    tradingPlan: any;
    keyPriceLevels: any;
    watchlist: any;
    marketNews: any;
    isLoading: any;
    error: any;
    tradingPlanItems: any;
    tradingPlanCompletion: any;
    marketSentiment: any;
    marketSummary: any;
    lastUpdated: any;
    currentDate: string;
    onDateChange: (date: string) => void;
    onTradingPlanItemToggle: (id: string, completed: boolean) => void;
    onAddTradingPlanItem: (item: TradingPlanItem) => void;
    onRemoveTradingPlanItem: (id: string) => void;
    onRefresh: () => void;
};
//# sourceMappingURL=useDailyGuide.d.ts.map