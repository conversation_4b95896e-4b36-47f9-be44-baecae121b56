import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { useICTActionPlan } from '../hooks/useICTActionPlan';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: 24px;
`;
const SectionTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const PerformanceCard = styled.div `
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;
const PerformanceGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
`;
const PerformanceMetric = styled.div `
  text-align: center;
`;
const MetricValue = styled.div `
  font-size: 24px;
  font-weight: 800;
  color: #ffffff;
`;
const MetricLabel = styled.div `
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
`;
const ModelComparisonCard = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
`;
const ModelGrid = styled.div `
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
`;
const ModelCard = styled.div `
  background: ${({ isRecommended }) => isRecommended ? 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid ${({ isRecommended }) => isRecommended ? 'rgba(220, 38, 38, 0.4)' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
`;
const ModelName = styled.div `
  font-size: 16px;
  font-weight: 700;
  color: ${({ isRecommended }) => isRecommended ? '#dc2626' : '#ffffff'};
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ModelStats = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 8px;
`;
const ModelStat = styled.div `
  font-size: 12px;
  color: #d1d5db;
`;
const RecommendationBadge = styled.div `
  background: ${({ isRecommended }) => isRecommended ? '#dc2626' : '#6b7280'};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: 8px;
`;
const ActionItemsCard = styled.div `
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;
const ActionItemsList = styled.div `
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
const ActionItem = styled.div `
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px 16px;
  border-left: 4px solid ${({ priority }) => {
    switch (priority) {
        case 'HIGH': return '#dc2626';
        case 'MEDIUM': return '#f59e0b';
        default: return '#6b7280';
    }
}};
`;
const ActionText = styled.div `
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
`;
const PriorityBadge = styled.div `
  background: ${({ priority }) => {
    switch (priority) {
        case 'HIGH': return '#dc2626';
        case 'MEDIUM': return '#f59e0b';
        default: return '#6b7280';
    }
}};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;
const RiskManagementCard = styled.div `
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
`;
const RiskGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
`;
const RiskItem = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
`;
const RiskValue = styled.div `
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
`;
const RiskLabel = styled.div `
  font-size: 11px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
`;
const QualityChecklistCard = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
`;
const ChecklistGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
`;
const ChecklistItem = styled.div `
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #d1d5db;
`;
const CheckIcon = styled.div `
  color: #10b981;
  font-weight: bold;
`;
const EmptyState = styled.div `
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
`;
const EmptyIcon = styled.div `
  font-size: 48px;
  margin-bottom: 16px;
`;
const EmptyTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 8px;
`;
const EmptyMessage = styled.p `
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
`;
/**
 * ICT Action Plan Component
 */
export const ICTActionPlan = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { actionPlan, isLoading: planLoading, error: planError } = useICTActionPlan();
    const loading = isLoading || planLoading;
    const displayError = error || planError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: "\uD83D\uDCCB ICT Action Plan", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Generating data-driven ICT action plan..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: "\uD83D\uDCCB ICT Action Plan", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    // Empty state
    if (!actionPlan) {
        return (_jsx(Card, { title: "\uD83D\uDCCB ICT Action Plan", children: _jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\uD83D\uDCCB" }), _jsx(EmptyTitle, { children: "No Trading Data Available" }), _jsx(EmptyMessage, { children: "Import your trading data to generate a personalized ICT action plan based on your actual performance patterns." })] }) }));
    }
    return (_jsx(Card, { title: "\uD83D\uDCCB ICT Action Plan", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA This Week's Performance Analysis" }), _jsx(PerformanceCard, { children: _jsxs(PerformanceGrid, { children: [_jsxs(PerformanceMetric, { children: [_jsx(MetricValue, { children: actionPlan.weeklyPerformance.totalTrades }), _jsx(MetricLabel, { children: "Total Trades" })] }), _jsxs(PerformanceMetric, { children: [_jsxs(MetricValue, { children: [actionPlan.weeklyPerformance.winRate.toFixed(0), "%"] }), _jsx(MetricLabel, { children: "Win Rate" })] }), _jsxs(PerformanceMetric, { children: [_jsxs(MetricValue, { children: [actionPlan.weeklyPerformance.avgRMultiple.toFixed(1), "R"] }), _jsx(MetricLabel, { children: "Avg R-Multiple" })] }), _jsxs(PerformanceMetric, { children: [_jsx(MetricValue, { children: actionPlan.weeklyPerformance.avgQuality.toFixed(1) }), _jsx(MetricLabel, { children: "Avg Quality" })] })] }) })] }), _jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83C\uDFAF Model Selection Strategy" }), _jsxs(ModelComparisonCard, { children: [_jsxs(ModelGrid, { children: [_jsxs(ModelCard, { isRecommended: actionPlan.modelStrategy.recommendation === 'RD-Cont', children: [_jsx(ModelName, { isRecommended: actionPlan.modelStrategy.recommendation === 'RD-Cont', children: "RD-Cont" }), _jsxs(ModelStats, { children: [_jsxs(ModelStat, { children: [actionPlan.modelStrategy.rdCont.trades, " trades"] }), _jsxs(ModelStat, { children: [actionPlan.modelStrategy.rdCont.winRate.toFixed(0), "% win"] })] }), _jsx(RecommendationBadge, { isRecommended: actionPlan.modelStrategy.recommendation === 'RD-Cont', children: actionPlan.modelStrategy.recommendation === 'RD-Cont' ? 'PRIORITIZE' : 'SECONDARY' })] }), _jsxs(ModelCard, { isRecommended: actionPlan.modelStrategy.recommendation === 'FVG-RD', children: [_jsx(ModelName, { isRecommended: actionPlan.modelStrategy.recommendation === 'FVG-RD', children: "FVG-RD" }), _jsxs(ModelStats, { children: [_jsxs(ModelStat, { children: [actionPlan.modelStrategy.fvgRd.trades, " trades"] }), _jsxs(ModelStat, { children: [actionPlan.modelStrategy.fvgRd.winRate.toFixed(0), "% win"] })] }), _jsx(RecommendationBadge, { isRecommended: actionPlan.modelStrategy.recommendation === 'FVG-RD', children: actionPlan.modelStrategy.recommendation === 'FVG-RD' ? 'PRIORITIZE' : 'SECONDARY' })] })] }), _jsxs("div", { style: { textAlign: 'center', marginTop: '16px', color: '#d1d5db', fontSize: '14px' }, children: [_jsx("strong", { children: "Current Recommendation:" }), " ", actionPlan.modelStrategy.reasoning] })] })] }), _jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83D\uDD25 Priority Action Items" }), _jsx(ActionItemsCard, { children: _jsx(ActionItemsList, { children: actionPlan.actionItems.map((item, index) => (_jsxs(ActionItem, { priority: item.priority, children: [_jsx(ActionText, { children: item.description }), _jsx(PriorityBadge, { priority: item.priority, children: item.priority })] }, index))) }) })] }), _jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83D\uDEE1\uFE0F Risk Management (Your Data)" }), _jsx(RiskManagementCard, { children: _jsxs(RiskGrid, { children: [_jsxs(RiskItem, { children: [_jsx(RiskValue, { children: actionPlan.riskManagement.avgRisk.toFixed(0) }), _jsx(RiskLabel, { children: "Avg Risk (Points)" })] }), _jsxs(RiskItem, { children: [_jsxs(RiskValue, { children: [actionPlan.riskManagement.targetRMultiple.toFixed(1), "R"] }), _jsx(RiskLabel, { children: "Target R-Multiple" })] }), _jsxs(RiskItem, { children: [_jsx(RiskValue, { children: actionPlan.riskManagement.positionSizing }), _jsx(RiskLabel, { children: "Position Sizing" })] }), _jsxs(RiskItem, { children: [_jsx(RiskValue, { children: actionPlan.riskManagement.stopLossStrategy }), _jsx(RiskLabel, { children: "Stop Loss" })] })] }) })] }), _jsxs("div", { children: [_jsx(SectionTitle, { children: "\uD83D\uDCDD Quality Control Checklist" }), _jsx(QualityChecklistCard, { children: _jsx(ChecklistGrid, { children: actionPlan.qualityChecklist.map((item, index) => (_jsxs(ChecklistItem, { children: [_jsx(CheckIcon, { children: "\u2705" }), item] }, index))) }) })] })] }) }));
};
//# sourceMappingURL=ICTActionPlan.js.map