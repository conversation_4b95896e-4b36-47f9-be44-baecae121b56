import { DailyGuideState, DailyGuideData, TradingPlanItem, DailyGuidePreferences } from '../types';
export declare enum DailyGuideActionTypes {
    FETCH_DATA_START = "dailyGuide/FETCH_DATA_START",
    FETCH_DATA_SUCCESS = "dailyGuide/FETCH_DATA_SUCCESS",
    FETCH_DATA_ERROR = "dailyGuide/FETCH_DATA_ERROR",
    UPDATE_TRADING_PLAN_ITEM = "dailyGuide/UPDATE_TRADING_PLAN_ITEM",
    ADD_TRADING_PLAN_ITEM = "dailyGuide/ADD_TRADING_PLAN_ITEM",
    REMOVE_TRADING_PLAN_ITEM = "dailyGuide/REMOVE_TRADING_PLAN_ITEM",
    UPDATE_SELECTED_DATE = "dailyGuide/UPDATE_SELECTED_DATE",
    UPDATE_MARKET_OVERVIEW = "dailyGuide/UPDATE_MARKET_OVERVIEW",
    UPDATE_KEY_PRICE_LEVELS = "dailyGuide/UPDATE_KEY_PRICE_LEVELS",
    UPDATE_PREFERENCES = "dailyGuide/UPDATE_PREFERENCES",
    RESET_STATE = "dailyGuide/RESET_STATE"
}
export interface FetchDataStartAction {
    type: DailyGuideActionTypes.FETCH_DATA_START;
}
export interface FetchDataSuccessAction {
    type: DailyGuideActionTypes.FETCH_DATA_SUCCESS;
    payload: DailyGuideData;
}
export interface FetchDataErrorAction {
    type: DailyGuideActionTypes.FETCH_DATA_ERROR;
    payload: string;
}
export interface UpdateTradingPlanItemAction {
    type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM;
    payload: {
        id: string;
        completed: boolean;
    };
}
export interface AddTradingPlanItemAction {
    type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM;
    payload: TradingPlanItem;
}
export interface RemoveTradingPlanItemAction {
    type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM;
    payload: string;
}
export interface UpdateSelectedDateAction {
    type: DailyGuideActionTypes.UPDATE_SELECTED_DATE;
    payload: string;
}
export interface UpdateMarketOverviewAction {
    type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW;
    payload: DailyGuideData['marketOverview'];
}
export interface UpdateKeyPriceLevelsAction {
    type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS;
    payload: DailyGuideData['keyPriceLevels'];
}
export interface UpdatePreferencesAction {
    type: DailyGuideActionTypes.UPDATE_PREFERENCES;
    payload: Partial<DailyGuidePreferences>;
}
export interface ResetStateAction {
    type: DailyGuideActionTypes.RESET_STATE;
}
export type DailyGuideAction = FetchDataStartAction | FetchDataSuccessAction | FetchDataErrorAction | UpdateTradingPlanItemAction | AddTradingPlanItemAction | RemoveTradingPlanItemAction | UpdateSelectedDateAction | UpdateMarketOverviewAction | UpdateKeyPriceLevelsAction | UpdatePreferencesAction | ResetStateAction;
export declare const initialDailyGuideState: DailyGuideState;
export declare const dailyGuideReducer: (state: DailyGuideState, action: DailyGuideAction) => DailyGuideState;
export declare const DailyGuideContext: any, DailyGuideProvider: any, useDailyGuideStore: any, useDailyGuideSelector: any, useDailyGuideAction: any, useDailyGuideActions: any;
export declare const dailyGuideActions: {
    fetchDataStart: () => FetchDataStartAction;
    fetchDataSuccess: (data: DailyGuideData) => FetchDataSuccessAction;
    fetchDataError: (error: string) => FetchDataErrorAction;
    updateTradingPlanItem: (id: string, completed: boolean) => UpdateTradingPlanItemAction;
    addTradingPlanItem: (item: TradingPlanItem) => AddTradingPlanItemAction;
    removeTradingPlanItem: (id: string) => RemoveTradingPlanItemAction;
    updateSelectedDate: (date: string) => UpdateSelectedDateAction;
    updateMarketOverview: (overview: DailyGuideData["marketOverview"]) => UpdateMarketOverviewAction;
    updateKeyPriceLevels: (levels: DailyGuideData["keyPriceLevels"]) => UpdateKeyPriceLevelsAction;
    resetState: () => ResetStateAction;
};
//# sourceMappingURL=dailyGuideState.d.ts.map