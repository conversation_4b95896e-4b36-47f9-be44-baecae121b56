import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const TabsContainer = styled.div `
  display: flex;
  gap: 0;
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0
    ${({ theme }) => theme.spacing?.xl || '32px'} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  position: relative;
`;
const Tab = styled.button `
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '24px'};
  border: none;
  background: transparent;
  color: ${({ $isActive, theme }) => $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textSecondary || '#9ca3af'};
  cursor: ${({ $disabled }) => ($disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;
  font-weight: ${({ $isActive }) => ($isActive ? '600' : '400')};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  position: relative;
  border-bottom: 2px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  /* F1 Racing active indicator */
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    transform: scaleX(${({ $isActive }) => ($isActive ? 1 : 0)});
    transition: transform 0.2s ease;
    transform-origin: center;
  }

  /* F1 Racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ $isActive, theme }) => $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textPrimary || '#ffffff'};
    transform: translateY(-1px);

    &::after {
      transform: scaleX(1);
      background: ${({ $isActive, theme }) => $isActive ? theme.colors?.primary || '#dc2626' : theme.colors?.textSecondary || '#9ca3af'};
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  /* Disabled styling */
  ${({ $disabled }) => $disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}

  /* Mobile responsive */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.sm || '8px'}
      ${({ theme }) => theme.spacing?.md || '12px'};
    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  }
`;
const TabIcon = styled.span `
  font-size: 16px;

  @media (max-width: 768px) {
    font-size: 14px;
  }
`;
const TabLabel = styled.span `
  @media (max-width: 768px) {
    display: none;
  }
`;
/**
 * Tab configuration with icons and labels - ICT-Focused
 */
const TAB_CONFIG = {
    overview: {
        icon: '🕘',
        label: 'Session Focus',
        description: 'Enhanced ICT session intelligence and timing analysis',
    },
    plan: {
        icon: '🧠',
        label: 'Elite Intelligence',
        description: 'Advanced ICT trading intelligence with model selection and pattern scoring',
    },
    levels: {
        icon: '🎯',
        label: 'PD Array Levels',
        description: 'ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis',
    },
    news: {
        icon: '📰',
        label: 'Market News',
        description: 'Latest market news and events',
    },
};
/**
 * F1GuideTabs Component
 *
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 */
export const F1GuideTabs = ({ activeTab, onTabChange, disabled = false, className, }) => {
    const handleTabClick = (tab) => {
        if (!disabled) {
            onTabChange(tab);
        }
    };
    const handleKeyDown = (event, tab) => {
        if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
            event.preventDefault();
            onTabChange(tab);
        }
    };
    return (_jsx(TabsContainer, { className: className, role: "tablist", children: Object.keys(TAB_CONFIG).map((tab) => {
            const config = TAB_CONFIG[tab];
            const isActive = activeTab === tab;
            return (_jsxs(Tab, { "$isActive": isActive, "$disabled": disabled, onClick: () => handleTabClick(tab), onKeyDown: (e) => handleKeyDown(e, tab), disabled: disabled, role: "tab", "aria-selected": isActive, "aria-controls": `guide-panel-${tab}`, tabIndex: disabled ? -1 : 0, title: config.description, children: [_jsx(TabIcon, { children: config.icon }), _jsx(TabLabel, { children: config.label })] }, tab));
        }) }));
};
export default F1GuideTabs;
//# sourceMappingURL=F1GuideTabs.js.map