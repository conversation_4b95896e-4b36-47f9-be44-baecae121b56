{"version": 3, "file": "useModelSelectionEngine.js", "sourceRoot": "", "sources": ["../../../../src/features/daily-guide/hooks/useModelSelectionEngine.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAsCrE;;GAEG;AACH,MAAM,mBAAmB,GAAG,CAAC,MAA2B,EAAmB,EAAE;IAC3E,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,QAAQ,CAAC;IAEvC,qBAAqB;IACrB,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvC,MAAM,UAAU,GAAG,YAAY;SAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;SAC9B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;IAE7D,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,QAAQ,CAAC;IAE3C,kEAAkE;IAClE,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;IACrF,MAAM,QAAQ,GACZ,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;IAC9F,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEnC,6DAA6D;IAC7D,IAAI,MAAM,GAAG,GAAG;QAAE,OAAO,MAAM,CAAC;IAChC,IAAI,MAAM,GAAG,GAAG;QAAE,OAAO,QAAQ,CAAC;IAClC,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG,CAAC,MAA2B,EAAoB,EAAE;IAC/E,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,OAAO,CAAC;IAEtC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7B,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,QAAQ,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC;QAErC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5F,SAAS,EAAE,CAAC;QACd,CAAC;QACD,IACE,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7B,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;YAC9B,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAC1B,CAAC;YACD,aAAa,EAAE,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,SAAS,GAAG,aAAa;QAAE,OAAO,MAAM,CAAC;IAC7C,IAAI,aAAa,GAAG,SAAS;QAAE,OAAO,UAAU,CAAC;IACjD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,MAA2B,EAAqC,EAAE;IAC3F,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,SAAS,CAAC;IAExC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC;IAE7E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,SAAS,CAAC;IAE/C,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;IAClF,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC,MAAM,CAAC;IAEpF,IAAI,QAAQ,GAAG,SAAS,GAAG,GAAG;QAAE,OAAO,SAAS,CAAC;IACjD,IAAI,SAAS,GAAG,QAAQ,GAAG,GAAG;QAAE,OAAO,SAAS,CAAC;IACjD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,yBAAyB,GAAG,CAAC,MAA2B,EAAoB,EAAE;IAClF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,IAAI,CAAC;IAEnC,gCAAgC;IAChC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAE3C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,SAAS,IAAI,SAAS,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,IAAI,CAAC;IAEzC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CACpC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CACtE,CAAC,MAAM,CAAC;IAET,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CACnC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CACrE,CAAC,MAAM,CAAC;IAET,IAAI,UAAU,GAAG,SAAS;QAAE,OAAO,SAAS,CAAC;IAC7C,IAAI,SAAS,GAAG,UAAU;QAAE,OAAO,QAAQ,CAAC;IAC5C,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,GAAG,EAAE;IAC1C,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,mBAAmB;IACnB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC;gBACH,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAC3D,SAAS,CAAC,SAAS,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC;gBACjE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YACxC,CAAC;oBAAS,CAAC;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,yCAAyC;IACzC,MAAM,UAAU,GAA6C,OAAO,CAAC,GAAG,EAAE;QACxE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC;QAE1E,MAAM,cAAc,GAAG,CACrB,WAAgC,EAChC,KAAgB,EACO,EAAE;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;YACvC,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC;YAC5E,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEjF,MAAM,UAAU,GAAG,WAAW;iBAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;iBAC9B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YAC7D,MAAM,YAAY,GAChB,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5F,sCAAsC;YACtC,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;YACjF,MAAM,iBAAiB,GACrB,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEzE,kCAAkC;YAClC,MAAM,qBAAqB,GAAG;gBAC5B,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACvC,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC1C,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;aACzC,CAAC;YAEF,2EAA2E;YAC3E,oEAAoE;YACpE,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/E,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAChF,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;gBAClC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH;gBACE,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,EAAE,KAAc,EAAE;gBAC7C,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,EAAE,QAAiB,EAAE;gBAChD,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,MAAe,EAAE;aAChD,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE;gBACvC,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;gBACxE,MAAM,EAAE,GAAG,SAAS;qBACjB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;qBAC9B,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;gBAE/C,qBAAqB,CAAC,GAAG,CAAC,GAAG;oBAC3B,OAAO,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAI,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACvE,MAAM,EAAE,SAAS,CAAC,MAAM;iBACzB,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,YAAY;gBACZ,iBAAiB;gBACjB,qBAAqB;aACtB,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,cAAc,CAAC,YAAY,EAAE,SAAS,CAAC;YAClD,QAAQ,EAAE,cAAc,CAAC,WAAW,EAAE,QAAQ,CAAC;SAChD,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,4CAA4C;IAC5C,MAAM,cAAc,GAAwB,OAAO,CAAC,GAAG,EAAE;QACvD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO;gBACL,gBAAgB,EAAE,SAAS;gBAC3B,WAAW,EAAE,EAAE;gBACf,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,iEAAiE;gBAC5E,gBAAgB,EAAE,QAAQ;gBAC1B,oBAAoB,EAAE,6BAA6B;gBACnD,gBAAgB,EAAE;oBAChB,UAAU,EAAE,QAAQ;oBACpB,gBAAgB,EAAE,OAAO;oBACzB,QAAQ,EAAE,SAAS;oBACnB,sBAAsB,EAAE,IAAI;iBAC7B;aACF,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAM,UAAU,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,sBAAsB,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEjE,MAAM,gBAAgB,GAAqB;YACzC,UAAU;YACV,gBAAgB;YAChB,QAAQ;YACR,sBAAsB;SACvB,CAAC;QAEF,qCAAqC;QACrC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,wCAAwC;QACvD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,sBAAsB;QACtB,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAC9E,CAAC;aAAM,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;YAChC,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAClE,CAAC;QAED,oBAAoB;QACpB,IAAI,gBAAgB,KAAK,MAAM,EAAE,CAAC;YAChC,KAAK,IAAI,GAAG,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACvE,CAAC;aAAM,IAAI,gBAAgB,KAAK,UAAU,EAAE,CAAC;YAC3C,KAAK,IAAI,GAAG,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACjF,CAAC;QAED,sBAAsB;QACtB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QACrF,CAAC;QAED,+BAA+B;QAC/B,IAAI,sBAAsB,KAAK,SAAS,EAAE,CAAC;YACzC,KAAK,IAAI,GAAG,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC1D,CAAC;aAAM,IAAI,sBAAsB,KAAK,QAAQ,EAAE,CAAC;YAC/C,KAAK,IAAI,GAAG,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACzD,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;QAExC,IAAI,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YAClD,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CACV,oCAAoC,WAAW,CAAC,OAAO,CAAC,OAAO,CAC7D,CAAC,CACF,QAAQ,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC3C,CAAC;QACJ,CAAC;aAAM,IAAI,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACpE,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CACV,qCAAqC,UAAU,CAAC,YAAY,CAAC,OAAO,CAClE,CAAC,CACF,OAAO,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAC/C,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,gBAAgB,GAAc,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;QACrE,MAAM,gBAAgB,GAAc,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;QAErE,uCAAuC;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;QAEpE,IAAI,UAA2B,CAAC;QAChC,IAAI,QAAQ,IAAI,CAAC;YAAE,UAAU,GAAG,MAAM,CAAC;aAClC,IAAI,QAAQ,IAAI,GAAG;YAAE,UAAU,GAAG,QAAQ,CAAC;;YAC3C,UAAU,GAAG,KAAK,CAAC;QAExB,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,oBAAoB,GACxB,gBAAgB,KAAK,QAAQ;YAC3B,CAAC,CAAC,2CAA2C;YAC7C,CAAC,CAAC,2CAA2C,CAAC;QAElD,OAAO;YACL,gBAAgB;YAChB,WAAW;YACX,UAAU;YACV,SAAS;YACT,gBAAgB;YAChB,oBAAoB;YACpB,gBAAgB;SACjB,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;IAEzB,OAAO;QACL,cAAc;QACd,UAAU;QACV,SAAS;QACT,KAAK;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,SAAS,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}