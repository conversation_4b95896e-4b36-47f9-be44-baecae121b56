/**
 * useTradingDashboard Hook
 *
 * Custom hook for fetching and managing trading dashboard data
 */
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { DashboardState } from '../types';
/**
 * useTradingDashboard Hook
 *
 * Fetches and manages trading dashboard data
 */
export declare const useTradingDashboard: () => DashboardState & {
    fetchDashboardData: () => Promise<void>;
    completeTradeData: CompleteTradeData[];
};
export default useTradingDashboard;
//# sourceMappingURL=useTradingDashboard.d.ts.map