/**
 * Dynamic Trading Plan Component
 *
 * Replaces static action items with intelligent, performance-driven recommendations
 * based on user's actual PD Array element performance and combinations.
 */
import React from 'react';
export interface DynamicTradingPlanProps {
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** The error message, if any */
    error?: string | null;
    /** Function called when the refresh button is clicked */
    onRefresh?: () => void;
    /** Additional class name */
    className?: string;
}
/**
 * Dynamic Trading Plan Component
 */
export declare const DynamicTradingPlan: React.FC<DynamicTradingPlanProps>;
//# sourceMappingURL=DynamicTradingPlan.d.ts.map